# BarrioCore PlaceholderAPI Placeholders

This document lists all available placeholders provided by BarrioCore for use with PlaceholderAPI.

## Usage

All placeholders use the format: `%barrio_placeholder_name%`

These placeholders will show actual values when a player is inside a barrio, and configurable default values when outside a barrio.

## Available Placeholders

| Placeholder | Description | Example Value |
|-------------|-------------|---------------|
| `%barrio_nickname%` | The nickname of the barrio | "My Barrio" |
| `%barrio_owner%` | Whether the player is the owner of the barrio | "True" or "False" |
| `%barrio_owner_name%` | The name of the barrio owner | "PlayerName" |
| `%barrio_max_players%` | Maximum number of players allowed in the barrio | "Unlimited" |
| `%barrio_player_perm%` | The player's permission group in the barrio | "VISITOR", "RESIDENT", "TRUSTED", "OWNER" |
| `%barrio_creation_date%` | When the barrio was created | "01/15/2023" |
| `%barrio_mode%` | The removal mode of the barrio | "UNLIMITED", "INACTIVITY", "RENTING" |
| `%barrio_auto_payment%` | Whether automatic payment is enabled | "True" or "False" |
| `%barrio_next_payment_date%` | The next payment date for rented barrios | "02/20/2023" |
| `%barrio_last_visit_date%` | The date of the last visit to the barrio | "02/15/2023" |
| `%barrio_status%` | Visitor access status | "Locked" or "Unlocked" |
| `%barrio_total_ratings%` | Total number of ratings for the barrio | "42" |
| `%barrio_average_ratings%` | Average rating of the barrio (1.0 to 5.0) | "4.5" |

## Gadget Status Placeholders

For gadget status, use the format: `%barrio_gadget_status__<gadget_name>%`

Available gadgets:
- `creeper_damage` - Whether creeper damage to players is enabled
- `creeper_block_damage` - Whether creeper damage to blocks is enabled
- `ghast_damage` - Whether ghast damage to players is enabled
- `ghast_block_damage` - Whether ghast damage to blocks is enabled
- `fire_spread` - Whether fire spread is enabled
- `enderman_grief` - Whether enderman griefing is enabled
- `ravager_grief` - Whether ravager griefing is enabled
- `show_entry_title` - Whether entry titles are shown
- `show_entry_chat` - Whether entry chat messages are shown

Example: `%barrio_gadget_status__fire_spread%` will return "True" or "False"

## Configuration

Default values for these placeholders when used outside a barrio can be configured in `config.yml` under the `placeholders.default_values` section.
