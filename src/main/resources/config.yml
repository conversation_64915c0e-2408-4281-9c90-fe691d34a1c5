database:
  host: localhost
  port: 3306
  name: barrio_db
  user: root
  password: your_password_here

border:
  default: 300
  increment: 25
  max: 400

server_spawn:
  world: world
  x: 0
  y: 100
  z: 0

templates:
  template_world_1:
    mode: UNLIMITED  # UNLIMITED, INACTIVITY, RENTING
    # Settings for INACTIVITY mode
    inactivity:
      group-check: ALL  # ALL, RESIDENT_UP, TRUSTED_UP, OWNER_ONLY
      time: 30d  # Format: #s, #m, #h, #d, #w, #mo, #y
      online-in-world: false  # true = count server login, false = only barrio visits
    # Settings for RENTING mode
    renting:
      time: 7d
      price: 1000.0

  template_world_2:
    mode: INACTIVITY
    inactivity:
      group-check: RESIDENT_UP
      time: 14d
      online-in-world: true

gadgets:
  inactivity_unload_minutes: 5

upgrades:
  inactivity_unload_minutes: 5

ranks:
  inactivity_unload_minutes: 5
  save_interval_minutes: 5

player_data:
  inactivity_unload_minutes: 5
  save_interval_minutes: 5

world_settings:
  inactivity_unload_minutes: 5
  save_interval_minutes: 5

rating:
  min_rating: 1
  max_rating: 5
  cooldown_hours: 24  # How long a player must wait before rating the same barrio again

create:
  cooldown_seconds: 0  # Set to 0 to disable cooldown, or any positive number for cooldown in seconds

barrio:
  save_interval_minutes: 5

permissions:
  save_interval: 300

placeholders:
  # The value to show when a player is not in a barrio
  not_in_barrio: "N/A"

  # Default values for placeholders when outside a barrio
  # These values will be shown when using placeholders outside of a barrio
  # All placeholders will work inside barrios and show actual values
  # Format: %barrio_placeholder_name%
  default_values:
    barrio_owner: "False"
    barrio_owner_name: "N/A"
    barrio_max_players: "0"
    barrio_player_perm: "N/A"
    barrio_creation_date: "N/A"
    barrio_mode: "N/A"
    barrio_auto_payment: "False"
    barrio_next_payment_date: "N/A"
    barrio_last_visit_date: "N/A"
    barrio_status: "N/A"
    # For gadget status, use %barrio_gadget_status__<gadget_name>%
    # Available gadgets: creeper_damage, creeper_block_damage, ghast_damage, ghast_block_damage,
    # fire_spread, enderman_grief, ravager_grief, show_entry_title, show_entry_chat
    barrio_gadget_status: "False"
    barrio_total_ratings: "0"
    barrio_average_ratings: "0.0"

debug:
  enabled: false

player_heads:
  save_interval_minutes: 5

default_permissions:
  visit_toggle: true
  permission_toggle: false
  block_place: false
  block_break: false
  storage_access: false
  bucket_use: false
  interaction: false
  entity_interact: false
  item_handling: false
  hostile_pve: true
  passive_pve: false
  pvp: false
  entity_damage: false
  gadget_change_toggle: false