barrio_type_selection:
  title: "&aSelect Barrio Type"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1, 2, 3, 4, 5, 6, 7, 8]
  back_button:
    material: BARRIER
    name: "&cBack to Main Menu"
    lore:
      - "&7<PERSON>lick to return to main menu"
    slots: [0]
  regular_button:
    material: LIME_WOOL
    name: "&aRegular Barrio"
    custom_model_data: 1
    lore:
      - "&7Create a regular barrio with"
      - "&7normal, nether, and end worlds"
      - ""
      - "&eClick to select"
    slots: [37, 38, 39, 46, 47, 48]
  template_button:
    material: LIGHT_BLUE_WOOL
    name: "&bTemplate Barrio"
    custom_model_data: 2
    lore:
      - "&7Create a barrio from a"
      - "&7pre-made template world"
      - ""
      - "&eClick to select"
    slots: [41, 42, 43, 50, 51, 52]

barrio_create_regular:
  title: "&aCreate Barrio"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1, 2, 3, 4, 5, 6, 7, 8]
  back_button:
    material: BARRIER
    name: "&cBack to Type Selection"
    lore:
      - "&7<PERSON>lick to return to type selection"
    slots: [0]
  create_button:
    material: LIME_WOOL
    name: "&aCreate Barrio"
    lore:
      - "&7Click to create your barrio"
    slots: [38, 39, 40, 41, 42]

barrio_create_template:
  title: "&aCreate Barrio From a Template"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1, 2, 3, 4, 5, 6, 7, 8]
  back_button:
    material: BARRIER
    name: "&cBack to Type Selection"
    lore:
      - "&7Click to return to type selection"
    slots: [0]
  status_indicator:
    material: BLACK_STAINED_GLASS_PANE
    no_selection_name: "&cNo template selected"
    selection_name: "&aSelected: &f%template%"
    lore:
      - "&7Choose a template below"
    slots: [1, 2, 3, 4, 5, 6, 7, 8]
  template_world_1:
    material: LIME_WOOL
    name: "&aChoose Template World1"
    custom_model_data: 100
    lore:
      - "&7Click to choose this template"
    slots: [38]
    on_click:
      custom_mode_data: 101
      new_title: "&aCreate Barrio From a Template [World1]"
  template_world_2:
    material: LIME_WOOL
    name: "&aChoose Template World2"
    custom_model_data: 200
    lore:
      - "&7Click to choose this template"
    slots: [39]
    on_click:
      custom_mode_data: 201
      new_title: "&aCreate Barrio From a Template [World2]"
  create_button:
    material: EMERALD_BLOCK
    name: "&aCreate Barrio from Template"
    lore:
      - "&7Click to create your barrio"
      - "&7using the selected template"
    slots: [49]
# Add this whole section to gui.yml
barrio_gadgets_gui:
  title: "&2Barrio Gadgets"
  size: 27 # Or 36, 45, 54 if more items needed
  decoration: # Optional: Add background panes
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1,2,3,5,6,7,8, 9,17, 18,20,21,22,23,24,25,26]
  back_button:
    material: BARRIER
    name: "&cBack to Settings"
    lore:
      - "&7Click to return to settings menu"
    slots: [0]
  creeper_toggle:
    material: CREEPER_HEAD
    name: "&eCreeper Player Damage"
    lore:
      - "&7Toggles creeper damage to players."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [10]
  creeper_block_toggle:
    material: TNT
    name: "&eCreeper Block Damage"
    lore:
      - "&7Toggles creeper damage to blocks."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [19]
  ghast_toggle:
    material: GHAST_TEAR
    name: "&eGhast Player Damage"
    lore:
      - "&7Toggles ghast fireball damage to players."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [11]
  ghast_block_toggle:
    material: FIRE_CHARGE
    name: "&eGhast Block Damage"
    lore:
      - "&7Toggles ghast fireball damage to blocks."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [20]
  fire_toggle:
    material: CAMPFIRE
    name: "&eFire Spread"
    lore:
      - "&7Toggles natural fire spread/burn."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [12]
  enderman_toggle:
    material: ENDER_EYE
    name: "&eEnderman Griefing"
    lore:
      - "&7Toggles enderman picking up/placing blocks."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [13]
  ravager_toggle:
    material: RAVAGER_SPAWN_EGG
    name: "&eRavager Griefing"
    lore:
      - "&7Toggles ravager breaking leaf/crop blocks."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [14]
  title_message:
    material: YELLOW_WOOL
    name: "&eToggle Entry Title"
    lore:
      - "&7Toggles the title shown when players enter."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [15]
  chat_message:
    material: LIME_WOOL
    name: "&eToggle Entry Chat Message"
    lore:
      - "&7Toggles the chat message sent when players enter."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [16]
  expand_border:
    material: IRON_BARS
    name: "&eExpand Border"
    lore:
      - "&7Increases the size of your barrio's border."
      - ""
      - "&eClick to expand"
    slots: [4]


barrio_permissions_gui:
  title: "&2Visitor Permissions"
  size: 27
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1,2,3,4,5,6,7,8, 9,17, 18,19]
  back_button:
    material: BARRIER
    name: "&cBack to Settings"
    lore:
      - "&7Click to return to settings menu"
    slots: [0]
  visit_toggle:
    material: IRON_DOOR
    name: "&eVisitor Access"
    lore:
      - "&7Allows visitors to teleport to barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [10]
    default: true
  permission_toggle:
    material: REDSTONE
    name: "&ePermission Management"
    lore:
      - "&7Allows changing permissions"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [11]
    default: false
  block_place:
    material: GRASS_BLOCK
    name: "&eBlock Placement"
    lore:
      - "&7Allows placing blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [12]
    default: false
  block_break:
    material: DIAMOND_PICKAXE
    name: "&eBlock Breaking"
    lore:
      - "&7Allows breaking blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [13]
    default: false
  storage_access:
    material: RED_STAINED_GLASS_PANE
    name: "&eStorage Access"
    lore:
      - "&7Allows chest/storage access"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [14]
    default: false
  bucket_use:
    material: BUCKET
    name: "&eBucket Usage"
    lore:
      - "&7Allows using buckets"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [15]
    default: false
  interaction:
    material: STICK
    name: "&eBlock Interaction"
    lore:
      - "&7Allows interacting with doors,"
      - "&7furnaces, and other blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [16]
    default: false
  gadget_change_toggle:
    material: CRAFTING_TABLE
    name: "&eGadget Access"
    lore:
      - "&7Allows opening the Gadgets GUI"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [ 20 ]
    default: false
  entity_interact:
    material: ARMOR_STAND
    name: "&eEntity Interaction"
    lore:
      - "&7Allows interacting with boats,"
      - "&7armor stands, item frames"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [21]
    default: false
  item_handling:
    material: DIAMOND
    name: "&eItem Handling"
    lore:
      - "&7Allows dropping and"
      - "&7picking up items"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [22]
    default: false
  hostile_pve:
    material: ZOMBIE_HEAD
    name: "&eHostile PvE"
    lore:
      - "&7Allows fighting hostile mobs"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [23]
    default: true
  passive_pve:
    material: PORKCHOP
    name: "&ePassive PvE"
    lore:
      - "&7Allows fighting passive mobs"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [24]
    default: false
  pvp:
    material: DIAMOND_SWORD
    name: "&ePvP"
    lore:
      - "&7Allows PvP combat"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [25]
    default: false
  entity_damage:
    material: ITEM_FRAME
    name: "&eEntity Damage"
    lore:
      - "&7Allows damaging boats,"
      - "&7armor stands, item frames"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [26]
    default: false

barrio_resident_permissions_gui:
  title: "&2Resident Permissions"
  size: 27
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [0,1,2,3,4,5,6,7,8, 9,17, 18,19]
  back_button:
    material: BARRIER
    name: "&cBack to Settings"
    lore:
      - "&7Click to return to settings menu"
    slots: [0]
  visit_toggle:
    material: IRON_DOOR
    name: "&eVisitor Access"
    lore:
      - "&7Allows visitors to teleport to barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [10]
  permission_toggle:
    material: REDSTONE
    name: "&ePermission Management"
    lore:
      - "&7Allows changing permissions"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [11]
  block_place:
    material: GRASS_BLOCK
    name: "&eBlock Placement"
    lore:
      - "&7Allows placing blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [12]
  block_break:
    material: DIAMOND_PICKAXE
    name: "&eBlock Breaking"
    lore:
      - "&7Allows breaking blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [13]
  storage_access:
    material: RED_STAINED_GLASS_PANE
    name: "&eStorage Access"
    lore:
      - "&7Allows chest/storage access"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [14]
  bucket_use:
    material: BUCKET
    name: "&eBucket Usage"
    lore:
      - "&7Allows using buckets"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [15]
  interaction:
    material: STICK
    name: "&eBlock Interaction"
    lore:
      - "&7Allows interacting with doors,"
      - "&7furnaces, and other blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [16]
  gadget_change_toggle:
    material: CRAFTING_TABLE
    name: "&eGadget Access"
    lore:
      - "&7Allows opening the Gadgets GUI"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [ 20 ]
  entity_interact:
    material: ARMOR_STAND
    name: "&eEntity Interaction"
    lore:
      - "&7Allows interacting with boats,"
      - "&7armor stands, item frames"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [21]
  item_handling:
    material: DIAMOND
    name: "&eItem Handling"
    lore:
      - "&7Allows dropping and"
      - "&7picking up items"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [22]
  hostile_pve:
    material: ZOMBIE_HEAD
    name: "&eHostile PvE"
    lore:
      - "&7Allows fighting hostile mobs"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [23]
  passive_pve:
    material: PORKCHOP
    name: "&ePassive PvE"
    lore:
      - "&7Allows fighting passive mobs"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [24]
  pvp:
    material: DIAMOND_SWORD
    name: "&ePvP"
    lore:
      - "&7Allows PvP combat"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [25]
  entity_damage:
    material: ITEM_FRAME
    name: "&eEntity Damage"
    lore:
      - "&7Allows damaging boats,"
      - "&7armor stands, item frames"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [26]

barrio_trusted_permissions_gui:
  title: "&2Trusted Permissions"
  size: 27
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1,2,3,4,5,6,7,8, 9,17, 18,19]
  back_button:
    material: BARRIER
    name: "&cBack to Settings"
    lore:
      - "&7Click to return to settings menu"
    slots: [0]
  moderation_access:
    material: IRON_AXE
    name: "&eModeration Access"
    lore:
      - "&7Allows kicking, banning, and"
      - "&7unbanning players in the barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [10]
  settings_access:
    material: COMPARATOR
    name: "&eSettings Access"
    lore:
      - "&7Allows access to the settings"
      - "&7menu for the barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [11]
  visit_toggle:
    material: IRON_DOOR
    name: "&eVisitor Access"
    lore:
      - "&7Allows visitors to teleport to barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [12]
  permission_toggle:
    material: REDSTONE
    name: "&ePermission Management"
    lore:
      - "&7Allows changing permissions"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [13]
  block_place:
    material: GRASS_BLOCK
    name: "&eBlock Placement"
    lore:
      - "&7Allows placing blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [14]
  block_break:
    material: DIAMOND_PICKAXE
    name: "&eBlock Breaking"
    lore:
      - "&7Allows breaking blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [15]
  storage_access:
    material: RED_STAINED_GLASS_PANE
    name: "&eStorage Access"
    lore:
      - "&7Allows chest/storage access"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [16]
  bucket_use:
    material: BUCKET
    name: "&eBucket Usage"
    lore:
      - "&7Allows using buckets"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [19]
  interaction:
    material: STICK
    name: "&eBlock Interaction"
    lore:
      - "&7Allows interacting with doors,"
      - "&7furnaces, and other blocks"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [20]
  gadget_change_toggle:
    material: CRAFTING_TABLE
    name: "&eGadget Access"
    lore:
      - "&7Allows opening the Gadgets GUI"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [21]
  entity_interact:
    material: ARMOR_STAND
    name: "&eEntity Interaction"
    lore:
      - "&7Allows interacting with boats,"
      - "&7armor stands, item frames"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [22]
  item_handling:
    material: DIAMOND
    name: "&eItem Handling"
    lore:
      - "&7Allows dropping and"
      - "&7picking up items"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [23]
  hostile_pve:
    material: ZOMBIE_HEAD
    name: "&eHostile PvE"
    lore:
      - "&7Allows fighting hostile mobs"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [24]
  passive_pve:
    material: PORKCHOP
    name: "&ePassive PvE"
    lore:
      - "&7Allows fighting passive mobs"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [25]
  pvp:
    material: DIAMOND_SWORD
    name: "&ePvP"
    lore:
      - "&7Allows PvP combat"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [26]
  entity_damage:
    material: ITEM_FRAME
    name: "&eEntity Damage"
    lore:
      - "&7Allows damaging boats,"
      - "&7armor stands, item frames"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [27]

barrio_main_gui:
  title: "&6Barrio Main Menu &7(Page %page%)"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 51, 53]
  home_button:
    material: LIGHT_BLUE_WOOL
    name: "&bReturn to Home"
    lore:
      - "&7Teleport to your first barrio's spawn point"
      - ""
      - "&eClick to teleport"
    slots: [49]
  filter_button:
    material: GREEN_WOOL
    name: "&aFilter: &f%filter%"
    lore:
      - "&7Current filter: &f%filter%"
      - ""
      - "&7Click to toggle between:"
      - "&7- All Barrios"
      - "&7- Only Barrios You Are Member In"
    filter_all: "All Barrios"
    filter_member: "Only Barrios You Are Member In"
    slots: [4]
  settings_button:
    material: RED_WOOL
    name: "&cBarrio Settings"
    lore:
      - "&7Open the settings menu for your barrio"
      - ""
      - "&eClick to open"
    slots: [50]
  ratings_button:
    material: GOLD_BLOCK
    name: "&6Barrio Ratings"
    lore:
      - "&7View and manage ratings for barrios"
      - ""
      - "&eClick to open"
    slots: [52]
  top_rated_button:
    material: YELLOW_WOOL
    name: "&eVisit Top-Rated Barrio"
    lore:
      - "&7Teleport to the highest rated barrio"
      - ""
      - "&eClick to visit"
    slots: [3]
  prev_button:
    material: ORANGE_WOOL
    name: "&6Previous Page"
    lore:
      - "&7Click to go to the previous page"
    slots: [45]
  next_button:
    material: ORANGE_WOOL
    name: "&6Next Page"
    lore:
      - "&7Click to go to the next page"
    slots: [53]
  barrio_head:
    material: PLAYER_HEAD
    name: "&e%barrio_name%"
    lore:
      - "&7Owner: &f%owner_name%"
      - "&7ID: &f%barrio_id%"
      - "&7Your Rank: &f%player_rank%"
      - ""
      - "&eClick to visit this barrio"
    slots: [10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43]

barrio_members_list:
  title: "&6Barrio Members &7(Page %page%)"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 50, 51, 52, 53]
  back_button:
    material: BARRIER
    name: "&cBack to Settings"
    lore:
      - "&7Click to return to settings menu"
    slots: [0]
  player_head:
    material: PLAYER_HEAD
    name: "&e%player_name%"
    lore:
      - "&7Player: &f%player_name%"
      - "&7Rank: &f%player_rank%"
      - "&7Owner: &f%is_owner%"
      - "&7Banned: &f%player_banned_date%"
      - ""
      - "&eClick to manage this player"
    not_banned_text: "N/A"
    slots: [10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43]
  prev_button:
    material: ORANGE_WOOL
    name: "&6Previous Page"
    lore:
      - "&7Click to go to the previous page"
    slots: [45]
  next_button:
    material: ORANGE_WOOL
    name: "&6Next Page"
    lore:
      - "&7Click to go to the next page"
    slots: [53]
  filter_button:
    material: LIME_WOOL
    name: "&aFilter: &f%filter%"
    lore:
      - "&7Current filter: &f%filter%"
      - ""
      - "&7Click to cycle through filters:"
      - "&7- All Players"
      - "&7- Banned Players"
      - "&7- Trusted Players"
      - "&7- Resident Players"
      - "&7- Visitors"
    filter_all: "All Players"
    filter_banned: "Banned Players"
    filter_trusted: "Trusted Players"
    filter_residents: "Resident Players"
    filter_visitors: "Visitors"
    slots: [49]

barrio_member_manage:
  title: "&6Managing: %player%"
  size: 45
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [1, 2, 3, 4, 5, 6, 7, 8]
  back_button:
    material: BARRIER
    name: "&cBack to Members List"
    lore:
      - "&7Click to return to members list"
    slots: [0]
  player_head:
    material: PLAYER_HEAD
    name: "&e%player_name%"
    lore:
      - "&7Player: &f%player_name%"
      - "&7Rank: &f%player_rank%"
      - "&7Owner: &f%is_owner%"
      - "&7Banned: &f%player_banned_date%"
      - ""
      - "&eClick to manage this player"
    not_banned_text: "N/A"
    slots: [19]
  visitor_button:
    material: GRAY_WOOL
    name: "&7Set as Visitor"
    lore:
      - "&7Click to set player as visitor"
      - "&7Basic access only"
    slots: [12, 13, 14]
  resident_button:
    material: LIME_WOOL
    name: "&aSet as Resident"
    lore:
      - "&7Click to set player as resident"
      - "&7Advanced access and building rights"
    slots: [21, 22, 23]
  trusted_button:
    material: LIGHT_BLUE_WOOL
    name: "&bSet as Trusted"
    lore:
      - "&7Click to set player as trusted"
      - "&7Full access and management rights"
    slots: [30, 31, 32]
  ban_button:
    material: RED_WOOL
    name: "&cBan Player"
    lore:
      - "&7Click to ban this player"
      - "&7from your barrio"
    slots: [25]

barrio_world_settings_gui:
  title: "&2Player World Settings"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 50, 51, 52, 53]
  back_button:
    material: BARRIER
    name: "&cBack to Main Menu"
    lore:
      - "&7Click to return to the main menu"
    slots: [49]
  guest_permissions:
    material: GRAY_DYE
    name: "&7Open Guest Permission GUI"
    lore:
      - "&7Click to open the visitor"
      - "&7permissions menu"
      - ""
      - "&eClick to open"
    slots: [10]
  resident_permissions:
    material: GREEN_DYE
    name: "&aOpen Resident Permission GUI"
    lore:
      - "&7Click to open the resident"
      - "&7permissions menu"
      - ""
      - "&eClick to open"
    slots: [11]
  trusted_permissions:
    material: RED_DYE
    name: "&cOpen Trusted Permission GUI"
    lore:
      - "&7Click to open the trusted"
      - "&7permissions menu"
      - ""
      - "&eClick to open"
    slots: [12]
  player_head:
    material: PLAYER_HEAD
    name: "&eBarrio Members"
    lore:
      - "&7Click to view and manage"
      - "&7all members of this barrio"
      - ""
      - "&eClick to open"
    slots: [13]
  rent_toggle:
    material: PAPER
    name: "&eRent Payment Toggle"
    lore:
      - "&7Toggle rent collection on/off"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [14]
  rent_extend:
    material: PAPER
    name: "&eExtend Rent"
    lore:
      - "&7Extend your barrio rent period"
      - "&7by paying the rent price"
      - ""
      - "&eClick to extend"
    slots: [15]
  nickname_change:
    material: PAPER
    name: "&eChange Barrio Nickname"
    lore:
      - "&7Change the display name"
      - "&7of your barrio"
      - ""
      - "&7Current: %status%"
      - ""
      - "&eClick to change"
    slots: [16]
  delete_barrio:
    material: BARRIER
    name: "&4DELETE BARRIO"
    lore:
      - "&cWARNING: This will permanently"
      - "&cdelete your barrio and all its"
      - "&ccontents!"
      - ""
      - "&eClick to delete"
    slots: [22]
  pvp_toggle:
    material: DIAMOND_SWORD
    name: "&ePVP Toggle"
    lore:
      - "&7Toggle PVP combat in your barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [28]
  hunger_toggle:
    material: COOKED_BEEF
    name: "&eHunger Loss Toggle"
    lore:
      - "&7Toggle hunger loss in your barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [29]
  time_toggle:
    material: CLOCK
    name: "&eChange Time"
    lore:
      - "&7Cycle through time settings:"
      - "&7Morning, Noon, Night, Midnight, Off"
      - ""
      - "&7Current: %status%"
      - ""
      - "&eClick to change"
    slots: [30]
  weather_toggle:
    material: IRON_PICKAXE
    name: "&eChange Weather"
    lore:
      - "&7Cycle through weather settings:"
      - "&7Clear, Storm, Rain, Off"
      - ""
      - "&7Current: %status%"
      - ""
      - "&eClick to change"
    slots: [31]
  difficulty_toggle:
    material: GOLDEN_PICKAXE
    name: "&eChange Difficulty"
    lore:
      - "&7Cycle through difficulty settings:"
      - "&7Peaceful, Easy, Normal, Hard"
      - ""
      - "&7Current: %status%"
      - ""
      - "&eClick to change"
    slots: [32]
  use_spawn_toggle:
    material: ENDER_PEARL
    name: "&eUse Spawn Toggle"
    lore:
      - "&7If disabled, players will return"
      - "&7to their last location in barrio"
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [33]
  respawn_location_toggle:
    material: COMPASS
    name: "&eRespawn Location Toggle"
    lore:
      - "&7Toggle whether players respawn"
      - "&7at server spawn or barrio spawn"
      - ""
      - "&7Current: %status%"
      - ""
      - "&eClick to toggle"
    slots: [34]
  barrio_chat_toggle:
    material: WRITABLE_BOOK
    name: "&eToggle Barrio Chat Only"
    lore:
      - "&7When enabled, only players in your barrio"
      - "&7will see chat messages from barrio members,"
      - "&7and barrio members will only see messages"
      - "&7from other players in the barrio."
      - ""
      - "&7Status: %status%"
      - ""
      - "&eClick to toggle"
    slots: [40]
  upgrades_button:
    material: DIAMOND
    name: "&bBarrio Upgrades"
    lore:
      - "&7Manage block and entity limits"
      - "&7in your barrio"
      - ""
      - "&7Purchase upgrades to increase"
      - "&7the number of blocks and entities"
      - "&7you can have in your barrio"
      - ""
      - "&eClick to open!"
    slots: [16]
barrio_rating_gui:
  title: "&6Rate &e%barrio_name%"
  size: 27
  decoration:
    1:
      material: RED_STAINED_GLASS_PANE
      name: "&c♥"
      slots: [8, 18, 26]
    2:
      material: GRAY_STAINED_GLASS_PANE
      name: "&7"
      slots: [1, 7, 9, 17]
  back_button:
    material: BARRIER
    name: "&cBack to Rating Info"
    lore:
      - "&7Click to return to rating info"
    slots: [0]
  barrio_head:
    material: PLAYER_HEAD
    name: "&6%barrio_name%'s Rating Info"
    lore:
      - "&eAverage Rating: &f%average%"
      - "&eTotal Ratings: &f%count%"
      - "&eHighest Rating: &f%highest%"
      - "&eLowest Rating: &f%lowest%"
    slots: [4]
  star:
    selected_material: GOLD_BLOCK
    unselected_material: IRON_BLOCK
    selected_model_data: 1
    unselected_model_data: 2
    name: "&e%number% Stars"
    lore:
      - "&7Click to select %number% stars"
    slots: [20, 21, 22, 23, 24]
  message:
    material: PAPER
    name: "&eEdit Message"
    lore:
      - "&7Current message:"
      - "&7%message%"
      - ""
      - "&eClick to edit!"
    slots: [10]
  submit:
    material: EMERALD
    name: "&aSubmit Rating"
    lore:
      - "&7Click to submit your rating"
      - "&7for %barrio_name%!"
    slots: [16]

# Upgrades GUI configurations
barrio_upgrades_main:
  title: "&6Barrio Upgrades"
  size: 27
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]
  back_button:
    material: BARRIER
    name: "&cBack to Settings"
    lore:
      - "&7Click to return to settings menu"
    slots: [0]
  block_upgrades_button:
    material: CRAFTING_TABLE
    name: "&aBlock Upgrades"
    lore:
      - "&7Manage block placement limits"
      - "&7in your barrio"
      - ""
      - "&eClick to open!"
    slots: [11]
  entity_upgrades_button:
    material: COW_SPAWN_EGG
    name: "&bEntity Upgrades"
    lore:
      - "&7Manage entity spawn limits"
      - "&7in your barrio"
      - ""
      - "&eClick to open!"
    slots: [15]

barrio_block_upgrades:
  title: "&aBlock Upgrades"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53]
  back_button:
    material: BARRIER
    name: "&cBack to Upgrades"
    lore:
      - "&7Click to return to upgrades menu"
    slots: [0]
  crafting_table_upgrade:
    material: CRAFTING_TABLE
    name: "&aCrafting Table Limit"
    lore:
      - "&7Increase the number of crafting tables"
      - "&7you can place in your barrio"
    slots: [10]
    levels:
      "1":
        limit: 5
        cost: 1000.0
      "2":
        limit: 10
        cost: 2000.0
      "3":
        limit: 20
        cost: 5000.0
  furnace_upgrade:
    material: FURNACE
    name: "&6Furnace Limit"
    lore:
      - "&7Increase the number of furnaces"
      - "&7you can place in your barrio"
    slots: [11]
    levels:
      "1":
        limit: 5
        cost: 1500.0
      "2":
        limit: 10
        cost: 3000.0
      "3":
        limit: 20
        cost: 6000.0
  chest_upgrade:
    material: CHEST
    name: "&eChest Limit"
    lore:
      - "&7Increase the number of chests"
      - "&7you can place in your barrio"
    slots: [12]
    levels:
      "1":
        limit: 10
        cost: 800.0
      "2":
        limit: 25
        cost: 1600.0
      "3":
        limit: 50
        cost: 4000.0
  enchanting_table_upgrade:
    material: ENCHANTING_TABLE
    name: "&dEnchanting Table Limit"
    lore:
      - "&7Increase the number of enchanting tables"
      - "&7you can place in your barrio"
    slots: [13]
    levels:
      "1":
        limit: 2
        cost: 3000.0
      "2":
        limit: 5
        cost: 6000.0
      "3":
        limit: 10
        cost: 12000.0

barrio_entity_upgrades:
  title: "&bEntity Upgrades"
  size: 54
  decoration:
    glass:
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53]
  back_button:
    material: BARRIER
    name: "&cBack to Upgrades"
    lore:
      - "&7Click to return to upgrades menu"
    slots: [0]
  cow_upgrade:
    material: LEATHER
    name: "&6Cow Limit"
    lore:
      - "&7Increase the number of cows"
      - "&7that can spawn in your barrio"
    slots: [10]
    entity_type: "COW"
    levels:
      "1":
        limit: 10
        cost: 1200.0
      "2":
        limit: 20
        cost: 2400.0
      "3":
        limit: 40
        cost: 6000.0
  pig_upgrade:
    material: PORKCHOP
    name: "&dPig Limit"
    lore:
      - "&7Increase the number of pigs"
      - "&7that can spawn in your barrio"
    slots: [11]
    entity_type: "PIG"
    levels:
      "1":
        limit: 10
        cost: 1000.0
      "2":
        limit: 20
        cost: 2000.0
      "3":
        limit: 40
        cost: 5000.0
  chicken_upgrade:
    material: FEATHER
    name: "&eChicken Limit"
    lore:
      - "&7Increase the number of chickens"
      - "&7that can spawn in your barrio"
    slots: [12]
    entity_type: "CHICKEN"
    levels:
      "1":
        limit: 15
        cost: 800.0
      "2":
        limit: 30
        cost: 1600.0
      "3":
        limit: 60
        cost: 4000.0
  villager_upgrade:
    material: EMERALD
    name: "&aVillager Limit"
    lore:
      - "&7Increase the number of villagers"
      - "&7that can spawn in your barrio"
    slots: [13]
    entity_type: "VILLAGER"
    levels:
      "1":
        limit: 5
        cost: 5000.0
      "2":
        limit: 10
        cost: 10000.0
      "3":
        limit: 20
        cost: 25000.0

barrio_rating_info_gui:
  title: "&6Ratings for %barrio_name%"
  size: 54
  decoration:
    1:
      material: GRAY_STAINED_GLASS_PANE
      name: "&7"
      slots: [1, 2, 3, 5, 6, 7, 8, 45, 46, 47, 48, 50, 51, 52, 53]
  back_button:
    material: BARRIER
    name: "&cBack to Main Menu"
    lore:
      - "&7Click to return to main menu"
    slots: [0]
  barrio_head:
    material: PLAYER_HEAD
    name: "&6%barrio_name%'s Rating Info"
    lore:
      - "&eAverage Rating: &f%average%"
      - "&eTotal Ratings: &f%count%"
      - "&eHighest Rating: &f%highest%"
      - "&eLowest Rating: &f%lowest%"
    slots: [4]
  review:
    material: PAPER
    name: "&eReview from %reviewer%"
    lore:
      - "&7Date: &f%date%"
      - "&7Rating: %stars%"
      - "&7Message:"
      - "&f%message%"
    star_format:
      1: "&6★&7★★★★"
      2: "&6★★&7★★★"
      3: "&6★★★&7★★"
      4: "&6★★★★&7★"
      5: "&6★★★★★"
    slots: [19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43]
  prev_button:
    material: ARROW
    name: "&aPrevious Page"
    lore:
      - "&7Click to view previous page"
    slots: [46]
  next_button:
    material: ARROW
    name: "&aNext Page"
    lore:
      - "&7Click to view next page"
    slots: [52]
  rate_button:
    material: EMERALD
    name: "&aRate %barrio_name%"
    lore:
      - "&7Click to rate this barrio!"
    slots: [49]
