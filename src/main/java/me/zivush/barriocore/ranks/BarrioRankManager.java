package me.zivush.barriocore.ranks;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.entity.Player;
import java.sql.SQLException;
import java.util.UUID;
import java.util.logging.Level;

public class BarrioRankManager {

    private final BarrioCore plugin;
    private final Database database;
    private final BarrioPlayerDataManager playerDataManager;

    public BarrioRankManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();
        this.playerDataManager = plugin.getPlayerDataManager();
        plugin.getLogger().info("BarrioRankManager initialized.");
    }

    public void initialize() {
        plugin.getLogger().info("BarrioRankManager initialized - using player data for ranks.");
    }

    // --- Core Rank Operations ---

    /**
     * Gets the rank of a player in a specific barrio.
     * Uses the player data manager to get the rank.
     *
     * @param barrioId   The ID of the barrio.
     * @param playerUuid The UUID of the player.
     * @return The Rank of the player (defaults to VISITOR).
     */
    public Rank getRank(String barrioId, UUID playerUuid) {
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("Attempted to get rank with null barrioId or playerUuid.");
            return Rank.VISITOR;
        }

        // Use the player data manager to get the rank
        return playerDataManager.getRank(barrioId, playerUuid);
    }

    /**
     * Sets the rank of a player in a specific barrio.
     * Uses the player data manager to set the rank.
     * Cannot set OWNER rank directly (handled by barrio ownership).
     *
     * @param barrioId   The ID of the barrio.
     * @param playerUuid The UUID of the player.
     * @param rank       The Rank to set (TRUSTED or RESIDENT). Use Rank.VISITOR to remove rank.
     * @return true if the rank was set/removed successfully, false otherwise (e.g., trying to set OWNER).
     */
    public boolean setRank(String barrioId, UUID playerUuid, Rank rank) {
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("Attempted to set rank with null barrioId or playerUuid.");
            return false;
        }
        if (rank == Rank.OWNER) {
            plugin.getLogger().warning("Attempted to set rank to OWNER directly for player " + playerUuid + " in barrio " + barrioId);
            return false; // Cannot set OWNER rank this way
        }

        // Use the player data manager to set the rank
        return playerDataManager.setRank(barrioId, playerUuid, rank);
    }

    // --- Loading ---

    /**
     * Loads ranks for a specific barrio.
     * This method now delegates to the player data manager.
     *
     * @param barrioId The ID of the barrio to load.
     */
    public void loadRanksForBarrio(String barrioId) {
        if (barrioId == null) return;

        // Delegate to player data manager
        playerDataManager.loadPlayerDataForBarrio(barrioId);
        plugin.debug("Delegated rank loading for barrio: " + barrioId + " to player data manager");
    }

    /**
     * Gets the UUID of the owner of a barrio.
     * This method still uses the database directly since ownership is stored in the barrios table.
     *
     * @param barrioId The ID of the barrio
     * @return The UUID of the owner, or null if not found
     */
    public UUID getOwnerUUID(String barrioId) {
        try {
            return database.executeQuery(
                    "SELECT uuid FROM barrios WHERE id = ?",
                    rs -> {
                        if (rs.next()) {
                            try {
                                return UUID.fromString(rs.getString("uuid"));
                            } catch (IllegalArgumentException | SQLException e) {
                                plugin.getLogger().warning("Failed to parse owner UUID for barrio " + barrioId + ": " + e.getMessage());
                                return null;
                            }
                        }
                        return null;
                    },
                    barrioId
            );
        } catch (RuntimeException e) {
            plugin.getLogger().log(Level.SEVERE, "Database error fetching owner for barrio: " + barrioId, e);
            return null;
        }
    }

    // --- Unloading ---

    /**
     * Forcefully unloads ranks for a barrio.
     * This method now delegates to the player data manager.
     *
     * @param barrioId The barrio ID.
     * @param saveFirst If true, save ranks to DB before unloading.
     */
    public void forceUnloadBarrioRanks(String barrioId, boolean saveFirst) {
        if (barrioId == null) return;
        plugin.getLogger().info("Force unloading ranks for barrio: " + barrioId + " (Save first: " + saveFirst + ")");

        // Delegate to player data manager
        playerDataManager.forceUnloadBarrioPlayerData(barrioId, saveFirst);
    }

    // --- Special Operations ---

    /**
     * Force updates the owner of a barrio in memory.
     * This is used after ownership transfers to ensure immediate consistency.
     * This method now uses the player data manager to update ranks.
     *
     * @param barrioId The ID of the barrio
     * @param newOwnerUuid The UUID of the new owner
     * @param oldOwnerUuid The UUID of the old owner (can be null if unknown)
     * @return true if the update was successful, false otherwise
     */
    public boolean forceUpdateOwnerInMemory(String barrioId, UUID newOwnerUuid, UUID oldOwnerUuid) {
        if (barrioId == null || newOwnerUuid == null) {
            plugin.getLogger().warning("Attempted to force update owner with null barrioId or newOwnerUuid.");
            return false;
        }

        // Load player data for this barrio
        playerDataManager.loadPlayerDataForBarrio(barrioId);

        // If old owner is provided, set their rank to VISITOR
        if (oldOwnerUuid != null) {
            // We don't need to check if they were actually the owner, just set to VISITOR
            playerDataManager.setRank(barrioId, oldOwnerUuid, Rank.VISITOR);
            plugin.debug("Set former owner " + oldOwnerUuid + " to VISITOR in barrio " + barrioId);
        }

        // No need to update in-memory ranks map as it's now handled by player data manager

        plugin.getLogger().info("Force updated OWNER rank for " + newOwnerUuid + " in barrio " + barrioId);
        return true;
    }

    // --- Shutdown ---

    public void shutdown() {
        plugin.getLogger().info("Shutting down BarrioRankManager...");
        // No tasks to cancel or data to save as it's now handled by player data manager
        plugin.getLogger().info("BarrioRankManager shutdown complete.");
    }

    // --- Helper to get Barrio ID from World ---
    // Duplicated from GadgetsManager, consider moving to a shared Util class
    public static String getBarrioIdFromWorld(World world) {
        if (world == null) return null;
        String worldName = world.getName();
        // Assuming format "Barrios/some_id/world" or "Barrios/some_id/world_nether", etc.
        if (worldName.startsWith("Barrios/")) {
            String[] parts = worldName.split("/");
            // parts[0] = "Barrios"
            // parts[1] = "some_id"
            // parts[2] = "world", "world_nether", etc.
            if (parts.length > 1 && !parts[1].isEmpty()) {
                return parts[1];
            }
        }
        return null;
    }
}