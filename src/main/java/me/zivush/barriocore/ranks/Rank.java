package me.zivush.barriocore.ranks;

import java.util.Optional;

public enum Rank {
    OWNER(3),    // Highest level
    TRUSTED(2),
    RESIDENT(1),
    VISITOR(0);  // Default/No explicit rank

    private final int level;

    Rank(int level) {
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    // Helper to get rank from string, case-insensitive
    public static Optional<Rank> fromString(String rankName) {
        if (rankName == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(Rank.valueOf(rankName.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    // Check if this rank is at least the required rank
    public boolean isAtLeast(Rank requiredRank) {
        return this.level >= requiredRank.level;
    }
}