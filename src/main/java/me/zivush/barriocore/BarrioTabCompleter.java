package me.zivush.barriocore;

import me.zivush.barriocore.playerdata.BarrioPlayerData;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

public class BarrioTabCompleter implements TabCompleter {
    private static final List<String> COMMANDS_BASE = Arrays.asList("create", "expand", "admin", "visit", "tp", "transfer", "members", "ban", "unban", "kick", "info", "setspawn", "setdefault", "home", "spawn", "permissions", "gadget", "rent", "settings", "upgrades", "delete", "rate", "ratings");
    private static final List<String> PERMISSION_TYPES = Arrays.asList("visitors", "residents", "trusted");
    private static final List<String> COMMANDS_MEMBERS = Arrays.asList("set", "manage");
    private static final List<String> RANKS = Arrays.asList("visitor", "resident", "trusted");
    private static final List<String> COMMANDS_CREATE = Arrays.asList("regular", "template");
    private static final List<String> COMMANDS_ADMIN = Arrays.asList("expand", "create", "delete", "transfer", "teleport", "mode", "setserverspawn", "setspawn", "settings", "gadgets", "reload", "debug");
    private static final List<String> COMMANDS_ADMIN_CREATE = Arrays.asList("regular", "template");
    private static final List<String> BARRIO_MODES = Arrays.asList("UNLIMITED", "INACTIVITY", "RENTING", "RENTING_OFF");
    private static final List<String> GROUP_CHECKS = Arrays.asList("ALL", "RESIDENT_UP", "TRUSTED_UP", "OWNER_ONLY");
    private static final List<String> BOOLEAN_VALUES = Arrays.asList("true", "false");
    private static final List<String> WORLD_SETTINGS = Arrays.asList("pvp", "hunger", "time", "weather", "difficulty", "spawn", "respawn", "rent", "chat");
    private static final List<String> TIME_MODES = Arrays.asList("MORNING", "NOON", "NIGHT", "MIDNIGHT", "OFF");
    private static final List<String> WEATHER_MODES = Arrays.asList("CLEAR", "STORM", "RAIN", "OFF");
    private static final List<String> DIFFICULTY_MODES = Arrays.asList("PEACEFUL", "EASY", "NORMAL", "HARD");
    private static final List<String> GADGET_SETTINGS = Arrays.asList("creeper_damage", "creeper_block_damage", "ghast_damage", "ghast_block_damage", "fire_spread", "enderman_grief", "ravager_grief", "entry_title", "entry_chat");
    private final BarrioCore plugin;

    public BarrioTabCompleter(BarrioCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Gets the current barrio ID for a player.
     *
     * @param player The player
     * @return The barrio ID, or null if not in a barrio
     */
    private String getCurrentBarrioId(Player player) {
        if (player == null) return null;
        World world = player.getWorld();
        return BarrioPlayerDataManager.getBarrioIdFromWorld(world);
    }

    /**
     * Gets all barrio IDs.
     *
     * @return A list of barrio IDs
     */
    private List<String> getAllBarrioIds() {
        return new ArrayList<>(plugin.getBarrioManager().getAllBarrioIds());
    }

    /**
     * Gets all player names who have data in a specific barrio.
     *
     * @param barrioId The barrio ID
     * @return A list of player names
     */
    private List<String> getBarrioPlayerNames(String barrioId) {
        if (barrioId == null) return new ArrayList<>();

        // Ensure player data is loaded for this barrio
        plugin.getPlayerDataManager().loadPlayerDataForBarrio(barrioId);

        // Get all player UUIDs with data in this barrio
        Map<UUID, BarrioPlayerData> playerDataMap = plugin.getPlayerDataManager().getPlayerDataMap(barrioId);
        if (playerDataMap == null) return new ArrayList<>();

        // Convert UUIDs to player names
        List<String> playerNames = new ArrayList<>();
        for (UUID uuid : playerDataMap.keySet()) {
            // Try to get player name from Bukkit
            String name = Bukkit.getOfflinePlayer(uuid).getName();
            if (name != null) {
                playerNames.add(name);
            }
        }

        return playerNames;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();

        if (args.length == 1) {
            suggestions.addAll(COMMANDS_BASE);
            if (!sender.hasPermission("barrio.admin.expand")) {
                suggestions.remove("admin");
            }
            if (!sender.hasPermission("barrio.cmd.expand")) {
                suggestions.remove("expand");
            }
            if (!sender.hasPermission("barrio.cmd.visit")) {
                suggestions.remove("visit");
                suggestions.remove("tp");
            }
            if (!sender.hasPermission("barrio.cmd.transfer")) {
                suggestions.remove("transfer");
            }
            if (!sender.hasPermission("barrio.cmd.home")) {
                suggestions.remove("home");
            }
            if (!sender.hasPermission("barrio.cmd.spawn")) {
                suggestions.remove("spawn");
            }
            if (!sender.hasPermission("barrio.cmd.rent")) {
                suggestions.remove("rent");
            }
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("create")) {
                suggestions.addAll(COMMANDS_CREATE);
            } else if (args[0].equalsIgnoreCase("admin") && sender.hasPermission("barrio.admin.expand")) {
                suggestions.addAll(COMMANDS_ADMIN);
            } else if ((args[0].equalsIgnoreCase("visit") || args[0].equalsIgnoreCase("tp"))
                    && sender.hasPermission("barrio.cmd.visit")) {
                // Add online players
                suggestions.addAll(Bukkit.getOnlinePlayers().stream()
                        .map(Player::getName)
                        .collect(Collectors.toList()));

                // Add top-rated option if player has permission
                if (sender.hasPermission("barrio.cmd.visit.toprated")) {
                    suggestions.add("top-rated");
                }
            } else if (args[0].equalsIgnoreCase("transfer")) {
                if (args[1].equalsIgnoreCase("confirm")) {
                    suggestions.add("confirm");
                } else {
                    suggestions.addAll(Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .collect(Collectors.toList()));
                }
            } else if (args[0].equalsIgnoreCase("members")) {
                suggestions.addAll(COMMANDS_MEMBERS);
            } else if (args[0].equalsIgnoreCase("permissions")) {
                suggestions.addAll(PERMISSION_TYPES);
            } else if (args[0].equalsIgnoreCase("rent")) {
                suggestions.addAll(Arrays.asList("on", "off", "extend"));
            } else if (args[0].equalsIgnoreCase("rate") || args[0].equalsIgnoreCase("ratings")) {
                // Get all barrio IDs
                suggestions.addAll(getAllBarrioIds());
            } else if (args[0].equalsIgnoreCase("ban") || args[0].equalsIgnoreCase("unban") || args[0].equalsIgnoreCase("kick")) {
                // Get online players
                List<String> onlinePlayers = Bukkit.getOnlinePlayers().stream()
                        .map(Player::getName)
                        .collect(Collectors.toList());

                // Add players from the current barrio if the sender is a player
                if (sender instanceof Player) {
                    Player player = (Player) sender;
                    String barrioId = getCurrentBarrioId(player);

                    if (barrioId != null) {
                        // Get all players who have data in this barrio
                        List<String> barrioPlayers = getBarrioPlayerNames(barrioId);

                        // Add all barrio players to suggestions (will include duplicates)
                        suggestions.addAll(onlinePlayers);
                        suggestions.addAll(barrioPlayers);

                        // Remove duplicates by converting to a Set and back to a List
                        suggestions = new ArrayList<>(new HashSet<>(suggestions));
                    } else {
                        // Not in a barrio, just use online players
                        suggestions.addAll(onlinePlayers);
                    }
                } else {
                    // Not a player sender, just use online players
                    suggestions.addAll(onlinePlayers);
                }
            }
        } else if (args.length == 3) {
            if (args[0].equalsIgnoreCase("admin")) {
                if (args[1].equalsIgnoreCase("create") && sender.hasPermission("barrio.admin.create.regular")) {
                    suggestions.addAll(COMMANDS_ADMIN_CREATE);
                } else if ((args[1].equalsIgnoreCase("delete") || args[1].equalsIgnoreCase("teleport"))
                        && (sender.hasPermission("barrio.admin.delete") || sender.hasPermission("barrio.admin.teleport"))) {
                    suggestions.addAll(Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .collect(Collectors.toList()));
                } else if (args[1].equalsIgnoreCase("transfer") && sender.hasPermission("barrio.admin.transfer")) {
                    suggestions.addAll(Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .collect(Collectors.toList()));
                } else if (args[1].equalsIgnoreCase("mode") && sender.hasPermission("barrio.admin.mode")) {
                    suggestions.addAll(BARRIO_MODES);
                } else if (args[1].equalsIgnoreCase("settings") && sender.hasPermission("barrio.settings.*")) {
                    suggestions.addAll(WORLD_SETTINGS);
                } else if (args[1].equalsIgnoreCase("gadgets") && sender.hasPermission("barrio.gadgets.*")) {
                    suggestions.addAll(GADGET_SETTINGS);
                } else if (args[1].equalsIgnoreCase("debug") && sender.hasPermission("barrio.admin.debug")) {
                    suggestions.addAll(Arrays.asList("on", "off"));
                }
            } else if (args[0].equalsIgnoreCase("rate") && sender.hasPermission("barrio.cmd.rate")) {
                // Add rating stars 1-5
                int minRating = plugin.getConfig().getInt("rating.min_rating", 1);
                int maxRating = plugin.getConfig().getInt("rating.max_rating", 5);
                for (int i = minRating; i <= maxRating; i++) {
                    suggestions.add(String.valueOf(i));
                }
            }
            else if ((args[0].equalsIgnoreCase("visit") || args[0].equalsIgnoreCase("tp"))
                    && sender.hasPermission("barrio.cmd.visit")) {
                suggestions.add("<index>");
            } else if (args[0].equalsIgnoreCase("home") && sender.hasPermission("barrio.cmd.home")) {
                suggestions.add("<index>");
            } else if (args[0].equalsIgnoreCase("members")) {
                if (args[1].equalsIgnoreCase("set") || args[1].equalsIgnoreCase("manage")) {
                    // Get online players
                    List<String> onlinePlayers = Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .collect(Collectors.toList());

                    // Add players from the current barrio if the sender is a player
                    if (sender instanceof Player) {
                        Player player = (Player) sender;
                        String barrioId = getCurrentBarrioId(player);

                        if (barrioId != null) {
                            // Get all players who have data in this barrio
                            List<String> barrioPlayers = getBarrioPlayerNames(barrioId);

                            // Add all barrio players to suggestions (will include duplicates)
                            suggestions.addAll(onlinePlayers);
                            suggestions.addAll(barrioPlayers);

                            // Remove duplicates by converting to a Set and back to a List
                            suggestions = new ArrayList<>(new HashSet<>(suggestions));
                        } else {
                            // Not in a barrio, just use online players
                            suggestions.addAll(onlinePlayers);
                        }
                    } else {
                        // Not a player sender, just use online players
                        suggestions.addAll(onlinePlayers);
                    }
                }
            }
        } else if (args.length == 4) {
            if (args[0].equalsIgnoreCase("admin")) {
                if (args[1].equalsIgnoreCase("create") &&
                        (args[2].equalsIgnoreCase("regular") || args[2].equalsIgnoreCase("template")) &&
                        (sender.hasPermission("barrio.admin.create.regular") || sender.hasPermission("barrio.admin.create.template"))) {
                    suggestions.addAll(Bukkit.getOnlinePlayers().stream()
                            .map(Player::getName)
                            .collect(Collectors.toList()));
                } else if (args[1].equalsIgnoreCase("delete") && sender.hasPermission("barrio.admin.delete")) {
                    suggestions.add("<index>");
                } else if (args[1].equalsIgnoreCase("teleport") && sender.hasPermission("barrio.admin.teleport")) {
                    suggestions.add("<index>");
                } else if (args[1].equalsIgnoreCase("mode") && !args[2].equalsIgnoreCase("UNLIMITED")) {
                    suggestions.add("<time>");
                    suggestions.add("30d");
                    suggestions.add("7d");
                    suggestions.add("24h");
                } else if (args[1].equalsIgnoreCase("settings") && sender.hasPermission("barrio.settings.*")) {
                    // Provide appropriate options based on the setting
                    String setting = args[2].toLowerCase();
                    if (setting.equals("time")) {
                        suggestions.addAll(TIME_MODES);
                    } else if (setting.equals("weather")) {
                        suggestions.addAll(WEATHER_MODES);
                    } else if (setting.equals("difficulty")) {
                        suggestions.addAll(DIFFICULTY_MODES);
                    } else {
                        suggestions.addAll(Arrays.asList("true", "false"));
                    }
                } else if (args[1].equalsIgnoreCase("gadgets") && sender.hasPermission("barrio.gadgets.*")) {
                    suggestions.addAll(Arrays.asList("true", "false"));
                }
            } else if (args[0].equalsIgnoreCase("members")) {
                if (args[1].equalsIgnoreCase("set")) {
                    suggestions.addAll(RANKS);
                }
            }
        } else if (args.length == 5) {
            if (args[0].equalsIgnoreCase("admin") && args[1].equalsIgnoreCase("mode")) {
                if (args[2].equalsIgnoreCase("INACTIVITY")) {
                    suggestions.addAll(GROUP_CHECKS);
                } else if (args[2].equalsIgnoreCase("RENTING") || args[2].equalsIgnoreCase("RENTING_OFF")) {
                    suggestions.add("<price>");
                    suggestions.add("1000");
                    suggestions.add("5000");
                    suggestions.add("10000");
                }
            }
        } else if (args.length == 6) {
            if (args[0].equalsIgnoreCase("admin") && args[1].equalsIgnoreCase("mode")
                    && args[2].equalsIgnoreCase("INACTIVITY")) {
                suggestions.addAll(BOOLEAN_VALUES);
            }
        }

        StringUtil.copyPartialMatches(args[args.length - 1], suggestions, completions);
        return completions;
    }
}
