package me.zivush.barriocore.chat;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.worldsettings.BarrioWorldSettings;
import me.zivush.barriocore.worldsettings.BarrioWorldSettingsManager;
import org.bukkit.entity.Player;
import org.bukkit.World;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages barrio chat functionality.
 * This class handles the barrio chat only mode, which restricts chat visibility
 * to only players within the same barrio.
 */
public class BarrioChatManager {
    private final BarrioCore plugin;
    private final Map<UUID, Boolean> playerChatCache = new HashMap<>();

    public BarrioChatManager(BarrioCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Checks if a player should see a message from another player based on barrio settings.
     *
     * @param sender The player sending the message
     * @param receiver The player potentially receiving the message
     * @return true if the receiver should see the message, false otherwise
     */
    public boolean shouldSeeMessage(Player sender, Player receiver) {
        // If they're the same player, always return true
        if (sender.equals(receiver)) {
            return true;
        }

        // Get barrio IDs for both players
        String senderBarrioId = getPlayerBarrioId(sender);
        String receiverBarrioId = getPlayerBarrioId(receiver);

        // If sender is not in a barrio, everyone can see their messages
        if (senderBarrioId == null) {
            return true;
        }

        // Get the barrio chat only setting for the sender's barrio
        boolean barrioChatOnly = isBarrioChatOnlyEnabled(senderBarrioId);

        // If barrio chat only is disabled, everyone can see the messages
        if (!barrioChatOnly) {
            return true;
        }

        // If barrio chat only is enabled, only players in the same barrio can see the messages
        return senderBarrioId.equals(receiverBarrioId);
    }

    /**
     * Gets the barrio ID for a player based on their current world.
     *
     * @param player The player
     * @return The barrio ID, or null if not in a barrio
     */
    public String getPlayerBarrioId(Player player) {
        World world = player.getWorld();
        return getBarrioIdFromWorld(world);
    }

    /**
     * Gets the barrio ID from a world.
     *
     * @param world The world
     * @return The barrio ID, or null if not a barrio world
     */
    public String getBarrioIdFromWorld(World world) {
        if (world == null) return null;
        String worldName = world.getName();
        if (worldName.startsWith("Barrios/")) {
            // More efficient than splitting the entire string
            int startIndex = 8; // "Barrios/".length()
            int endIndex = worldName.indexOf('/', startIndex);
            if (endIndex == -1) {
                // No second slash, return everything after "Barrios/"
                return worldName.substring(startIndex);
            } else {
                // Return the part between the first and second slash
                return worldName.substring(startIndex, endIndex);
            }
        }
        return null;
    }

    /**
     * Gets the main world for a barrio ID.
     *
     * @param barrioId The barrio ID
     * @return The main world for the barrio, or null if not found
     */
    public World getWorldFromBarrioId(String barrioId) {
        if (barrioId == null) return null;
        return plugin.getServer().getWorld("Barrios/" + barrioId + "/world");
    }

    /**
     * Checks if barrio chat only mode is enabled for a specific barrio.
     *
     * @param barrioId The barrio ID
     * @return true if barrio chat only is enabled, false otherwise
     */
    public boolean isBarrioChatOnlyEnabled(String barrioId) {
        BarrioWorldSettings settings = plugin.getWorldSettingsManager().getWorldSettings(barrioId);
        return settings != null && settings.isBarrioChatOnlyEnabled();
    }

    /**
     * Clears the player chat cache when a player leaves.
     *
     * @param playerUuid The UUID of the player
     */
    public void clearPlayerCache(UUID playerUuid) {
        playerChatCache.remove(playerUuid);
    }
}
