package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerPortalEvent;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;

public class BarrioPortalListener implements Listener {
    private final BarrioCore plugin;

    public BarrioPortalListener(BarrioCore plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerPortal(PlayerPortalEvent event) {
        World fromWorld = event.getFrom().getWorld();
        if (fromWorld == null || !fromWorld.getName().startsWith("Barrios/")) return;

        String[] pathParts = fromWorld.getName().split("/");
        if (pathParts.length < 3) return;

        String barrioId = pathParts[1];
        String worldType = pathParts[2];
        Location from = event.getFrom();

        if (event.getCause() == TeleportCause.NETHER_PORTAL) {
            if (worldType.equals("world")) {
                // Going from overworld to nether
                World netherWorld = plugin.getServer().getWorld("Barrios/" + barrioId + "/world_nether");
                if (netherWorld != null) {
                    Location to = new Location(netherWorld,
                            from.getX() / 8,
                            from.getY(),
                            from.getZ() / 8,
                            from.getYaw(),
                            from.getPitch());
                    event.setTo(to);
                }
            } else if (worldType.equals("world_nether")) {
                // Going from nether to overworld
                World overworld = plugin.getServer().getWorld("Barrios/" + barrioId + "/world");
                if (overworld != null) {
                    Location to = new Location(overworld,
                            from.getX() * 8,
                            from.getY(),
                            from.getZ() * 8,
                            from.getYaw(),
                            from.getPitch());
                    event.setTo(to);
                }
            }
        } else if (event.getCause() == TeleportCause.END_PORTAL) {
            if (worldType.equals("world")) {
                // Going from overworld to end
                World endWorld = plugin.getServer().getWorld("Barrios/" + barrioId + "/world_the_end");
                if (endWorld != null) {
                    Location spawnLocation = endWorld.getSpawnLocation();
                    event.setTo(spawnLocation);
                }
            } else if (worldType.equals("world_the_end")) {
                // Going from end to overworld
                World overworld = plugin.getServer().getWorld("Barrios/" + barrioId + "/world");
                if (overworld != null) {
                    Location spawnLocation = overworld.getSpawnLocation();
                    event.setTo(spawnLocation);
                }
            }
        }
    }
}
