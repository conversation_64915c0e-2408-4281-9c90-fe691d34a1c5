package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.chat.BarrioChatManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Listener for chat-related events.
 * This listener handles the barrio chat only mode, which restricts chat visibility
 * to only players within the same barrio.
 */
public class BarrioChatListener implements Listener {
    private final BarrioCore plugin;
    private final BarrioChatManager chatManager;

    public BarrioChatListener(BarrioCore plugin, BarrioChatManager chatManager) {
        this.plugin = plugin;
        this.chatManager = chatManager;
    }

    @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player sender = event.getPlayer();
        Set<Player> recipients = event.getRecipients();
        
        // Create a copy of recipients to avoid ConcurrentModificationException
        Set<Player> recipientsToRemove = new HashSet<>();
        
        // Check each recipient to see if they should receive the message
        for (Player recipient : recipients) {
            if (!chatManager.shouldSeeMessage(sender, recipient)) {
                recipientsToRemove.add(recipient);
            }
        }
        
        // Remove players who shouldn't see the message
        recipients.removeAll(recipientsToRemove);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up any cached data when a player leaves
        chatManager.clearPlayerCache(event.getPlayer().getUniqueId());
    }
}
