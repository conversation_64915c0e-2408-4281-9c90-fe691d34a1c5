package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import me.zivush.barriocore.worldsettings.BarrioWorldSettings;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerRespawnEvent;

/**
 * Listener for handling player respawn events.
 * This listener is responsible for determining where a player should respawn
 * based on the barrio world settings.
 */
public class BarrioRespawnListener implements Listener {
    private final BarrioCore plugin;

    public BarrioRespawnListener(BarrioCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Handle player respawn events to determine the respawn location
     * based on the barrio world settings.
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();
        
        // Get the barrio ID from the player's death location
        World deathWorld = player.getWorld();
        String barrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(deathWorld);
        
        // If not in a barrio, use default respawn behavior
        if (barrioId == null) {
            return;
        }
        
        // Get the world settings for this barrio
        BarrioWorldSettings settings = plugin.getWorldSettingsManager().getWorldSettings(barrioId);
        if (settings == null) {
            return;
        }
        
        // Check if the barrio is set to use server spawn for respawning
        if (settings.isUseServerSpawn()) {
            // Use server spawn from config
            World spawnWorld = plugin.getServer().getWorld(plugin.getConfig().getString("server_spawn.world", "world"));
            double x = plugin.getConfig().getDouble("server_spawn.x", 0);
            double y = plugin.getConfig().getDouble("server_spawn.y", 100);
            double z = plugin.getConfig().getDouble("server_spawn.z", 0);
            Location serverSpawn = new Location(spawnWorld, x, y, z);
            
            // Set the respawn location to the server spawn
            event.setRespawnLocation(serverSpawn);
            plugin.getLogger().fine("Player " + player.getName() + " respawned at server spawn due to barrio settings");
        }
        // If not using server spawn, the default behavior will be to use the barrio's spawn point
    }
}
