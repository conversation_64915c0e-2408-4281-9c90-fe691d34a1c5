package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.DefaultBarrioManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Listener for default barrio-related events.
 */
public class DefaultBarrioListener implements Listener {
    private final BarrioCore plugin;
    private final DefaultBarrioManager defaultBarrioManager;

    public DefaultBarrioListener(BarrioCore plugin, DefaultBarrioManager defaultBarrioManager) {
        this.plugin = plugin;
        this.defaultBarrioManager = defaultBarrioManager;
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Load default barrio data when player joins
        Player player = event.getPlayer();
        defaultBarrioManager.loadPlayerDefaultBarrio(player.getUniqueId());
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Save and unload default barrio data when player leaves
        Player player = event.getPlayer();
        defaultBarrioManager.savePlayerDefaultBarrio(player.getUniqueId());
        defaultBarrioManager.unloadPlayerDefaultBarrio(player.getUniqueId());
    }
}
