package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.upgrades.BarrioUpgradeSettings;
import me.zivush.barriocore.upgrades.BarrioUpgradesManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.EntityType;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.event.entity.EntityDeathEvent;

/**
 * Listener for handling upgrade limits on block placement and entity spawning.
 */
public class BarrioUpgradesListener implements Listener {

    private final BarrioCore plugin;

    public BarrioUpgradesListener(BarrioCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Handle block placement events to enforce upgrade limits.
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPlace(BlockPlaceEvent event) {
        World world = event.getBlock().getWorld();
        String barrioId = BarrioUpgradesManager.getBarrioIdFromWorld(world);

        if (barrioId == null) {
            return; // Not in a barrio world
        }

        Material material = event.getBlock().getType();

        // Check if this material has upgrade limits configured
        if (!isMaterialLimited(material)) {
            return; // Material not limited by upgrades
        }

        BarrioUpgradeSettings settings = plugin.getUpgradesManager().getSettings(barrioId);
        int upgradeLevel = settings.getBlockUpgradeLevel(material);

        // Get the limit for this upgrade level
        int limit = getBlockLimit(material, upgradeLevel);
        if (limit <= 0) {
            // No upgrade purchased, block placement
            event.setCancelled(true);
            event.getPlayer().sendMessage(plugin.getMessage("messages.block_limit_no_upgrade")
                .replace("%material%", material.name().toLowerCase().replace("_", " ")));
            return;
        }

        // Get current count from memory (much faster than scanning worlds)
        int currentCount = settings.getBlockCount(material);

        if (currentCount >= limit) {
            event.setCancelled(true);
            event.getPlayer().sendMessage(plugin.getMessage("messages.block_limit_reached")
                .replace("%material%", material.name().toLowerCase().replace("_", " "))
                .replace("%limit%", String.valueOf(limit))
                .replace("%current%", String.valueOf(currentCount)));
        } else {
            // Block placement is allowed, increment the count
            settings.incrementBlockCount(material);
            plugin.getUpgradesManager().saveSettings(settings);
        }
    }

    /**
     * Handle entity spawn events to enforce upgrade limits.
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onCreatureSpawn(CreatureSpawnEvent event) {
        World world = event.getLocation().getWorld();
        String barrioId = BarrioUpgradesManager.getBarrioIdFromWorld(world);

        if (barrioId == null) {
            return; // Not in a barrio world
        }

        EntityType entityType = event.getEntityType();

        // Check if this entity type has upgrade limits configured
        if (!isEntityLimited(entityType)) {
            return; // Entity not limited by upgrades
        }

        BarrioUpgradeSettings settings = plugin.getUpgradesManager().getSettings(barrioId);
        int upgradeLevel = settings.getEntityUpgradeLevel(entityType);

        // Get the limit for this upgrade level
        int limit = getEntityLimit(entityType, upgradeLevel);
        if (limit <= 0) {
            // No upgrade purchased, cancel spawn
            event.setCancelled(true);
            return;
        }

        // Get current count from memory (much faster than scanning worlds)
        int currentCount = settings.getEntityCount(entityType);

        if (currentCount >= limit) {
            event.setCancelled(true);
        } else {
            // Entity spawn is allowed, increment the count
            settings.incrementEntityCount(entityType);
            plugin.getUpgradesManager().saveSettings(settings);
        }
    }

    /**
     * Handle block break events to decrement block counts.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreak(BlockBreakEvent event) {
        if (event.isCancelled()) {
            return; // Don't process if the event was cancelled
        }

        World world = event.getBlock().getWorld();
        String barrioId = BarrioUpgradesManager.getBarrioIdFromWorld(world);

        if (barrioId == null) {
            return; // Not in a barrio world
        }

        Material material = event.getBlock().getType();

        // Check if this material has upgrade limits configured
        if (!isMaterialLimited(material)) {
            return; // Material not limited by upgrades
        }

        BarrioUpgradeSettings settings = plugin.getUpgradesManager().getSettings(barrioId);

        // Decrement the count if the block was tracked
        if (settings.getBlockCount(material) > 0) {
            settings.decrementBlockCount(material);
            plugin.getUpgradesManager().saveSettings(settings);
        }
    }

    /**
     * Handle entity death events to decrement entity counts.
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDeath(EntityDeathEvent event) {
        World world = event.getEntity().getWorld();
        String barrioId = BarrioUpgradesManager.getBarrioIdFromWorld(world);

        if (barrioId == null) {
            return; // Not in a barrio world
        }

        EntityType entityType = event.getEntityType();

        // Check if this entity type has upgrade limits configured
        if (!isEntityLimited(entityType)) {
            return; // Entity not limited by upgrades
        }

        BarrioUpgradeSettings settings = plugin.getUpgradesManager().getSettings(barrioId);

        // Decrement the count if the entity was tracked
        if (settings.getEntityCount(entityType) > 0) {
            settings.decrementEntityCount(entityType);
            plugin.getUpgradesManager().saveSettings(settings);
        }
    }

    /**
     * Check if a material is limited by the upgrade system.
     */
    private boolean isMaterialLimited(Material material) {
        ConfigurationSection blockUpgradesSection = plugin.getGuiConfig().getConfigurationSection("barrio_block_upgrades");
        if (blockUpgradesSection == null) return false;

        for (String key : blockUpgradesSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }

            ConfigurationSection itemSection = blockUpgradesSection.getConfigurationSection(key);
            if (itemSection != null && itemSection.contains("material")) {
                try {
                    Material configMaterial = Material.valueOf(itemSection.getString("material"));
                    if (configMaterial == material) {
                        return true;
                    }
                } catch (IllegalArgumentException ignored) {
                    // Invalid material in config
                }
            }
        }
        return false;
    }

    /**
     * Check if an entity type is limited by the upgrade system.
     */
    private boolean isEntityLimited(EntityType entityType) {
        ConfigurationSection entityUpgradesSection = plugin.getGuiConfig().getConfigurationSection("barrio_entity_upgrades");
        if (entityUpgradesSection == null) return false;

        for (String key : entityUpgradesSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }

            ConfigurationSection itemSection = entityUpgradesSection.getConfigurationSection(key);
            if (itemSection != null && itemSection.contains("entity_type")) {
                try {
                    EntityType configEntityType = EntityType.valueOf(itemSection.getString("entity_type"));
                    if (configEntityType == entityType) {
                        return true;
                    }
                } catch (IllegalArgumentException ignored) {
                    // Invalid entity type in config
                }
            }
        }
        return false;
    }

    /**
     * Get the block limit for a specific material and upgrade level.
     */
    private int getBlockLimit(Material material, int upgradeLevel) {
        ConfigurationSection blockUpgradesSection = plugin.getGuiConfig().getConfigurationSection("barrio_block_upgrades");
        if (blockUpgradesSection == null) return 0;

        for (String key : blockUpgradesSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }

            ConfigurationSection itemSection = blockUpgradesSection.getConfigurationSection(key);
            if (itemSection != null && itemSection.contains("material")) {
                try {
                    Material configMaterial = Material.valueOf(itemSection.getString("material"));
                    if (configMaterial == material) {
                        ConfigurationSection levelsSection = itemSection.getConfigurationSection("levels");
                        if (levelsSection != null) {
                            ConfigurationSection levelSection = levelsSection.getConfigurationSection(String.valueOf(upgradeLevel));
                            if (levelSection != null) {
                                return levelSection.getInt("limit", 0);
                            }
                        }
                    }
                } catch (IllegalArgumentException ignored) {
                    // Invalid material in config
                }
            }
        }
        return 0;
    }

    /**
     * Get the entity limit for a specific entity type and upgrade level.
     */
    private int getEntityLimit(EntityType entityType, int upgradeLevel) {
        ConfigurationSection entityUpgradesSection = plugin.getGuiConfig().getConfigurationSection("barrio_entity_upgrades");
        if (entityUpgradesSection == null) return 0;

        for (String key : entityUpgradesSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }

            ConfigurationSection itemSection = entityUpgradesSection.getConfigurationSection(key);
            if (itemSection != null && itemSection.contains("entity_type")) {
                try {
                    EntityType configEntityType = EntityType.valueOf(itemSection.getString("entity_type"));
                    if (configEntityType == entityType) {
                        ConfigurationSection levelsSection = itemSection.getConfigurationSection("levels");
                        if (levelsSection != null) {
                            ConfigurationSection levelSection = levelsSection.getConfigurationSection(String.valueOf(upgradeLevel));
                            if (levelSection != null) {
                                return levelSection.getInt("limit", 0);
                            }
                        }
                    }
                } catch (IllegalArgumentException ignored) {
                    // Invalid entity type in config
                }
            }
        }
        return 0;
    }


}
