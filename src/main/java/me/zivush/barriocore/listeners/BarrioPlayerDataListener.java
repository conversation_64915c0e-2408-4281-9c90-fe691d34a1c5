package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

/**
 * Listener for player data-related events.
 */
public class BarrioPlayerDataListener implements Listener {
    private final BarrioCore plugin;
    private final BarrioPlayerDataManager playerDataManager;

    public BarrioPlayerDataListener(BarrioCore plugin, BarrioPlayerDataManager playerDataManager) {
        this.plugin = plugin;
        this.playerDataManager = playerDataManager;
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        handleWorldChange(event.getPlayer(), event.getFrom(), event.getTo());
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // Update player data when they join the server
        Player player = event.getPlayer();
        String barrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(player.getWorld());
        if (barrioId != null) {
            playerDataManager.updatePlayerLocation(barrioId, player.getUniqueId(), player.getLocation());
            playerDataManager.updateActivity(barrioId);
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Update player data when they leave the server
        Player player = event.getPlayer();
        String barrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(player.getWorld());
        if (barrioId != null) {
            playerDataManager.updatePlayerLocation(barrioId, player.getUniqueId(), player.getLocation());
        }
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Only update location on significant movement (block change)
        if (event.getFrom().getBlockX() == event.getTo().getBlockX() &&
            event.getFrom().getBlockY() == event.getTo().getBlockY() &&
            event.getFrom().getBlockZ() == event.getTo().getBlockZ()) {
            return;
        }
        
        String barrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(event.getTo().getWorld());
        if (barrioId != null) {
            playerDataManager.updatePlayerLocation(barrioId, event.getPlayer().getUniqueId(), event.getTo());
        }
    }

    private void handleWorldChange(Player player, Location fromLocation, Location toLocation) {
        // Handle leaving a barrio
        if (fromLocation != null) {
            String fromBarrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(fromLocation.getWorld());
            if (fromBarrioId != null) {
                // Update last location when player leaves
                playerDataManager.updatePlayerLocation(fromBarrioId, player.getUniqueId(), fromLocation);
                playerDataManager.updateActivity(fromBarrioId);
            }
        }

        // Handle entering a barrio
        if (toLocation != null) {
            String toBarrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(toLocation.getWorld());
            if (toBarrioId != null) {
                // Load player data and update activity
                playerDataManager.loadPlayerDataForBarrio(toBarrioId);
                playerDataManager.updateActivity(toBarrioId);
                
                // Check if player is banned
                if (playerDataManager.isBanned(toBarrioId, player.getUniqueId())) {
                    // Teleport player to server spawn
                    plugin.getServer().getScheduler().runTask(plugin, () -> {
                        teleportToServerSpawn(player);
                        player.sendMessage(plugin.getMessage("messages.banned_from_barrio"));
                    });
                }
            }
        }
    }

    private void teleportToServerSpawn(Player player) {
        org.bukkit.World spawnWorld = plugin.getServer().getWorld(plugin.getConfig().getString("server_spawn.world", "world"));
        double x = plugin.getConfig().getDouble("server_spawn.x", 0);
        double y = plugin.getConfig().getDouble("server_spawn.y", 100);
        double z = plugin.getConfig().getDouble("server_spawn.z", 0);
        Location serverSpawn = new Location(spawnWorld, x, y, z);
        player.teleport(serverSpawn);
    }
}
