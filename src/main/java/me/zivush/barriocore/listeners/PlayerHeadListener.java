package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerheads.PlayerHeadManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Listener for player head-related events
 */
public class PlayerHeadListener implements Listener {
    private final BarrioCore plugin;
    private final PlayerHeadManager playerHeadManager;

    public PlayerHeadListener(BarrioCore plugin, PlayerHeadManager playerHeadManager) {
        this.plugin = plugin;
        this.playerHeadManager = playerHeadManager;
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Update the player's head texture in the cache
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            playerHeadManager.updatePlayerHeadTexture(player);
        });
    }
}
