package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.permissions.BarrioPermissionManager;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Boat;
import org.bukkit.entity.ChestBoat;
import org.bukkit.entity.Entity;
import org.bukkit.entity.ItemFrame;
import org.bukkit.entity.Monster;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.hanging.HangingBreakByEntityEvent;
import org.bukkit.event.player.*;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.event.entity.EntityDamageEvent;

public class BarrioPermissionsListener implements Listener {
    private final BarrioCore plugin;
    private final BarrioPermissionManager permManager;

    public BarrioPermissionsListener(BarrioCore plugin, BarrioPermissionManager permManager) {
        this.plugin = plugin;
        this.permManager = permManager;
    }

    private String getBarrioId(Location location) {
        String worldName = location.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) return null;
        return worldName.split("/")[1];
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        String barrioId = getBarrioId(event.getBlock().getLocation());
        if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "block_break")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        String barrioId = getBarrioId(event.getBlock().getLocation());
        if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "block_place")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onBucketFill(PlayerBucketFillEvent event) {
        String barrioId = getBarrioId(event.getBlock().getLocation());
        if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "bucket_use")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onBucketEmpty(PlayerBucketEmptyEvent event) {
        String barrioId = getBarrioId(event.getBlock().getLocation());
        if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "bucket_use")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onInteract(PlayerInteractEvent event) {
        if (event.getClickedBlock() != null) {
            String barrioId = getBarrioId(event.getClickedBlock().getLocation());
            if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "interaction")) {
                event.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onInventoryOpen(InventoryOpenEvent event) {
        if (event.getInventory().getLocation() != null) {
            String barrioId = getBarrioId(event.getInventory().getLocation());
            if (barrioId != null && !permManager.hasPermission(barrioId, (Player)event.getPlayer(), "storage_access")) {
                event.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onEntityInteract(PlayerInteractEntityEvent event) {
        Entity entity = event.getRightClicked();
        String barrioId = getBarrioId(entity.getLocation());
        if (barrioId == null) return;

        Player player = event.getPlayer();

        // Special handling for armor stands - require both block_break and entity_interact permissions
        if (entity instanceof ArmorStand) {
            if (!permManager.hasPermission(barrioId, player, "block_break") ||
                !permManager.hasPermission(barrioId, player, "entity_interact")) {
                event.setCancelled(true);
                plugin.debug("Prevented player " + player.getName() + " from interacting with an armor stand in barrio " + barrioId);
                return;
            }
        }

        // General entity interaction permission check
        if (!permManager.hasPermission(barrioId, player, "entity_interact")) {
            event.setCancelled(true);
        }
    }
    @EventHandler
    public void OnInteractAtEntity(PlayerInteractAtEntityEvent event) {
        Entity entity = event.getRightClicked();
        String barrioId = getBarrioId(entity.getLocation());
        if (barrioId == null) return;

        Player player = event.getPlayer();

        // Special handling for armor stands - require both block_break and entity_interact permissions
        if (entity instanceof ArmorStand) {
            if (!permManager.hasPermission(barrioId, player, "block_break") ||
                    !permManager.hasPermission(barrioId, player, "entity_interact")) {
                event.setCancelled(true);
                plugin.debug("Prevented player " + player.getName() + " from interacting with an armor stand in barrio " + barrioId);
                return;
            }
        }

        // General entity interaction permission check
        if (!permManager.hasPermission(barrioId, player, "entity_interact")) {
            event.setCancelled(true);
        }
    }
    @EventHandler
    public void onItemDrop(PlayerDropItemEvent event) {
        String barrioId = getBarrioId(event.getPlayer().getLocation());
        if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "item_handling")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onItemPickup(PlayerPickupItemEvent event) {
        String barrioId = getBarrioId(event.getItem().getLocation());
        if (barrioId != null && !permManager.hasPermission(barrioId, event.getPlayer(), "item_handling")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) return;
        Player player = (Player) event.getDamager();
        String barrioId = getBarrioId(event.getEntity().getLocation());
        if (barrioId == null) return;

        Entity victim = event.getEntity();

        // Check for boats, chest boats, armor stands, and item frames
        if (victim instanceof Boat || victim instanceof ChestBoat || victim instanceof ArmorStand || victim instanceof ItemFrame) {
            if (!permManager.hasPermission(barrioId, player, "block_break")) {
                event.setCancelled(true);
                plugin.debug("Prevented player " + player.getName() + " from breaking a " + victim.getType().name() + " in barrio " + barrioId);
                return;
            }
        }

        // Handle player vs player combat
        if (victim instanceof Player) {
            if (!permManager.hasPermission(barrioId, player, "pvp")) {
                event.setCancelled(true);
            }
        } else {
            String permission = isHostileMob(victim) ? "hostile_pve" : "passive_pve";
            if (!permManager.hasPermission(barrioId, player, permission)) {
                event.setCancelled(true);
            }
        }
    }

    private boolean isHostileMob(Entity entity) {
        return entity instanceof Monster;
    }

    @EventHandler
    public void onHangingBreak(HangingBreakByEntityEvent event) {
        // Check if the hanging entity is an item frame
        if (event.getEntity() instanceof ItemFrame) {
            // Check if the remover is a player
            if (event.getRemover() instanceof Player) {
                Player player = (Player) event.getRemover();
                String barrioId = getBarrioId(event.getEntity().getLocation());

                // If in a barrio and player doesn't have block_break permission, cancel the event
                if (barrioId != null && !permManager.hasPermission(barrioId, player, "block_break")) {
                    event.setCancelled(true);
                    plugin.debug("Prevented player " + player.getName() + " from breaking an item frame in barrio " + barrioId);
                }
            }
        }
    }
}
