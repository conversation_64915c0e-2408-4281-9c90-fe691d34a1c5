package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.gadgets.BarrioGadgetSettings;
import me.zivush.barriocore.gadgets.BarrioGadgetsManager;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.*;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBurnEvent;
import org.bukkit.event.block.BlockIgniteEvent;
import org.bukkit.event.block.BlockSpreadEvent;
import org.bukkit.event.entity.EntityChangeBlockEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityExplodeEvent;
import org.bukkit.event.player.PlayerChangedWorldEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

public class BarrioGadgetsListener implements Listener {

    private final BarrioCore plugin;
    private final BarrioGadgetsManager gadgetsManager;

    public BarrioGadgetsListener(BarrioCore plugin) {
        this.plugin = plugin;
        this.gadgetsManager = plugin.getGadgetsManager();
    }

    // --- Event Cancellers based on Settings ---

    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onCreeperExplode(EntityExplodeEvent event) {
        if (event.getEntityType() == EntityType.CREEPER) {
            String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(event.getLocation().getWorld());
            if (barrioId != null) {
                BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
                if (settings != null && !settings.isCreeperBlockDamageEnabled()) {
                    event.blockList().clear(); // Prevent block damage
                    plugin.debug("Cancelled creeper block damage in barrio " + barrioId);
                }
            }
        }
    }
    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onGhastExplode(EntityExplodeEvent event) {
        if (event.getEntityType() == EntityType.FIREBALL) { // Ghast fireballs
            Entity source = (Entity) ((Projectile) event.getEntity()).getShooter();
            if (source instanceof Ghast) {
                String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(event.getLocation().getWorld());
                if (barrioId != null) {
                    BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
                    if (settings != null && !settings.isGhastBlockDamageEnabled()) {
                        event.blockList().clear(); // Prevent block damage
                        event.setYield(0f); // Prevent item drops from blocks
                        plugin.debug("Cancelled ghast block damage in barrio " + barrioId);
                    }
                }
            }
        }
    }
    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        // Disable player damage from creepers/ghasts if toggled off
        Entity damager = event.getDamager();
        String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(event.getEntity().getWorld());
        if (barrioId == null) return;

        BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
        if (settings == null) return;

        // Creeper Player Damage
        if (damager instanceof Creeper || (damager instanceof AreaEffectCloud && ((AreaEffectCloud) damager).getSource() instanceof Creeper)) {
            if (!settings.isCreeperDamageEnabled()) {
                event.setCancelled(true);
                plugin.debug("Cancelled creeper player damage in barrio " + barrioId);
            }
        }
        // Ghast Fireball Damage
        else if (damager instanceof Fireball) {
            Projectile projectile = (Projectile) damager;
            if(projectile.getShooter() instanceof Ghast){
                if (!settings.isGhastDamageEnabled()) {
                    event.setCancelled(true);
                    plugin.debug("Cancelled ghast player damage in barrio " + barrioId);
                }
            }
        }
    }


    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onFireSpread(BlockSpreadEvent event) {
        if (event.getSource().getType() == Material.FIRE) {
            handleFireEvent(event.getBlock(), event);
        }
    }

    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onBlockBurn(BlockBurnEvent event) {
        handleFireEvent(event.getBlock(), event);
    }
    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onBlockIgnite(BlockIgniteEvent event) {
        // Only cancel natural fire spread/ignition, not player actions like flint/steel
        if (event.getCause() == BlockIgniteEvent.IgniteCause.SPREAD || event.getCause() == BlockIgniteEvent.IgniteCause.LAVA) {
            handleFireEvent(event.getBlock(), event);
        }
    }

    private void handleFireEvent(Block block, org.bukkit.event.Cancellable event) {
        String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(block.getWorld());
        if (barrioId != null) {
            BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
            if (settings != null && !settings.isFireSpreadEnabled()) {
                event.setCancelled(true);
                plugin.debug("Cancelled fire spread/burn in barrio " + barrioId);
            }
        }
    }


    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onEndermanGrief(EntityChangeBlockEvent event) {
        if (event.getEntityType() == EntityType.ENDERMAN) {
            String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(event.getBlock().getWorld());
            if (barrioId != null) {
                BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
                if (settings != null && !settings.isEndermanGriefEnabled()) {
                    event.setCancelled(true);
                    plugin.debug("Cancelled enderman grief in barrio " + barrioId);
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void onRavagerGrief(EntityChangeBlockEvent event) {
        // Ravagers break leaves, crops etc.
        if (event.getEntityType() == EntityType.RAVAGER) {
            String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(event.getBlock().getWorld());
            if (barrioId != null) {
                BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
                if (settings != null && !settings.isRavagerGriefEnabled()) {
                    event.setCancelled(true);
                    plugin.debug("Cancelled ravager grief in barrio " + barrioId);
                }
            }
        }
    }

    // --- Loading/Unloading and Entry Messages ---

    // Use TeleportEvent as it's more general than ChangedWorldEvent
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        handleWorldChange(event.getPlayer(), event.getFrom(), event.getTo());
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerJoin(PlayerJoinEvent event) {
        handleWorldChange(event.getPlayer(), null, event.getPlayer().getLocation());
    }

    @EventHandler (priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerQuit(PlayerQuitEvent event) {
        handleWorldChange(event.getPlayer(), event.getPlayer().getLocation(), null);
    }

    private void handleWorldChange(Player player, Location fromLocation, Location toLocation) {
        // Get barrio IDs for from and to locations
        String fromBarrioId = null;
        if (fromLocation != null) {
            fromBarrioId = BarrioGadgetsManager.getBarrioIdFromWorld(fromLocation.getWorld());
            if (fromBarrioId != null) {
                // Update last activity timestamp when player leaves
                gadgetsManager.updateActivity(fromBarrioId);
            }
        }

        // Handle entering a barrio
        if (toLocation != null) {
            String toBarrioId = BarrioGadgetsManager.getBarrioIdFromWorld(toLocation.getWorld());
            if (toBarrioId != null) {
                // Get settings from cache or load from DB if not loaded yet
                BarrioGadgetSettings settings = gadgetsManager.getSettings(toBarrioId);

                // Only send entry messages if the player is entering a different barrio
                // or if they weren't in a barrio before
                if (fromBarrioId == null || !fromBarrioId.equals(toBarrioId)) {
                    plugin.debug("Player " + player.getName() + " entered barrio " + toBarrioId + " from " +
                        (fromBarrioId != null ? "barrio " + fromBarrioId : "outside") + ". Sending entry messages.");
                    sendEntryMessages(player, settings);
                } else {
                    plugin.debug("Player " + player.getName() + " teleported within the same barrio " +
                        toBarrioId + ". Skipping entry messages.");
                }
            }
        }
    }



    private void sendEntryMessages(Player player, BarrioGadgetSettings settings) {
        if (settings == null) return;

        if (settings.isShowEntryTitleEnabled()) {
            String title = plugin.getMessage("messages.gadgets_entry_title"); // Get from messages.yml
            String subtitle = plugin.getMessage("messages.gadgets_entry_subtitle"); // Optional subtitle
            if (!title.isEmpty()) {
                player.sendTitle(
                        ChatColor.translateAlternateColorCodes('&', title),
                        ChatColor.translateAlternateColorCodes('&', subtitle),
                        10, 70, 20); // Default timings (fadein, stay, fadeout)
            }
        }

        if (settings.isShowEntryChatEnabled()) {
            String chat = plugin.getMessage("messages.gadgets_entry_chat"); // Get from messages.yml
            if (!chat.isEmpty()) {
                player.sendMessage(ChatColor.translateAlternateColorCodes('&', chat));
            }
        }
    }
}