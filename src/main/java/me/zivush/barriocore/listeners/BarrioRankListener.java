package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import me.zivush.barriocore.ranks.BarrioRankManager;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

public class BarrioRankListener implements Listener {

    private final BarrioCore plugin;
    private final BarrioRankManager rankManager;
    private final BarrioPlayerDataManager playerDataManager;

    public BarrioRankListener(BarrioCore plugin, BarrioRankManager rankManager) {
        this.plugin = plugin;
        this.rankManager = rankManager;
        this.playerDataManager = plugin.getPlayerDataManager();
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerJoin(PlayerJoinEvent event) {
        handleWorldChange(event.getPlayer(), null, event.getPlayer().getLocation());
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerQuit(PlayerQuitEvent event) {
        handleWorldChange(event.getPlayer(), event.getPlayer().getLocation(), null);
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        // Only handle inter-world teleports or teleports where the barrio ID might change
        // (Handles changing between nether/overworld/end within the *same* barrio implicitly)
        World fromWorld = event.getFrom().getWorld();
        World toWorld = event.getTo().getWorld();

        if (fromWorld == null || toWorld == null || !fromWorld.equals(toWorld)) {
            handleWorldChange(event.getPlayer(), event.getFrom(), event.getTo());
        } else {
            // Same world teleport, check if barrio ID differs (e.g., admin teleport between barrios)
            String fromBarrioId = BarrioRankManager.getBarrioIdFromWorld(fromWorld);
            String toBarrioId = BarrioRankManager.getBarrioIdFromWorld(toWorld); // Should be same world, but check anyway
            if (fromBarrioId == null && toBarrioId != null || // Entering a barrio
                    fromBarrioId != null && toBarrioId == null || // Leaving a barrio
                    fromBarrioId != null && toBarrioId != null && !fromBarrioId.equals(toBarrioId)) // Moving between barrios
            {
                handleWorldChange(event.getPlayer(), event.getFrom(), event.getTo());
            }
            // If fromBarrioId == toBarrioId (or both null), no rank load/unload logic needed for same-world TP
        }
    }

    /**
     * Handles loading ranks when entering a barrio and updating activity.
     *
     * @param player      The player involved.
     * @param fromLocation The location the player came from (can be null on join).
     * @param toLocation   The location the player is going to (can be null on quit).
     */
    private void handleWorldChange(Player player, Location fromLocation, Location toLocation) {
        String fromBarrioId = null;
        if (fromLocation != null && fromLocation.getWorld() != null) {
            fromBarrioId = BarrioRankManager.getBarrioIdFromWorld(fromLocation.getWorld());
        }

        String toBarrioId = null;
        if (toLocation != null && toLocation.getWorld() != null) {
            toBarrioId = BarrioRankManager.getBarrioIdFromWorld(toLocation.getWorld());
        }

        // --- Leaving Logic ---
        // If the player was in a barrio and is no longer in that *specific* barrio
        // (or is leaving the server)
        if (fromBarrioId != null && !fromBarrioId.equals(toBarrioId)) {
            // Player left 'fromBarrioId', update its activity timestamp in player data manager
            // The inactivity checker will handle the actual unload later if needed
            playerDataManager.updateActivity(fromBarrioId);
            plugin.debugFinest("Player " + player.getName() + " left barrio " + fromBarrioId + ". Updated activity.");
        }

        // --- Entering Logic ---
        // If the player is now in a barrio they weren't in before
        if (toBarrioId != null && !toBarrioId.equals(fromBarrioId)) {
            // Player entered 'toBarrioId', ensure player data is loaded and activity updated
            playerDataManager.loadPlayerDataForBarrio(toBarrioId);
            playerDataManager.updateActivity(toBarrioId);
            plugin.debugFinest("Player " + player.getName() + " entered barrio " + toBarrioId + ". Loaded player data/updated activity.");
        }
    }
}