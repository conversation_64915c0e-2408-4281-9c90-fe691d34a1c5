package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.worldsettings.BarrioWorldSettingsManager;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

/**
 * Listener for world settings-related events.
 * This listener is responsible for tracking player activity in barrio worlds
 * and updating the activity timestamps.
 */
public class BarrioWorldSettingsListener implements Listener {
    private final BarrioCore plugin;

    public BarrioWorldSettingsListener(BarrioCore plugin) {
        this.plugin = plugin;
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        handleWorldChange(event.getPlayer(), event.getFrom(), event.getTo());
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerJoin(PlayerJoinEvent event) {
        handleWorldChange(event.getPlayer(), null, event.getPlayer().getLocation());
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerQuit(PlayerQuitEvent event) {
        handleWorldChange(event.getPlayer(), event.getPlayer().getLocation(), null);
    }

    private void handleWorldChange(Player player, Location fromLocation, Location toLocation) {
        // Handle leaving a barrio
        if (fromLocation != null) {
            String fromBarrioId = getBarrioIdFromLocation(fromLocation);
            if (fromBarrioId != null) {
                // Update last activity timestamp when player leaves
                plugin.getWorldSettingsManager().updateActivity(fromBarrioId);
            }
        }

        // Handle entering a barrio
        if (toLocation != null) {
            String toBarrioId = getBarrioIdFromLocation(toLocation);
            if (toBarrioId != null) {
                // Update last activity timestamp when player enters
                plugin.getWorldSettingsManager().updateActivity(toBarrioId);
            }
        }
    }

    /**
     * Gets the barrio ID from a location.
     *
     * @param location The location
     * @return The barrio ID, or null if not in a barrio world
     */
    private String getBarrioIdFromLocation(Location location) {
        if (location == null || location.getWorld() == null) {
            return null;
        }
        
        String worldName = location.getWorld().getName();
        if (worldName.startsWith("Barrios/")) {
            // More efficient than splitting the entire string
            int startIndex = 8; // "Barrios/".length()
            int endIndex = worldName.indexOf('/', startIndex);
            if (endIndex == -1) {
                // No second slash, return everything after "Barrios/"
                return worldName.substring(startIndex);
            } else {
                // Return the part between the first and second slash
                return worldName.substring(startIndex, endIndex);
            }
        }
        return null;
    }
}
