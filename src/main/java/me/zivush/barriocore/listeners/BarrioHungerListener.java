package me.zivush.barriocore.listeners;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.worldsettings.BarrioWorldSettings;
import me.zivush.barriocore.worldsettings.BarrioWorldSettingsManager;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.FoodLevelChangeEvent;

/**
 * Listener for handling hunger-related events in barrios.
 * Prevents hunger loss when the hunger loss toggle is disabled for a barrio.
 */
public class BarrioHungerListener implements Listener {
    private final BarrioCore plugin;
    private final BarrioWorldSettingsManager worldSettingsManager;

    public BarrioHungerListener(BarrioCore plugin) {
        this.plugin = plugin;
        this.worldSettingsManager = plugin.getWorldSettingsManager();
    }

    /**
     * Get the barrio ID from a world.
     *
     * @param world The world to check
     * @return The barrio ID, or null if not a barrio world
     */
    private String getBarrioIdFromWorld(World world) {
        String worldName = world.getName();
        if (!worldName.startsWith("Barrios/")) return null;

        // Extract barrio ID from world name (format: "Barrios/barrioId/worldType")
        String[] parts = worldName.split("/");
        if (parts.length < 2) return null;

        return parts[1];
    }

    /**
     * Handle food level change events to prevent hunger loss when disabled.
     */
    @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
    public void onFoodLevelChange(FoodLevelChangeEvent event) {
        if (!(event.getEntity() instanceof Player)) return;

        Player player = (Player) event.getEntity();
        String barrioId = getBarrioIdFromWorld(player.getWorld());

        // If not in a barrio, allow normal hunger behavior
        if (barrioId == null) return;

        // Get the world settings for this barrio
        BarrioWorldSettings settings = worldSettingsManager.getWorldSettings(barrioId);
        if (settings == null) return;

        // If hunger loss is disabled and the new food level is lower (hunger decreasing)
        if (!settings.isHungerLossEnabled() && event.getFoodLevel() < player.getFoodLevel()) {
            event.setCancelled(true);
            plugin.debugFiner("Cancelled hunger loss for player " + player.getName() + " in barrio " + barrioId);
        }
    }
}
