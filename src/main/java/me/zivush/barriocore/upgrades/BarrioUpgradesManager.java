package me.zivush.barriocore.upgrades;

import me.zivush.barriocore.BarrioCore;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.World;
import org.bukkit.entity.EntityType;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manager class for handling barrio upgrades system.
 * Manages block and entity upgrade limits, database operations, and economy integration.
 */
public class BarrioUpgradesManager {
    
    private final BarrioCore plugin;
    private final Map<String, BarrioUpgradeSettings> loadedSettings = new ConcurrentHashMap<>();
    private final Map<String, Long> lastActivity = new ConcurrentHashMap<>();
    private final long inactivityTimeoutMillis;
    private BukkitTask inactivityCheckTask;
    private Economy economy;

    public BarrioUpgradesManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.inactivityTimeoutMillis = plugin.getConfig().getLong("upgrades.inactivity_unload_minutes", 5) * 60 * 1000;
        setupEconomy();
        createTables();
        startInactivityCheck();
    }

    /**
     * Set up Vault economy integration.
     */
    private void setupEconomy() {
        if (Bukkit.getPluginManager().getPlugin("Vault") == null) {
            plugin.getLogger().warning("Vault not found! Upgrade purchases will not work.");
            return;
        }
        RegisteredServiceProvider<Economy> rsp = plugin.getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp != null) {
            economy = rsp.getProvider();
            plugin.getLogger().info("Economy integration enabled for upgrades.");
        } else {
            plugin.getLogger().warning("No economy provider found! Upgrade purchases will not work.");
        }
    }

    /**
     * Create the database tables for upgrades if they don't exist.
     */
    private void createTables() {
        // Create the main upgrades table
        plugin.getDatabase().executeUpdate(
            "CREATE TABLE IF NOT EXISTS barrio_upgrades (" +
            "barrio_id VARCHAR(8), " +
            "upgrade_type VARCHAR(16), " +
            "upgrade_target VARCHAR(64), " +
            "upgrade_level INT NOT NULL DEFAULT 0, " +
            "PRIMARY KEY (barrio_id, upgrade_type, upgrade_target), " +
            "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
        );

        // Create the block/entity counts table for tracking actual placed items
        plugin.getDatabase().executeUpdate(
            "CREATE TABLE IF NOT EXISTS barrio_upgrade_counts (" +
            "barrio_id VARCHAR(8), " +
            "count_type VARCHAR(16), " +
            "count_target VARCHAR(64), " +
            "count_value INT NOT NULL DEFAULT 0, " +
            "PRIMARY KEY (barrio_id, count_type, count_target), " +
            "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
        );

        plugin.getLogger().info("Created barrio_upgrades and barrio_upgrade_counts tables if they didn't exist.");
    }

    /**
     * Get upgrade settings for a barrio, loading from database if necessary.
     *
     * @param barrioId The barrio ID
     * @return The upgrade settings
     */
    public BarrioUpgradeSettings getSettings(String barrioId) {
        updateActivity(barrioId);
        boolean alreadyLoaded = loadedSettings.containsKey(barrioId);
        BarrioUpgradeSettings settings = loadedSettings.computeIfAbsent(barrioId, this::loadSettingsFromDb);
        if (!alreadyLoaded) {
            plugin.getLogger().info("Loaded upgrade settings for barrio: " + barrioId);
        }
        return settings;
    }

    /**
     * Load settings from database for a specific barrio.
     *
     * @param barrioId The barrio ID
     * @return The loaded settings
     */
    private BarrioUpgradeSettings loadSettingsFromDb(String barrioId) {
        // Load upgrade levels
        Map<Material, Integer> blockUpgrades = new HashMap<>();
        Map<EntityType, Integer> entityUpgrades = new HashMap<>();

        plugin.getDatabase().executeQuery(
            "SELECT * FROM barrio_upgrades WHERE barrio_id = ?",
            rs -> {
                while (rs.next()) {
                    String upgradeType = rs.getString("upgrade_type");
                    String upgradeTarget = rs.getString("upgrade_target");
                    int upgradeLevel = rs.getInt("upgrade_level");

                    if ("BLOCK".equals(upgradeType)) {
                        try {
                            Material material = Material.valueOf(upgradeTarget);
                            blockUpgrades.put(material, upgradeLevel);
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid material in database: " + upgradeTarget);
                        }
                    } else if ("ENTITY".equals(upgradeType)) {
                        try {
                            EntityType entityType = EntityType.valueOf(upgradeTarget);
                            entityUpgrades.put(entityType, upgradeLevel);
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid entity type in database: " + upgradeTarget);
                        }
                    }
                }
                return null; // We don't need to return anything here
            },
            barrioId
        );

        // Load block/entity counts
        Map<Material, Integer> blockCounts = new HashMap<>();
        Map<EntityType, Integer> entityCounts = new HashMap<>();

        plugin.getDatabase().executeQuery(
            "SELECT * FROM barrio_upgrade_counts WHERE barrio_id = ?",
            rs -> {
                while (rs.next()) {
                    String countType = rs.getString("count_type");
                    String countTarget = rs.getString("count_target");
                    int countValue = rs.getInt("count_value");

                    if ("BLOCK".equals(countType)) {
                        try {
                            Material material = Material.valueOf(countTarget);
                            blockCounts.put(material, countValue);
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid material in counts database: " + countTarget);
                        }
                    } else if ("ENTITY".equals(countType)) {
                        try {
                            EntityType entityType = EntityType.valueOf(countTarget);
                            entityCounts.put(entityType, countValue);
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid entity type in counts database: " + countTarget);
                        }
                    }
                }
                return null; // We don't need to return anything here
            },
            barrioId
        );

        return new BarrioUpgradeSettings(barrioId, blockUpgrades, entityUpgrades, blockCounts, entityCounts);
    }

    /**
     * Save upgrade settings to database.
     *
     * @param settings The settings to save
     */
    public void saveSettings(BarrioUpgradeSettings settings) {
        if (settings == null) return;

        // Update cache immediately
        loadedSettings.put(settings.getBarrioId(), settings);

        // Save to DB asynchronously only if dirty
        if (settings.isDirty()) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    saveSettingsToDb(settings);
                    settings.markClean(); // Mark as clean after saving
                }
            }.runTaskAsynchronously(plugin);
        }
    }

    /**
     * Save settings to database (internal method).
     *
     * @param settings The settings to save
     */
    private void saveSettingsToDb(BarrioUpgradeSettings settings) {
        // Check if barrio exists
        boolean barrioExists = plugin.getDatabase().executeQuery(
            "SELECT 1 FROM barrios WHERE id = ?",
            rs -> {
                try {
                    return rs.next();
                } catch (SQLException e) {
                    plugin.getLogger().log(Level.SEVERE, "Error checking if barrio exists", e);
                    return false;
                }
            },
            settings.getBarrioId()
        );

        if (!barrioExists) {
            plugin.getLogger().warning("Cannot save upgrade settings for barrio " + settings.getBarrioId() + ": Barrio does not exist");
            return;
        }

        // Clear existing upgrades for this barrio
        plugin.getDatabase().executeUpdate(
            "DELETE FROM barrio_upgrades WHERE barrio_id = ?",
            settings.getBarrioId()
        );

        // Clear existing counts for this barrio
        plugin.getDatabase().executeUpdate(
            "DELETE FROM barrio_upgrade_counts WHERE barrio_id = ?",
            settings.getBarrioId()
        );

        // Save block upgrades
        for (Map.Entry<Material, Integer> entry : settings.getBlockUpgradeLevels().entrySet()) {
            plugin.getDatabase().executeUpdate(
                "INSERT INTO barrio_upgrades (barrio_id, upgrade_type, upgrade_target, upgrade_level) VALUES (?, ?, ?, ?)",
                settings.getBarrioId(), "BLOCK", entry.getKey().name(), entry.getValue()
            );
        }

        // Save entity upgrades
        for (Map.Entry<EntityType, Integer> entry : settings.getEntityUpgradeLevels().entrySet()) {
            plugin.getDatabase().executeUpdate(
                "INSERT INTO barrio_upgrades (barrio_id, upgrade_type, upgrade_target, upgrade_level) VALUES (?, ?, ?, ?)",
                settings.getBarrioId(), "ENTITY", entry.getKey().name(), entry.getValue()
            );
        }

        // Save block counts
        for (Map.Entry<Material, Integer> entry : settings.getBlockCounts().entrySet()) {
            if (entry.getValue() > 0) { // Only save non-zero counts
                plugin.getDatabase().executeUpdate(
                    "INSERT INTO barrio_upgrade_counts (barrio_id, count_type, count_target, count_value) VALUES (?, ?, ?, ?)",
                    settings.getBarrioId(), "BLOCK", entry.getKey().name(), entry.getValue()
                );
            }
        }

        // Save entity counts
        for (Map.Entry<EntityType, Integer> entry : settings.getEntityCounts().entrySet()) {
            if (entry.getValue() > 0) { // Only save non-zero counts
                plugin.getDatabase().executeUpdate(
                    "INSERT INTO barrio_upgrade_counts (barrio_id, count_type, count_target, count_value) VALUES (?, ?, ?, ?)",
                    settings.getBarrioId(), "ENTITY", entry.getKey().name(), entry.getValue()
                );
            }
        }

        plugin.getLogger().info("Saved upgrade settings and counts for barrio: " + settings.getBarrioId());
    }

    /**
     * Update activity timestamp for a barrio.
     *
     * @param barrioId The barrio ID
     */
    public void updateActivity(String barrioId) {
        if (barrioId != null) {
            lastActivity.put(barrioId, System.currentTimeMillis());
        }
    }

    /**
     * Start the inactivity check task to unload unused settings.
     */
    private void startInactivityCheck() {
        inactivityCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                unloadInactiveBarrios();
                saveDirtySettings(); // Also save any dirty settings periodically
            }
        }.runTaskTimer(plugin, 1200L, 1200L); // Check every minute
    }

    /**
     * Stop the inactivity check task.
     */
    public void stopInactivityCheck() {
        if (inactivityCheckTask != null && !inactivityCheckTask.isCancelled()) {
            inactivityCheckTask.cancel();
        }
    }

    /**
     * Shutdown method to save all settings before plugin disable.
     */
    public void shutdown() {
        stopInactivityCheck();
        // Save all dirty settings synchronously before shutdown
        for (BarrioUpgradeSettings settings : loadedSettings.values()) {
            if (settings.isDirty()) {
                saveSettingsToDb(settings);
                settings.markClean();
            }
        }
        plugin.getLogger().info("Saved all upgrade settings during shutdown.");
    }

    /**
     * Unload settings for inactive barrios.
     */
    private void unloadInactiveBarrios() {
        long now = System.currentTimeMillis();
        
        // Get barrios with active players
        Map<String, Integer> barriosWithPlayers = new HashMap<>();
        for (World world : Bukkit.getWorlds()) {
            if (world.getName().startsWith("Barrios/")) {
                String barrioId = world.getName().split("/")[1];
                int playerCount = world.getPlayers().size();
                if (playerCount > 0) {
                    barriosWithPlayers.put(barrioId, playerCount);
                }
            }
        }

        // Find barrios to unload
        List<String> barriosToRemove = new ArrayList<>();
        lastActivity.forEach((barrioId, lastSeen) -> {
            if (now - lastSeen > inactivityTimeoutMillis) {
                if (!barriosWithPlayers.containsKey(barrioId)) {
                    plugin.getLogger().info("Unloading inactive upgrade settings for barrio: " + barrioId);
                    barriosToRemove.add(barrioId);
                }
            }
        });

        // Remove inactive barrios
        for (String barrioId : barriosToRemove) {
            loadedSettings.remove(barrioId);
            lastActivity.remove(barrioId);
        }
    }

    /**
     * Save all dirty settings to database.
     */
    private void saveDirtySettings() {
        for (BarrioUpgradeSettings settings : loadedSettings.values()) {
            if (settings.isDirty()) {
                saveSettings(settings);
            }
        }
    }

    /**
     * Get the barrio ID from a world name.
     *
     * @param world The world
     * @return The barrio ID or null if not a barrio world
     */
    public static String getBarrioIdFromWorld(World world) {
        if (world == null) return null;
        String worldName = world.getName();
        if (!worldName.startsWith("Barrios/")) return null;
        String[] parts = worldName.split("/");
        return parts.length >= 2 ? parts[1] : null;
    }

    /**
     * Check if economy is available.
     *
     * @return true if economy is available
     */
    public boolean isEconomyAvailable() {
        return economy != null;
    }

    /**
     * Purchase an upgrade for a player.
     *
     * @param player The player making the purchase
     * @param barrioId The barrio ID
     * @param upgradeType The type of upgrade
     * @param target The target (Material or EntityType name)
     * @param cost The cost of the upgrade
     * @return true if purchase was successful
     */
    public boolean purchaseUpgrade(OfflinePlayer player, String barrioId, BarrioUpgradeType upgradeType, String target, double cost) {
        if (!isEconomyAvailable()) {
            return false;
        }

        if (economy.getBalance(player) < cost) {
            return false;
        }

        if (!economy.withdrawPlayer(player, cost).transactionSuccess()) {
            return false;
        }

        // Apply the upgrade
        BarrioUpgradeSettings settings = getSettings(barrioId);
        
        if (upgradeType == BarrioUpgradeType.BLOCK) {
            try {
                Material material = Material.valueOf(target);
                int currentLevel = settings.getBlockUpgradeLevel(material);
                settings.setBlockUpgradeLevel(material, currentLevel + 1);
            } catch (IllegalArgumentException e) {
                // Refund if invalid material
                economy.depositPlayer(player, cost);
                return false;
            }
        } else if (upgradeType == BarrioUpgradeType.ENTITY) {
            try {
                EntityType entityType = EntityType.valueOf(target);
                int currentLevel = settings.getEntityUpgradeLevel(entityType);
                settings.setEntityUpgradeLevel(entityType, currentLevel + 1);
            } catch (IllegalArgumentException e) {
                // Refund if invalid entity type
                economy.depositPlayer(player, cost);
                return false;
            }
        }

        saveSettings(settings);
        return true;
    }
}
