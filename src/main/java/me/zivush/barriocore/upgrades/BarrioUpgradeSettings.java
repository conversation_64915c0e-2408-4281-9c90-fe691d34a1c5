package me.zivush.barriocore.upgrades;

import org.bukkit.Material;
import org.bukkit.entity.EntityType;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Represents the upgrade settings for a specific barrio.
 * Stores the current levels for both block and entity upgrades.
 * Also tracks actual block counts in memory for performance.
 */
public class BarrioUpgradeSettings {
    private final String barrioId;
    private final Map<Material, Integer> blockUpgradeLevels;
    private final Map<EntityType, Integer> entityUpgradeLevels;

    // Memory-based block count tracking
    private final Map<Material, Integer> blockCounts;
    private final Map<EntityType, Integer> entityCounts;
    private volatile boolean dirty = false;

    /**
     * Constructor for creating upgrade settings.
     *
     * @param barrioId The ID of the barrio
     * @param blockUpgradeLevels Map of block materials to their upgrade levels
     * @param entityUpgradeLevels Map of entity types to their upgrade levels
     */
    public BarrioUpgradeSettings(String barrioId, Map<Material, Integer> blockUpgradeLevels, 
                                Map<EntityType, Integer> entityUpgradeLevels) {
        this.barrioId = barrioId;
        this.blockUpgradeLevels = new HashMap<>(blockUpgradeLevels);
        this.entityUpgradeLevels = new HashMap<>(entityUpgradeLevels);
    }

    /**
     * Default constructor with empty upgrade levels.
     *
     * @param barrioId The ID of the barrio
     */
    public BarrioUpgradeSettings(String barrioId) {
        this.barrioId = barrioId;
        this.blockUpgradeLevels = new HashMap<>();
        this.entityUpgradeLevels = new HashMap<>();
    }

    // --- Getters ---
    public String getBarrioId() {
        return barrioId;
    }

    public Map<Material, Integer> getBlockUpgradeLevels() {
        return new HashMap<>(blockUpgradeLevels);
    }

    public Map<EntityType, Integer> getEntityUpgradeLevels() {
        return new HashMap<>(entityUpgradeLevels);
    }

    /**
     * Get the upgrade level for a specific block material.
     *
     * @param material The block material
     * @return The upgrade level (0 if not upgraded)
     */
    public int getBlockUpgradeLevel(Material material) {
        return blockUpgradeLevels.getOrDefault(material, 0);
    }

    /**
     * Get the upgrade level for a specific entity type.
     *
     * @param entityType The entity type
     * @return The upgrade level (0 if not upgraded)
     */
    public int getEntityUpgradeLevel(EntityType entityType) {
        return entityUpgradeLevels.getOrDefault(entityType, 0);
    }

    // --- Setters ---
    /**
     * Set the upgrade level for a specific block material.
     *
     * @param material The block material
     * @param level The upgrade level
     */
    public void setBlockUpgradeLevel(Material material, int level) {
        if (level <= 0) {
            blockUpgradeLevels.remove(material);
        } else {
            blockUpgradeLevels.put(material, level);
        }
    }

    /**
     * Set the upgrade level for a specific entity type.
     *
     * @param entityType The entity type
     * @param level The upgrade level
     */
    public void setEntityUpgradeLevel(EntityType entityType, int level) {
        if (level <= 0) {
            entityUpgradeLevels.remove(entityType);
        } else {
            entityUpgradeLevels.put(entityType, level);
        }
    }

    /**
     * Check if a block material has any upgrades.
     *
     * @param material The block material
     * @return true if the material has upgrades
     */
    public boolean hasBlockUpgrade(Material material) {
        return blockUpgradeLevels.containsKey(material);
    }

    /**
     * Check if an entity type has any upgrades.
     *
     * @param entityType The entity type
     * @return true if the entity type has upgrades
     */
    public boolean hasEntityUpgrade(EntityType entityType) {
        return entityUpgradeLevels.containsKey(entityType);
    }
}
