package me.zivush.barriocore;

import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages barrio data, including loading/saving to the database.
 * Implements memory-efficient handling of barrio data with periodic saving.
 */
public class BarrioManager {
    private final BarrioCore plugin;
    private final Database database;

    // In-memory storage for barrio data
    private final Map<String, BarrioData> loadedBarrios = new ConcurrentHashMap<>();

    // Task for periodic saving
    private BukkitTask saveTask;

    // Save interval in ticks (5 minutes = 6000 ticks)
    private final long saveIntervalTicks;

    /**
     * Constructor for the BarrioManager.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public BarrioManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();
        this.saveIntervalTicks = plugin.getConfig().getLong("barrio.save_interval_minutes", 5) * 60 * 20; // minutes -> ticks
        plugin.debugInfo("BarrioManager initialized.");
    }

    /**
     * Initializes the manager by loading all barrios and starting the save task.
     */
    public void initialize() {
        loadAllBarrios();
        startPeriodicSaving();
        plugin.debugInfo("BarrioManager tasks started.");
    }

    /**
     * Determines if a barrio is template-based by checking its world structure.
     *
     * @param barrioId The ID of the barrio
     * @return The template name if it's a template-based barrio, null if it's a regular barrio or not found
     */
    public String getBarrioTemplateName(String barrioId) {
        if (barrioId == null) return null;

        java.io.File barrioDir = new java.io.File(Bukkit.getWorldContainer(), "Barrios/" + barrioId);
        if (!barrioDir.exists()) return null;

        java.io.File[] worlds = barrioDir.listFiles();
        if (worlds == null || worlds.length == 0) return null;

        // Check if it's a regular barrio (has multiple worlds) or template (single world)
        boolean isRegular = java.util.Arrays.stream(worlds)
                .anyMatch(file -> file.getName().equals("world_nether") || file.getName().equals("world_the_end"));

        if (isRegular) {
            return null; // Regular barrio
        } else {
            // Template-based barrio - return the template name (directory name)
            for (java.io.File worldDir : worlds) {
                if (worldDir.isDirectory() && !worldDir.getName().equals("world")) {
                    return worldDir.getName();
                }
            }
        }

        return null; // Default to regular if we can't determine
    }

    /**
     * Shuts down the manager by stopping tasks and saving data.
     */
    public void shutdown() {
        plugin.debugInfo("BarrioManager shutting down...");

        if (saveTask != null) {
            saveTask.cancel();
            plugin.debugInfo("Periodic save task cancelled.");
        }

        plugin.debugInfo("Performing final save of all barrio data...");
        saveAllBarrios(); // Final save on shutdown
        plugin.debugInfo("BarrioManager shutdown complete.");
    }

    /**
     * Starts the periodic saving task.
     */
    private void startPeriodicSaving() {
        if (saveIntervalTicks <= 0) {
            plugin.getLogger().warning("Barrio periodic saving is disabled (interval <= 0).");
            return;
        }

        saveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::saveAllBarrios, saveIntervalTicks, saveIntervalTicks);
        plugin.debugInfo("Barrio periodic save task started (Interval: " + (saveIntervalTicks / 20.0) + " seconds).");
    }

    /**
     * Loads all barrios from the database into memory.
     */
    public void loadAllBarrios() {
        plugin.debugInfo("Loading all barrios from database...");

        try {
            database.executeQuery(
                "SELECT id, uuid, creation_time, nickname FROM barrios",
                rs -> {
                    int count = 0;
                    while (rs.next()) {
                        String id = rs.getString("id");
                        String uuidStr = rs.getString("uuid");
                        long creationTime = rs.getLong("creation_time");
                        String nickname = rs.getString("nickname");

                        UUID ownerUuid = null;
                        try {
                            if (uuidStr != null && !uuidStr.isEmpty()) {
                                ownerUuid = UUID.fromString(uuidStr);
                            }
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid UUID for barrio " + id + ": " + uuidStr);
                        }

                        BarrioData barrioData = new BarrioData(id, ownerUuid, creationTime, nickname);
                        loadedBarrios.put(id, barrioData);
                        count++;
                    }
                    plugin.debugInfo("Loaded " + count + " barrios from database.");
                    return null;
                }
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error loading barrios from database", e);
        }
    }

    /**
     * Saves all barrios to the database.
     */
    public void saveAllBarrios() {
        plugin.debugInfo("Saving all barrios to database...");

        for (Map.Entry<String, BarrioData> entry : loadedBarrios.entrySet()) {
            String barrioId = entry.getKey();
            BarrioData barrioData = entry.getValue();

            try {
                database.executeUpdate(
                    "UPDATE barrios SET uuid = ?, creation_time = ?, nickname = ? WHERE id = ?",
                    barrioData.getOwnerUuid() != null ? barrioData.getOwnerUuid().toString() : null,
                    barrioData.getCreationTime(),
                    barrioData.getNickname(),
                    barrioId
                );
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error saving barrio " + barrioId + " to database", e);
            }
        }

        plugin.debugInfo("Finished saving all barrios to database.");
    }

    /**
     * Creates a new barrio and saves it directly to the database.
     *
     * @param id The ID of the barrio
     * @param ownerUuid The UUID of the owner
     * @param nickname The nickname of the barrio
     * @return The created BarrioData object
     */
    public BarrioData createBarrio(String id, UUID ownerUuid, String nickname) {
        long creationTime = System.currentTimeMillis();
        BarrioData barrioData = new BarrioData(id, ownerUuid, creationTime, nickname);

        // Save to memory
        loadedBarrios.put(id, barrioData);

        // Save directly to database
        try {
            database.executeUpdate(
                "INSERT INTO barrios (id, uuid, creation_time, nickname) VALUES (?, ?, ?, ?)",
                id,
                ownerUuid != null ? ownerUuid.toString() : null,
                creationTime,
                nickname
            );
            plugin.debugInfo("Created new barrio " + id + " for " + ownerUuid);
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error creating barrio " + id + " in database", e);
        }

        return barrioData;
    }

    /**
     * Deletes a barrio from memory and database.
     *
     * @param barrioId The ID of the barrio to delete
     */
    public void deleteBarrio(String barrioId) {
        if (barrioId == null) return;

        // Remove from memory
        loadedBarrios.remove(barrioId);

        // Delete from database
        try {
            database.executeUpdate("DELETE FROM barrios WHERE id = ?", barrioId);
            plugin.debugInfo("Deleted barrio " + barrioId + " from database");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error deleting barrio " + barrioId + " from database", e);
        }
    }

    /**
     * Updates the owner of a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param newOwnerUuid The UUID of the new owner
     */
    public void updateBarrioOwner(String barrioId, UUID newOwnerUuid) {
        if (barrioId == null) return;

        // Update in memory
        BarrioData barrioData = loadedBarrios.get(barrioId);
        if (barrioData != null) {
            barrioData.setOwnerUuid(newOwnerUuid);
        }

        // Update in database directly
        try {
            database.executeUpdate(
                "UPDATE barrios SET uuid = ? WHERE id = ?",
                newOwnerUuid != null ? newOwnerUuid.toString() : null,
                barrioId
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error updating owner for barrio " + barrioId + " in database", e);
        }
    }

    /**
     * Updates the nickname of a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param nickname The new nickname
     */
    public void updateBarrioNickname(String barrioId, String nickname) {
        if (barrioId == null) return;

        // Update in memory
        BarrioData barrioData = loadedBarrios.get(barrioId);
        if (barrioData != null) {
            barrioData.setNickname(nickname);
        }

        // We don't update the database immediately for nicknames
        // It will be saved during the next periodic save or server shutdown
    }

    /**
     * Gets the nickname of a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The nickname, or null if not found
     */
    public String getBarrioNickname(String barrioId) {
        if (barrioId == null) return null;

        BarrioData barrioData = loadedBarrios.get(barrioId);
        return barrioData != null ? barrioData.getNickname() : null;
    }

    /**
     * Gets the owner UUID of a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The owner UUID, or null if not found
     */
    public UUID getBarrioOwner(String barrioId) {
        if (barrioId == null) return null;

        BarrioData barrioData = loadedBarrios.get(barrioId);
        return barrioData != null ? barrioData.getOwnerUuid() : null;
    }

    /**
     * Gets the creation time of a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The creation time, or 0 if not found
     */
    public long getBarrioCreationTime(String barrioId) {
        if (barrioId == null) return 0;

        BarrioData barrioData = loadedBarrios.get(barrioId);
        return barrioData != null ? barrioData.getCreationTime() : 0;
    }

    /**
     * Gets all loaded barrio IDs.
     *
     * @return A set of all barrio IDs
     */
    public java.util.Set<String> getAllBarrioIds() {
        return loadedBarrios.keySet();
    }

    /**
     * Gets the BarrioData object for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The BarrioData object, or null if not found
     */
    public BarrioData getBarrioData(String barrioId) {
        return loadedBarrios.get(barrioId);
    }

    /**
     * Checks if a barrio exists.
     *
     * @param barrioId The ID of the barrio
     * @return True if the barrio exists, false otherwise
     */
    public boolean barrioExists(String barrioId) {
        return loadedBarrios.containsKey(barrioId);
    }

    /**
     * Class to hold barrio data.
     */
    public static class BarrioData {
        private final String id;
        private UUID ownerUuid;
        private final long creationTime;
        private String nickname;

        /**
         * Constructor for BarrioData.
         *
         * @param id The ID of the barrio
         * @param ownerUuid The UUID of the owner
         * @param creationTime The creation time of the barrio
         * @param nickname The nickname of the barrio
         */
        public BarrioData(String id, UUID ownerUuid, long creationTime, String nickname) {
            this.id = id;
            this.ownerUuid = ownerUuid;
            this.creationTime = creationTime;
            this.nickname = nickname;
        }

        /**
         * Gets the ID of the barrio.
         *
         * @return The ID
         */
        public String getId() {
            return id;
        }

        /**
         * Gets the UUID of the owner.
         *
         * @return The owner UUID
         */
        public UUID getOwnerUuid() {
            return ownerUuid;
        }

        /**
         * Sets the UUID of the owner.
         *
         * @param ownerUuid The new owner UUID
         */
        public void setOwnerUuid(UUID ownerUuid) {
            this.ownerUuid = ownerUuid;
        }

        /**
         * Gets the creation time of the barrio.
         *
         * @return The creation time
         */
        public long getCreationTime() {
            return creationTime;
        }

        /**
         * Gets the nickname of the barrio.
         *
         * @return The nickname
         */
        public String getNickname() {
            return nickname;
        }

        /**
         * Sets the nickname of the barrio.
         *
         * @param nickname The new nickname
         */
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
    }
}
