package me.zivush.barriocore.util;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerheads.PlayerHeadManager;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.profile.PlayerProfile;

import java.util.UUID;

/**
 * Utility class for handling player heads
 */
public class HeadUtils {
    private static PlayerHeadManager playerHeadManager;

    /**
     * Sets the PlayerHeadManager instance
     *
     * @param manager The PlayerHeadManager instance
     */
    public static void setPlayerHeadManager(PlayerHeadManager manager) {
        playerHeadManager = manager;
    }

    /**
     * Sets the texture of a player head item
     *
     * @param head The player head item
     * @param player The player whose texture to use
     * @return The modified player head item
     */
    public static ItemStack setSkullOwner(ItemStack head, OfflinePlayer player) {
        if (head == null || !(head.getItemMeta() instanceof SkullMeta) || player == null) {
            return head;
        }

        // If we have a PlayerHeadManager, try to use it first
        if (playerHeadManager != null) {
            UUID playerUuid = player.getUniqueId();
            if (playerHeadManager.hasPlayerHeadTexture(playerUuid)) {
                return playerHeadManager.applyTextureToHead(head, playerUuid);
            }
        }

        // Fall back to standard method using modern Bukkit API
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return head;

        // Try to get the player's profile
        PlayerProfile profile = null;
        if (player.isOnline() && player.getPlayer() != null) {
            profile = player.getPlayer().getPlayerProfile();
        } else {
            profile = Bukkit.createPlayerProfile(player.getUniqueId(), player.getName());
        }

        if (profile != null) {
            meta.setOwnerProfile(profile);
        } else {
            // Fallback to the old method if profile creation fails
            meta.setOwningPlayer(player);
        }

        head.setItemMeta(meta);
        return head;
    }
}
