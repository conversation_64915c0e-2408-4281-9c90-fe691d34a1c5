package me.zivush.barriocore.worldsettings;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import org.bukkit.Bukkit;
import org.bukkit.Difficulty;
import org.bukkit.WeatherType;
import org.bukkit.World;
import org.bukkit.scheduler.BukkitTask;

import java.sql.SQLException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages world settings for barrios, including loading/unloading based on activity.
 */
public class BarrioWorldSettingsManager {
    private final BarrioCore plugin;
    private final Database database;
    private final Map<String, BarrioWorldSettings> loadedSettings = new ConcurrentHashMap<>();
    private final Map<String, Long> lastActivity = new ConcurrentHashMap<>();
    private final Map<String, UUID> barrioOwners = new ConcurrentHashMap<>();
    private final long inactivityUnloadMillis;
    private final long saveIntervalTicks;
    private BukkitTask inactivityCheckTask;
    private BukkitTask saveTask;
    private BarrioWorldSettingsApplier settingsApplier;

    /**
     * Constructor for the BarrioWorldSettingsManager.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public BarrioWorldSettingsManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();
        this.inactivityUnloadMillis = plugin.getConfig().getLong("world_settings.inactivity_unload_minutes", 5) * 60 * 1000;
        this.saveIntervalTicks = plugin.getConfig().getLong("world_settings.save_interval_minutes", 5) * 60 * 20; // minutes -> ticks
        this.settingsApplier = new BarrioWorldSettingsApplier(plugin);
        plugin.getLogger().info("BarrioWorldSettingsManager initialized.");
    }

    /**
     * Initializes the manager by starting the necessary tasks and creating the database table.
     */
    public void initialize() {
        // Create the world settings table if it doesn't exist
        createTable();

        // Start tasks
        startInactivityCheck();
        startPeriodicSaving();
        plugin.getLogger().info("BarrioWorldSettingsManager tasks started.");
    }

    /**
     * Creates the database table for world settings if it doesn't exist.
     */
    private void createTable() {
        try {
            database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_world_settings (" +
                "barrio_id VARCHAR(8) PRIMARY KEY, " +
                "pvp_enabled BOOLEAN NOT NULL DEFAULT FALSE, " +
                "hunger_loss_enabled BOOLEAN NOT NULL DEFAULT TRUE, " +
                "time_mode VARCHAR(16) NOT NULL DEFAULT 'OFF', " +
                "fixed_time BIGINT NOT NULL DEFAULT -1, " +
                "weather_mode VARCHAR(16) NOT NULL DEFAULT 'OFF', " +
                "fixed_weather VARCHAR(16) NOT NULL DEFAULT 'CLEAR', " +
                "difficulty VARCHAR(16) NOT NULL DEFAULT 'NORMAL', " +
                "use_barrio_spawn BOOLEAN NOT NULL DEFAULT TRUE, " +
                "use_server_spawn BOOLEAN NOT NULL DEFAULT FALSE, " +
                "rent_enabled BOOLEAN NOT NULL DEFAULT TRUE, " +
                "barrio_chat_only BOOLEAN NOT NULL DEFAULT FALSE, " +
                "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
            );
            plugin.getLogger().info("Created or verified barrio_world_settings table.");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create barrio_world_settings table", e);
        }
    }

    /**
     * Starts the task that checks for inactive barrio settings and unloads them.
     */
    private void startInactivityCheck() {
        if (inactivityCheckTask != null) {
            inactivityCheckTask.cancel();
        }

        inactivityCheckTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            long currentTime = System.currentTimeMillis();
            for (Map.Entry<String, Long> entry : lastActivity.entrySet()) {
                String barrioId = entry.getKey();
                long lastActiveTime = entry.getValue();

                if (currentTime - lastActiveTime > inactivityUnloadMillis) {
                    // Unload if inactive for too long
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        unloadBarrioWorldSettings(barrioId, true);
                    });
                }
            }
        }, 20 * 60, 20 * 60); // Check every minute
    }

    /**
     * Starts the task that periodically saves all loaded world settings.
     */
    private void startPeriodicSaving() {
        if (saveTask != null) {
            saveTask.cancel();
        }

        saveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            for (String barrioId : loadedSettings.keySet()) {
                saveWorldSettings(barrioId, false);
            }
        }, saveIntervalTicks, saveIntervalTicks);
    }

    /**
     * Updates the last activity time for a barrio.
     * Also starts applying settings if not already doing so.
     *
     * @param barrioId The ID of the barrio
     */
    public void updateActivity(String barrioId) {
        if (barrioId != null) {
            lastActivity.put(barrioId, System.currentTimeMillis());

            // Start applying settings if not already doing so
            if (!loadedSettings.containsKey(barrioId)) {
                // Load settings from database
                loadWorldSettings(barrioId);

                // Load barrio owner if not already loaded
                if (!barrioOwners.containsKey(barrioId)) {
                    loadBarrioInfo(barrioId);
                }
            }

            // Start applying settings
            settingsApplier.startApplyingSettings(barrioId);
        }
    }

    /**
     * Gets the world settings for a specific barrio, loading it if necessary.
     *
     * @param barrioId The ID of the barrio
     * @return The world settings for the barrio
     */
    public BarrioWorldSettings getWorldSettings(String barrioId) {
        if (barrioId == null) return null;

        // Check if already loaded
        BarrioWorldSettings settings = loadedSettings.get(barrioId);
        if (settings != null) {
            // Just update the timestamp, don't reload from database
            lastActivity.put(barrioId, System.currentTimeMillis());
            return settings;
        }

        // Not loaded yet, update activity which will load settings
        updateActivity(barrioId);
        return loadedSettings.get(barrioId);
    }

    /**
     * Loads world settings for a specific barrio from the database.
     *
     * @param barrioId The ID of the barrio
     * @return The loaded world settings, or a new default instance if not found
     */
    private BarrioWorldSettings loadWorldSettings(String barrioId) {
        if (barrioId == null) return null;

        plugin.getLogger().info("Loading world settings for barrio: " + barrioId);

        try {
            BarrioWorldSettings settings = database.executeQuery(
                "SELECT * FROM barrio_world_settings WHERE barrio_id = ?",
                rs -> {
                    if (rs.next()) {
                        // Parse time mode
                        String timeModeStr = rs.getString("time_mode");
                        BarrioWorldSettings.TimeMode timeMode;
                        try {
                            timeMode = BarrioWorldSettings.TimeMode.valueOf(timeModeStr);
                        } catch (IllegalArgumentException e) {
                            timeMode = BarrioWorldSettings.TimeMode.OFF;
                        }

                        // Parse weather mode
                        String weatherModeStr = rs.getString("weather_mode");
                        BarrioWorldSettings.WeatherMode weatherMode;
                        try {
                            weatherMode = BarrioWorldSettings.WeatherMode.valueOf(weatherModeStr);
                        } catch (IllegalArgumentException e) {
                            weatherMode = BarrioWorldSettings.WeatherMode.OFF;
                        }

                        // Parse fixed weather
                        String fixedWeatherStr = rs.getString("fixed_weather");
                        WeatherType fixedWeather;
                        try {
                            fixedWeather = WeatherType.valueOf(fixedWeatherStr);
                        } catch (IllegalArgumentException e) {
                            fixedWeather = WeatherType.CLEAR;
                        }

                        // Parse difficulty
                        String difficultyStr = rs.getString("difficulty");
                        Difficulty difficulty;
                        try {
                            difficulty = Difficulty.valueOf(difficultyStr);
                        } catch (IllegalArgumentException e) {
                            difficulty = Difficulty.NORMAL;
                        }

                        return new BarrioWorldSettings(
                            barrioId,
                            rs.getBoolean("pvp_enabled"),
                            rs.getBoolean("hunger_loss_enabled"),
                            timeMode,
                            rs.getLong("fixed_time"),
                            weatherMode,
                            fixedWeather,
                            difficulty,
                            rs.getBoolean("use_barrio_spawn"),
                            rs.getBoolean("use_server_spawn"),
                            rs.getBoolean("rent_enabled"),
                            rs.getBoolean("barrio_chat_only")
                        );
                    } else {
                        // No settings found, create default
                        return new BarrioWorldSettings(barrioId);
                    }
                },
                barrioId
            );

            // Store in cache
            loadedSettings.put(barrioId, settings);
            return settings;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error loading world settings for barrio: " + barrioId, e);
            // Return default settings
            BarrioWorldSettings defaultSettings = new BarrioWorldSettings(barrioId);
            loadedSettings.put(barrioId, defaultSettings);
            return defaultSettings;
        }
    }

    /**
     * Saves world settings for a specific barrio to the database.
     * This is only called when unloading settings or during periodic saving.
     *
     * @param barrioId The ID of the barrio
     * @param sync Whether to save synchronously
     */
    public void saveWorldSettings(String barrioId, boolean sync) {
        if (barrioId == null) return;

        BarrioWorldSettings settings = loadedSettings.get(barrioId);
        if (settings == null || !settings.isDirty()) {
            return; // Nothing to save
        }

        plugin.getLogger().info("Saving world settings for barrio: " + barrioId + " (sync: " + sync + ")");

        Runnable saveTask = () -> {
            try {
                database.executeUpdate(
                    "INSERT INTO barrio_world_settings " +
                    "(barrio_id, pvp_enabled, hunger_loss_enabled, time_mode, fixed_time, " +
                    "weather_mode, fixed_weather, difficulty, use_barrio_spawn, use_server_spawn, " +
                    "rent_enabled, barrio_chat_only) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                    "ON DUPLICATE KEY UPDATE " +
                    "pvp_enabled = VALUES(pvp_enabled), " +
                    "hunger_loss_enabled = VALUES(hunger_loss_enabled), " +
                    "time_mode = VALUES(time_mode), " +
                    "fixed_time = VALUES(fixed_time), " +
                    "weather_mode = VALUES(weather_mode), " +
                    "fixed_weather = VALUES(fixed_weather), " +
                    "difficulty = VALUES(difficulty), " +
                    "use_barrio_spawn = VALUES(use_barrio_spawn), " +
                    "use_server_spawn = VALUES(use_server_spawn), " +
                    "rent_enabled = VALUES(rent_enabled), " +
                    "barrio_chat_only = VALUES(barrio_chat_only)",
                    settings.getBarrioId(),
                    settings.isPvpEnabled(),
                    settings.isHungerLossEnabled(),
                    settings.getTimeMode().name(),
                    settings.getFixedTime(),
                    settings.getWeatherMode().name(),
                    settings.getFixedWeather().name(),
                    settings.getDifficulty().name(),
                    settings.isUseBarrioSpawn(),
                    settings.isUseServerSpawn(),
                    settings.isRentEnabled(),
                    settings.isBarrioChatOnlyEnabled()
                );
                settings.clearDirty();
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error saving world settings for barrio: " + barrioId, e);
            }
        };

        if (sync) {
            saveTask.run();
        } else {
            Bukkit.getScheduler().runTaskAsynchronously(plugin, saveTask);
        }
    }

    /**
     * Unloads world settings for a barrio, saving it first if requested.
     *
     * @param barrioId The ID of the barrio
     * @param saveFirst Whether to save the settings before unloading
     */
    public void unloadBarrioWorldSettings(String barrioId, boolean saveFirst) {
        if (barrioId == null) return;
        plugin.getLogger().info("Unloading world settings for barrio: " + barrioId + " (Save first: " + saveFirst + ")");
        if (saveFirst) {
            saveWorldSettings(barrioId, true); // Save synchronously
        }

        // Stop applying settings
        settingsApplier.stopApplyingSettings(barrioId);

        loadedSettings.remove(barrioId);
        lastActivity.remove(barrioId);
        barrioOwners.remove(barrioId);
    }

    /**
     * Saves all loaded world settings and shuts down the manager.
     */
    public void shutdown() {
        // Cancel tasks
        if (inactivityCheckTask != null) {
            inactivityCheckTask.cancel();
            inactivityCheckTask = null;
        }
        if (saveTask != null) {
            saveTask.cancel();
            saveTask = null;
        }

        // Shutdown the settings applier
        settingsApplier.shutdown();

        // Save all loaded settings
        for (String barrioId : loadedSettings.keySet()) {
            saveWorldSettings(barrioId, true); // Save synchronously
        }

        // Clear maps
        loadedSettings.clear();
        lastActivity.clear();
        barrioOwners.clear();

        plugin.getLogger().info("BarrioWorldSettingsManager shutdown complete.");
    }

    /**
     * Gets the world from a barrio ID.
     *
     * @param barrioId The ID of the barrio
     * @return The world, or null if not found
     */
    public static World getWorldFromBarrioId(String barrioId) {
        return Bukkit.getWorld("Barrios/" + barrioId + "/world");
    }

    /**
     * Loads barrio information from the database.
     *
     * @param barrioId The ID of the barrio
     */
    private void loadBarrioInfo(String barrioId) {
        try {
            database.executeQuery(
                "SELECT uuid FROM barrios WHERE id = ?",
                rs -> {
                    if (rs.next()) {
                        try {
                            String uuidStr = rs.getString("uuid");
                            if (uuidStr != null && !uuidStr.isEmpty()) {
                                barrioOwners.put(barrioId, UUID.fromString(uuidStr));
                            }
                        } catch (Exception e) {
                            plugin.getLogger().log(Level.SEVERE, "Error parsing barrio info for: " + barrioId, e);
                        }
                    }
                    return null;
                },
                barrioId
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error loading barrio info for: " + barrioId, e);
        }
    }

    /**
     * Gets the owner UUID for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The owner UUID, or null if not found
     */
    public UUID getBarrioOwnerUuid(String barrioId) {
        if (barrioId == null) return null;

        // Check if already loaded
        UUID ownerUuid = barrioOwners.get(barrioId);
        if (ownerUuid != null) {
            return ownerUuid;
        }

        // Load from database
        loadBarrioInfo(barrioId);
        return barrioOwners.get(barrioId);
    }

    /**
     * Gets the nickname for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The nickname, or null if not found
     */
    public String getBarrioNickname(String barrioId) {
        if (barrioId == null) return null;

        // Use the BarrioManager to get the nickname
        return plugin.getBarrioManager().getBarrioNickname(barrioId);
    }

    /**
     * Updates the nickname for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param nickname The new nickname
     */
    public void updateBarrioNickname(String barrioId, String nickname) {
        if (barrioId == null) return;

        // Use the BarrioManager to update the nickname
        plugin.getBarrioManager().updateBarrioNickname(barrioId, nickname);
    }

    /**
     * Counts how many barrios a player owns.
     *
     * @param playerUuid The UUID of the player
     * @return The number of barrios owned by the player
     */
    public int countPlayerBarrios(UUID playerUuid) {
        if (playerUuid == null) return 0;

        try {
            return database.executeQuery(
                "SELECT COUNT(*) as count FROM barrios WHERE uuid = ?",
                rs -> {
                    if (rs.next()) {
                        return rs.getInt("count");
                    }
                    return 0;
                },
                playerUuid.toString()
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error counting barrios for player: " + playerUuid, e);
            return 0;
        }
    }

    /**
     * Creates a default nickname for a new barrio based on the player's name and barrio count.
     *
     * @param playerUuid The UUID of the player
     * @return A default nickname for the barrio
     */
    public String createDefaultNickname(UUID playerUuid) {
        if (playerUuid == null) return "Unknown_1";

        String playerName = Bukkit.getOfflinePlayer(playerUuid).getName();
        if (playerName == null) playerName = "Unknown";

        // Count existing barrios and add 1 for the new one
        int count = countPlayerBarrios(playerUuid) + 1;

        return playerName + "_" + count;
    }
}
