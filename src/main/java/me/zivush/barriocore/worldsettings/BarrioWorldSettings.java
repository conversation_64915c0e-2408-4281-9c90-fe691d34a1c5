package me.zivush.barriocore.worldsettings;

import org.bukkit.Difficulty;
import org.bukkit.WeatherType;

/**
 * Represents world settings for a specific barrio.
 * This includes PVP, hunger, time, weather, difficulty, and respawn settings.
 */
public class BarrioWorldSettings {
    private final String barrioId;
    private boolean pvpEnabled;
    private boolean hungerLossEnabled;

    // Time settings
    private TimeMode timeMode;
    private long fixedTime;

    // Weather settings
    private WeatherMode weatherMode;
    private WeatherType fixedWeather;

    // Difficulty settings
    private Difficulty difficulty;

    // Respawn settings
    private boolean useBarrioSpawn;
    private boolean useServerSpawn;

    // Rent settings
    private boolean rentEnabled;

    // Chat settings
    private boolean barrioChatOnly;

    private boolean dirty; // Flag to track if data has been modified and needs saving

    /**
     * Enum for time mode settings
     */
    public enum TimeMode {
        MORNING(1000),
        NOON(6000),
        NIGHT(13000),
        MIDNIGHT(18000),
        OFF(-1);

        private final long ticks;

        TimeMode(long ticks) {
            this.ticks = ticks;
        }

        public long getTicks() {
            return ticks;
        }
    }

    /**
     * Enum for weather mode settings
     */
    public enum WeatherMode {
        CLEAR,
        STORM,
        RAIN,
        OFF
    }

    /**
     * Constructor for creating a new world settings entry.
     *
     * @param barrioId The ID of the barrio
     */
    public BarrioWorldSettings(String barrioId) {
        this.barrioId = barrioId;
        this.pvpEnabled = false;
        this.hungerLossEnabled = true;
        this.timeMode = TimeMode.OFF;
        this.fixedTime = -1;
        this.weatherMode = WeatherMode.OFF;
        this.fixedWeather = WeatherType.CLEAR;
        this.difficulty = Difficulty.NORMAL;
        this.useBarrioSpawn = true;
        this.useServerSpawn = false;
        this.rentEnabled = true;
        this.barrioChatOnly = false;
        this.dirty = false;
    }

    /**
     * Constructor for creating a world settings entry with specific values.
     */
    public BarrioWorldSettings(String barrioId, boolean pvpEnabled, boolean hungerLossEnabled,
                              TimeMode timeMode, long fixedTime, WeatherMode weatherMode,
                              WeatherType fixedWeather, Difficulty difficulty,
                              boolean useBarrioSpawn, boolean useServerSpawn,
                              boolean rentEnabled, boolean barrioChatOnly) {
        this.barrioId = barrioId;
        this.pvpEnabled = pvpEnabled;
        this.hungerLossEnabled = hungerLossEnabled;
        this.timeMode = timeMode;
        this.fixedTime = fixedTime;
        this.weatherMode = weatherMode;
        this.fixedWeather = fixedWeather;
        this.difficulty = difficulty;
        this.useBarrioSpawn = useBarrioSpawn;
        this.useServerSpawn = useServerSpawn;
        this.rentEnabled = rentEnabled;
        this.barrioChatOnly = barrioChatOnly;
        this.dirty = false;
    }

    // Getters
    public String getBarrioId() { return barrioId; }
    public boolean isPvpEnabled() { return pvpEnabled; }
    public boolean isHungerLossEnabled() { return hungerLossEnabled; }
    public TimeMode getTimeMode() { return timeMode; }
    public long getFixedTime() { return fixedTime; }
    public WeatherMode getWeatherMode() { return weatherMode; }
    public WeatherType getFixedWeather() { return fixedWeather; }
    public Difficulty getDifficulty() { return difficulty; }
    public boolean isUseBarrioSpawn() { return useBarrioSpawn; }
    public boolean isUseServerSpawn() { return useServerSpawn; }
    public boolean isRentEnabled() { return rentEnabled; }
    public boolean isBarrioChatOnlyEnabled() { return barrioChatOnly; }
    public boolean isDirty() { return dirty; }

    // Setters (mark as dirty when modified)
    public void setPvpEnabled(boolean pvpEnabled) {
        if (this.pvpEnabled != pvpEnabled) {
            this.pvpEnabled = pvpEnabled;
            this.dirty = true;
        }
    }

    public void setHungerLossEnabled(boolean hungerLossEnabled) {
        if (this.hungerLossEnabled != hungerLossEnabled) {
            this.hungerLossEnabled = hungerLossEnabled;
            this.dirty = true;
        }
    }

    public void setTimeMode(TimeMode timeMode) {
        if (this.timeMode != timeMode) {
            this.timeMode = timeMode;
            if (timeMode != TimeMode.OFF) {
                this.fixedTime = timeMode.getTicks();
            }
            this.dirty = true;
        }
    }

    public void setFixedTime(long fixedTime) {
        if (this.fixedTime != fixedTime) {
            this.fixedTime = fixedTime;
            this.dirty = true;
        }
    }

    public void setWeatherMode(WeatherMode weatherMode) {
        if (this.weatherMode != weatherMode) {
            this.weatherMode = weatherMode;
            this.dirty = true;
        }
    }

    public void setFixedWeather(WeatherType fixedWeather) {
        if (this.fixedWeather != fixedWeather) {
            this.fixedWeather = fixedWeather;
            this.dirty = true;
        }
    }

    public void setDifficulty(Difficulty difficulty) {
        if (this.difficulty != difficulty) {
            this.difficulty = difficulty;
            this.dirty = true;
        }
    }

    public void setUseBarrioSpawn(boolean useBarrioSpawn) {
        if (this.useBarrioSpawn != useBarrioSpawn) {
            this.useBarrioSpawn = useBarrioSpawn;
            this.dirty = true;
        }
    }

    public void setUseServerSpawn(boolean useServerSpawn) {
        if (this.useServerSpawn != useServerSpawn) {
            this.useServerSpawn = useServerSpawn;
            this.dirty = true;
        }
    }

    public void setRentEnabled(boolean rentEnabled) {
        if (this.rentEnabled != rentEnabled) {
            this.rentEnabled = rentEnabled;
            this.dirty = true;
        }
    }

    public void setBarrioChatOnly(boolean barrioChatOnly) {
        if (this.barrioChatOnly != barrioChatOnly) {
            this.barrioChatOnly = barrioChatOnly;
            this.dirty = true;
        }
    }

    /**
     * Resets the dirty flag after saving.
     */
    public void clearDirty() {
        this.dirty = false;
    }
}
