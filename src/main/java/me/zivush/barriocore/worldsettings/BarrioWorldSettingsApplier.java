package me.zivush.barriocore.worldsettings;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

/**
 * Applies world settings to Minecraft worlds.
 * This class is responsible for applying the settings to the actual Minecraft world.
 */
public class BarrioWorldSettingsApplier {
    private final BarrioCore plugin;
    private final Map<String, BukkitTask> activeApplierTasks = new HashMap<>();
    private final long applyIntervalTicks;

    /**
     * Constructor for the BarrioWorldSettingsApplier.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public BarrioWorldSettingsApplier(BarrioCore plugin) {
        this.plugin = plugin;
        this.applyIntervalTicks = 20; // Apply every second to ensure settings are maintained
    }

    /**
     * Starts applying settings for a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void startApplyingSettings(String barrioId) {
        if (barrioId == null || activeApplierTasks.containsKey(barrioId)) {
            return;
        }

        plugin.getLogger().info("Starting to apply world settings for barrio: " + barrioId);

        BukkitTask task = Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            applySettings(barrioId);
        }, 0, applyIntervalTicks);

        activeApplierTasks.put(barrioId, task);
    }

    /**
     * Stops applying settings for a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void stopApplyingSettings(String barrioId) {
        if (barrioId == null) {
            return;
        }

        BukkitTask task = activeApplierTasks.remove(barrioId);
        if (task != null) {
            task.cancel();
            plugin.getLogger().info("Stopped applying world settings for barrio: " + barrioId);
        }
    }

    /**
     * Applies the settings to the world.
     *
     * @param barrioId The ID of the barrio
     */
    public void applySettings(String barrioId) {
        if (barrioId == null) {
            return;
        }

        BarrioWorldSettings settings = plugin.getWorldSettingsManager().getWorldSettings(barrioId);
        if (settings == null) {
            return;
        }

        World world = getWorldFromBarrioId(barrioId);
        if (world == null) {
            return;
        }

        try {
            // Apply time settings
            applyTimeSettings(world, settings);

            // Apply weather settings
            applyWeatherSettings(world, settings);

            // Apply difficulty settings
            world.setDifficulty(settings.getDifficulty());

            // Apply PvP settings
            world.setPVP(settings.isPvpEnabled());
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error applying world settings for barrio: " + barrioId, e);
        }
    }

    /**
     * Applies time settings to the world.
     *
     * @param world The world to apply settings to
     * @param settings The settings to apply
     */
    private void applyTimeSettings(World world, BarrioWorldSettings settings) {
        if (settings.getTimeMode() != BarrioWorldSettings.TimeMode.OFF) {
            // If time mode is not OFF, set the time to the fixed time
            world.setTime(settings.getFixedTime());

            // For non-OFF modes, disable the day cycle to keep time fixed
            world.setGameRule(org.bukkit.GameRule.DO_DAYLIGHT_CYCLE, false);
        } else {
            // If time mode is OFF, enable the day cycle to allow natural time changes
            world.setGameRule(org.bukkit.GameRule.DO_DAYLIGHT_CYCLE, true);
        }
    }

    /**
     * Applies weather settings to the world.
     *
     * @param world The world to apply settings to
     * @param settings The settings to apply
     */
    private void applyWeatherSettings(World world, BarrioWorldSettings settings) {
        if (settings.getWeatherMode() != BarrioWorldSettings.WeatherMode.OFF) {
            // If weather mode is not OFF, set the weather according to the mode
            switch (settings.getWeatherMode()) {
                case CLEAR:
                    world.setStorm(false);
                    world.setThundering(false);
                    break;
                case RAIN:
                    world.setStorm(true);
                    world.setThundering(false);
                    break;
                case STORM:
                    world.setStorm(true);
                    world.setThundering(true);
                    break;
            }

            // Prevent weather from changing naturally
            world.setGameRule(org.bukkit.GameRule.DO_WEATHER_CYCLE, false);
        } else {
            // If weather mode is OFF, allow weather to change naturally
            world.setGameRule(org.bukkit.GameRule.DO_WEATHER_CYCLE, true);
        }
    }

    /**
     * Gets the world from a barrio ID.
     *
     * @param barrioId The ID of the barrio
     * @return The world, or null if not found
     */
    private World getWorldFromBarrioId(String barrioId) {
        return Bukkit.getWorld("Barrios/" + barrioId + "/world");
    }

    /**
     * Shuts down the applier, cancelling all tasks.
     */
    public void shutdown() {
        for (BukkitTask task : activeApplierTasks.values()) {
            task.cancel();
        }
        activeApplierTasks.clear();
    }
}
