package me.zivush.barriocore.ratings;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.BarrioManager;
import me.zivush.barriocore.permissions.BarrioPermissionManager;
import me.zivush.barriocore.ranks.Rank;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.logging.Level;

/**
 * Manages finding and caching the top-rated barrio.
 */
public class TopRatedBarrioManager {
    private final BarrioCore plugin;
    private final BarrioRatingManager ratingManager;
    private final BarrioManager barrioManager;
    private BarrioPermissionManager permissionManager;

    // Cache for the top-rated barrio
    private String topRatedBarrioId = null;

    // Task for periodic updating
    private BukkitTask updateTask;

    // Update interval in ticks (1 minute = 1200 ticks)
    private final long updateIntervalTicks;

    // Random for tie-breaking
    private final Random random = new Random();

    /**
     * Constructor for the TopRatedBarrioManager.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public TopRatedBarrioManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.ratingManager = plugin.getRatingManager();
        this.barrioManager = plugin.getBarrioManager();
        this.permissionManager = plugin.getPermissionManager();
        this.updateIntervalTicks = 1200; // 1 minute in ticks
    }

    /**
     * Initializes the manager by starting the update task.
     */
    public void initialize() {
        try {
            plugin.getLogger().info("Starting periodic update for top-rated barrio");

            // Make sure we have the latest permissionManager reference
            // This is important because permissionManager might be initialized after this manager
            this.permissionManager = plugin.getPermissionManager();
            if (this.permissionManager == null) {
                plugin.getLogger().warning("PermissionManager is not yet initialized. Will try to get it later.");
            }

            startPeriodicUpdate();
            // Run an immediate update to set the initial top-rated barrio
            updateTopRatedBarrio();
            plugin.getLogger().info("TopRatedBarrioManager initialized successfully");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error initializing TopRatedBarrioManager", e);
        }
    }

    /**
     * Starts the periodic update task.
     */
    private void startPeriodicUpdate() {
        updateTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::updateTopRatedBarrio, 0, updateIntervalTicks);
    }

    /**
     * Stops the periodic update task.
     */
    public void stopPeriodicUpdate() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
    }

    /**
     * Shuts down the manager.
     */
    public void shutdown() {
        stopPeriodicUpdate();
    }

    /**
     * Updates the top-rated barrio.
     */
    public void updateTopRatedBarrio() {
        try {
            plugin.debugInfo("Updating top-rated barrio...");

            // Try to get the permissionManager if it was null before
            if (permissionManager == null) {
                permissionManager = plugin.getPermissionManager();
                if (permissionManager == null) {
                    plugin.getLogger().warning("PermissionManager is still not initialized. Will default visitor permissions to true.");
                } else {
                    plugin.debugInfo("Successfully obtained PermissionManager reference.");
                }
            }

            // Get all barrio IDs
            java.util.Set<String> barrioIds = barrioManager.getAllBarrioIds();
            if (barrioIds.isEmpty()) {
                plugin.getLogger().info("No barrios found");
                topRatedBarrioId = null;
                return;
            }

            // Filter barrios that allow visitors
            List<String> visitorAllowedBarrios = new ArrayList<>();
            for (String barrioId : barrioIds) {
                // Check if visitors are allowed (visit_toggle permission)
                // First try using the permission manager directly
                boolean visitorAllowed = false;

                try {
                    // Check if permissionManager is initialized
                    if (permissionManager != null) {
                        // Create a dummy player to check permissions
                        org.bukkit.entity.Player dummyPlayer = null;
                        // Try to get the default permission value
                        Map<String, Map<Rank, Boolean>> barrioPerms = permissionManager.getBarrioPermissionsForAllRanks(barrioId);
                        if (barrioPerms != null) {
                            Map<Rank, Boolean> visitPerms = barrioPerms.get("visit_toggle");
                            if (visitPerms != null) {
                                Boolean visitorPerm = visitPerms.get(Rank.VISITOR);
                                if (visitorPerm != null) {
                                    visitorAllowed = visitorPerm;
                                    plugin.debugInfo("Barrio " + barrioId + " visitor permission: " + visitorAllowed);
                                } else {
                                    // If no specific permission is set, check the default
                                    visitorAllowed = permissionManager.getDefaultPermission("visit_toggle", Rank.VISITOR);
                                    plugin.debugInfo("Barrio " + barrioId + " using default visitor permission: " + visitorAllowed);
                                }
                            } else {
                                // If no permission entry exists, check the default
                                visitorAllowed = permissionManager.getDefaultPermission("visit_toggle", Rank.VISITOR);
                                plugin.debugInfo("Barrio " + barrioId + " using default visitor permission (no entry): " + visitorAllowed);
                            }
                        } else {
                            // If no permissions exist for this barrio, use the default
                            visitorAllowed = permissionManager.getDefaultPermission("visit_toggle", Rank.VISITOR);
                            plugin.debugInfo("Barrio " + barrioId + " using default visitor permission (no perms): " + visitorAllowed);
                        }
                    } else {
                        // If permissionManager is not initialized yet, default to true
                        plugin.debugInfo("PermissionManager not initialized yet, defaulting visitor permission to true for barrio: " + barrioId);
                        visitorAllowed = true;
                    }
                } catch (Exception e) {
                    plugin.getLogger().log(Level.WARNING, "Error checking visitor permission for barrio " + barrioId, e);
                    // Default to true if there's an error
                    visitorAllowed = true;
                }

                if (visitorAllowed) {
                    visitorAllowedBarrios.add(barrioId);
                }
            }

            plugin.debugInfo("Found " + barrioIds.size() + " total barrios");
            plugin.debugInfo("Found " + visitorAllowedBarrios.size() + " barrios that allow visitors");

            if (visitorAllowedBarrios.isEmpty()) {
                plugin.debugInfo("No barrios allow visitors");
                topRatedBarrioId = null;
                return;
            }

            // Calculate average rating and count for each barrio
            Map<String, Double> averageRatings = new HashMap<>();
            Map<String, Integer> ratingCounts = new HashMap<>();

            for (String barrioId : visitorAllowedBarrios) {
                double avgRating = ratingManager.getAverageRating(barrioId);
                int count = ratingManager.getRatingCount(barrioId);

                // Only consider barrios with at least one rating
                if (count > 0) {
                    averageRatings.put(barrioId, avgRating);
                    ratingCounts.put(barrioId, count);
                }
            }

            plugin.debugInfo("Found " + averageRatings.size() + " barrios with ratings");

            if (averageRatings.isEmpty()) {
                plugin.debugInfo("No barrios have ratings, using first visitor-allowed barrio");
                // If no barrios have ratings, just use the first one that allows visitors
                if (!visitorAllowedBarrios.isEmpty()) {
                    topRatedBarrioId = visitorAllowedBarrios.get(0);
                    plugin.debugInfo("Selected barrio without ratings: " + topRatedBarrioId);
                    return;
                } else {
                    plugin.debugInfo("No barrios allow visitors");
                    topRatedBarrioId = null;
                    return;
                }
            }

            // Find the highest average rating
            double highestAvg = Collections.max(averageRatings.values());

            // Find all barrios with the highest average rating
            List<String> highestRatedBarrios = new ArrayList<>();
            for (Map.Entry<String, Double> entry : averageRatings.entrySet()) {
                if (Math.abs(entry.getValue() - highestAvg) < 0.001) { // Compare with small epsilon for floating point
                    highestRatedBarrios.add(entry.getKey());
                }
            }

            // If there's only one barrio with the highest rating, that's our top-rated barrio
            if (highestRatedBarrios.size() == 1) {
                topRatedBarrioId = highestRatedBarrios.get(0);
                plugin.debugInfo("Top-rated barrio updated: " + topRatedBarrioId + " (avg: " + highestAvg + ")");
                return;
            }

            // If there are multiple barrios with the same highest rating, pick the one with the most reviews
            int highestCount = -1;
            List<String> mostReviewedBarrios = new ArrayList<>();

            for (String barrioId : highestRatedBarrios) {
                int count = ratingCounts.get(barrioId);
                if (count > highestCount) {
                    highestCount = count;
                    mostReviewedBarrios.clear();
                    mostReviewedBarrios.add(barrioId);
                } else if (count == highestCount) {
                    mostReviewedBarrios.add(barrioId);
                }
            }

            // If there's only one barrio with the most reviews, that's our top-rated barrio
            if (mostReviewedBarrios.size() == 1) {
                topRatedBarrioId = mostReviewedBarrios.get(0);
                plugin.debugInfo("Top-rated barrio updated: " + topRatedBarrioId + " (avg: " + highestAvg + ", count: " + highestCount + ")");
                return;
            }

            // If there are still multiple barrios tied, pick one randomly
            topRatedBarrioId = mostReviewedBarrios.get(random.nextInt(mostReviewedBarrios.size()));
            plugin.debugInfo("Top-rated barrio updated (random tie-break): " + topRatedBarrioId + " (avg: " + highestAvg + ", count: " + highestCount + ")");

        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error updating top-rated barrio", e);
        }
    }

    /**
     * Gets the top-rated barrio ID.
     *
     * @return The top-rated barrio ID, or null if none found
     */
    public String getTopRatedBarrioId() {
        return topRatedBarrioId;
    }
}
