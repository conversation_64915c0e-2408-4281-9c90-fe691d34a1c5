package me.zivush.barriocore.ratings;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages barrio ratings, including loading/saving to the database.
 */
public class BarrioRatingManager {
    private final BarrioCore plugin;
    private final Database database;

    // Cache for barrio ratings
    private final Map<String, List<Map<String, Object>>> barrioRatingsCache = new ConcurrentHashMap<>();

    // Task for periodic saving
    private BukkitTask saveTask;

    // Save interval in ticks (5 minutes = 6000 ticks)
    private final long saveIntervalTicks;

    // Cooldown map to track when players can rate again (UUID -> Map<BarrioId, Timestamp>)
    private final Map<UUID, Map<String, Long>> ratingCooldowns = new ConcurrentHashMap<>();

    /**
     * Constructor for the BarrioRatingManager.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public BarrioRatingManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();

        if (this.database == null) {
            plugin.getLogger().severe("Database is null in BarrioRatingManager constructor");
        } else {
            plugin.getLogger().info("Database is not null in BarrioRatingManager constructor");
        }

        this.saveIntervalTicks = plugin.getConfig().getLong("barrio.save_interval_minutes", 5) * 60 * 20; // minutes -> ticks
    }

    /**
     * Initializes the manager by creating the database table and starting the save task.
     */
    public void initialize() {
        try {
            plugin.getLogger().info("Creating barrio_ratings table");
            createTable();
            plugin.getLogger().info("Starting periodic saving for barrio ratings");
            startPeriodicSaving();
            plugin.getLogger().info("BarrioRatingManager initialized successfully");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error initializing BarrioRatingManager", e);
        }
    }

    /**
     * Creates the database table if it doesn't exist.
     */
    private void createTable() {
        try {
            plugin.getLogger().info("Checking if barrios table exists");
            // Check if the barrios table exists first
            boolean barriosTableExists = database.executeQuery(
                "SHOW TABLES LIKE 'barrios'",
                rs -> {
                    try {
                        boolean exists = rs.next();
                        plugin.getLogger().info("Barrios table exists: " + exists);
                        return exists;
                    } catch (SQLException e) {
                        plugin.getLogger().log(Level.SEVERE, "Error checking if barrios table exists", e);
                        return false;
                    }
                }
            );

            if (!barriosTableExists) {
                plugin.getLogger().warning("Cannot create barrio_ratings table: barrios table does not exist yet");
                plugin.getLogger().warning("The barrio_ratings table will be created when the plugin is reloaded after the barrios table is created");
                return;
            }

            plugin.getLogger().info("Creating barrio_ratings table if it doesn't exist");
            database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_ratings (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "barrio_id VARCHAR(8) NOT NULL, " +
                "rater_uuid VARCHAR(36) NOT NULL, " +
                "stars INT NOT NULL, " +
                "message TEXT, " +
                "date BIGINT NOT NULL, " +
                "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE, " +
                "UNIQUE KEY unique_rating (barrio_id, rater_uuid))"
            );
            plugin.getLogger().info("Created barrio_ratings table if it didn't exist.");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error creating barrio_ratings table", e);
            e.printStackTrace();
        }
    }

    /**
     * Starts the periodic saving task.
     */
    private void startPeriodicSaving() {
        saveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::saveAllRatings, saveIntervalTicks, saveIntervalTicks);
    }

    /**
     * Stops the periodic saving task.
     */
    public void stopPeriodicSaving() {
        if (saveTask != null) {
            saveTask.cancel();
            saveTask = null;
        }
    }

    /**
     * Shuts down the manager, saving all ratings.
     */
    public void shutdown() {
        stopPeriodicSaving();
        saveAllRatings();
    }

    /**
     * Saves all ratings to the database.
     */
    public void saveAllRatings() {
        // No need to implement this as we save ratings immediately when they are submitted
    }

    /**
     * Gets all ratings for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return A list of rating data maps
     */
    public List<Map<String, Object>> getRatings(String barrioId) {
        if (barrioId == null) return new ArrayList<>();

        // Check if database is null
        if (database == null) {
            plugin.getLogger().severe("Database is null in getRatings method");
            return new ArrayList<>();
        }

        // Check cache first
        if (barrioRatingsCache.containsKey(barrioId)) {
            return new ArrayList<>(barrioRatingsCache.get(barrioId));
        }

        try {
            plugin.getLogger().info("Checking if barrio exists in getRatings method: " + barrioId);
            // Check if the barrio exists before trying to get ratings
            boolean barrioExists = database.executeQuery(
                "SELECT 1 FROM barrios WHERE id = ?",
                rs -> {
                    try {
                        boolean exists = rs.next();
                        plugin.getLogger().info("Barrio exists in getRatings method: " + exists);
                        return exists;
                    } catch (SQLException e) {
                        plugin.getLogger().log(Level.SEVERE, "Error checking if barrio exists", e);
                        return false;
                    }
                },
                barrioId
            );

            if (!barrioExists) {
                plugin.getLogger().warning("Cannot get ratings for barrio " + barrioId + ": Barrio does not exist");
                return new ArrayList<>();
            }

            // Load from database
            plugin.getLogger().info("Loading ratings from database for barrio: " + barrioId);
            List<Map<String, Object>> ratings = database.executeQuery(
                "SELECT rater_uuid, stars, message, date FROM barrio_ratings WHERE barrio_id = ? ORDER BY date DESC",
                this::processRatingsResultSet,
                barrioId
            );

            // Cache the results
            plugin.getLogger().info("Caching ratings for barrio: " + barrioId + ", count: " + ratings.size());
            barrioRatingsCache.put(barrioId, ratings);

            return ratings;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error loading ratings for barrio " + barrioId, e);
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Processes a ResultSet of ratings into a list of maps.
     *
     * @param rs The ResultSet to process
     * @return A list of rating data maps
     * @throws SQLException If an SQL error occurs
     */
    private List<Map<String, Object>> processRatingsResultSet(ResultSet rs) throws SQLException {
        List<Map<String, Object>> ratings = new ArrayList<>();

        while (rs.next()) {
            Map<String, Object> rating = new HashMap<>();
            rating.put("rater", rs.getString("rater_uuid"));
            rating.put("stars", rs.getInt("stars"));
            rating.put("message", rs.getString("message"));
            rating.put("date", rs.getLong("date"));
            ratings.add(rating);
        }

        return ratings;
    }

    /**
     * Saves a rating for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param raterUuid The UUID of the rater
     * @param stars The number of stars (1-5)
     * @param message The rating message
     * @return true if successful, false otherwise
     */
    public boolean saveRating(String barrioId, UUID raterUuid, int stars, String message) {
        if (barrioId == null || raterUuid == null) return false;

        // Validate stars
        int minRating = plugin.getConfig().getInt("rating.min_rating", 1);
        int maxRating = plugin.getConfig().getInt("rating.max_rating", 5);
        if (stars < minRating || stars > maxRating) {
            return false;
        }

        // Check cooldown
        if (isOnCooldown(barrioId, raterUuid)) {
            return false;
        }

        long currentTime = System.currentTimeMillis();

        try {
            // Check if the barrio exists before trying to save the rating
            boolean barrioExists = database.executeQuery(
                "SELECT 1 FROM barrios WHERE id = ?",
                rs -> {
                    try {
                        return rs.next();
                    } catch (SQLException e) {
                        plugin.getLogger().log(Level.SEVERE, "Error checking if barrio exists", e);
                        return false;
                    }
                },
                barrioId
            );

            if (!barrioExists) {
                plugin.getLogger().warning("Cannot save rating for barrio " + barrioId + ": Barrio does not exist");
                return false;
            }

            // Save to database
            database.executeUpdate(
                "INSERT INTO barrio_ratings (barrio_id, rater_uuid, stars, message, date) " +
                "VALUES (?, ?, ?, ?, ?) " +
                "ON DUPLICATE KEY UPDATE stars = ?, message = ?, date = ?",
                barrioId, raterUuid.toString(), stars, message, currentTime,
                stars, message, currentTime
            );

            // Update cache
            updateRatingCache(barrioId, raterUuid, stars, message, currentTime);

            // Set cooldown
            setRatingCooldown(barrioId, raterUuid);

            return true;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error saving rating for barrio " + barrioId, e);
            return false;
        }
    }

    /**
     * Updates the rating cache for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param raterUuid The UUID of the rater
     * @param stars The number of stars
     * @param message The rating message
     * @param date The rating date
     */
    private void updateRatingCache(String barrioId, UUID raterUuid, int stars, String message, long date) {
        // Get or create the ratings list for this barrio
        List<Map<String, Object>> ratings = barrioRatingsCache.computeIfAbsent(barrioId, k -> new ArrayList<>());

        // Remove any existing rating from this rater
        ratings.removeIf(r -> r.get("rater").equals(raterUuid.toString()));

        // Add the new rating
        Map<String, Object> rating = new HashMap<>();
        rating.put("rater", raterUuid.toString());
        rating.put("stars", stars);
        rating.put("message", message);
        rating.put("date", date);
        ratings.add(rating);

        // Sort by date (newest first)
        ratings.sort((r1, r2) -> Long.compare((Long) r2.get("date"), (Long) r1.get("date")));
    }

    /**
     * Sets a cooldown for a player rating a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param raterUuid The UUID of the rater
     */
    private void setRatingCooldown(String barrioId, UUID raterUuid) {
        long cooldownTime = System.currentTimeMillis() + (plugin.getConfig().getLong("rating.cooldown_hours", 24) * 60 * 60 * 1000);

        // Get or create the cooldown map for this player
        Map<String, Long> playerCooldowns = ratingCooldowns.computeIfAbsent(raterUuid, k -> new HashMap<>());

        // Set the cooldown for this barrio
        playerCooldowns.put(barrioId, cooldownTime);
    }

    /**
     * Checks if a player is on cooldown for rating a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param raterUuid The UUID of the rater
     * @return true if on cooldown, false otherwise
     */
    public boolean isOnCooldown(String barrioId, UUID raterUuid) {
        // Get the cooldown map for this player
        Map<String, Long> playerCooldowns = ratingCooldowns.get(raterUuid);
        if (playerCooldowns == null) return false;

        // Check if there's a cooldown for this barrio
        Long cooldownTime = playerCooldowns.get(barrioId);
        if (cooldownTime == null) return false;

        // Check if the cooldown has expired
        return System.currentTimeMillis() < cooldownTime;
    }

    /**
     * Gets the remaining cooldown time for a player rating a barrio.
     *
     * @param barrioId The ID of the barrio
     * @param raterUuid The UUID of the rater
     * @return The remaining cooldown time in milliseconds, or 0 if not on cooldown
     */
    public long getRemainingCooldown(String barrioId, UUID raterUuid) {
        // Get the cooldown map for this player
        Map<String, Long> playerCooldowns = ratingCooldowns.get(raterUuid);
        if (playerCooldowns == null) return 0;

        // Check if there's a cooldown for this barrio
        Long cooldownTime = playerCooldowns.get(barrioId);
        if (cooldownTime == null) return 0;

        // Calculate remaining time
        long remaining = cooldownTime - System.currentTimeMillis();
        return Math.max(0, remaining);
    }

    /**
     * Gets the average rating for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The average rating, or 0 if no ratings
     */
    public double getAverageRating(String barrioId) {
        List<Map<String, Object>> ratings = getRatings(barrioId);
        if (ratings.isEmpty()) return 0;

        double sum = 0;
        for (Map<String, Object> rating : ratings) {
            sum += (int) rating.get("stars");
        }

        return sum / ratings.size();
    }

    /**
     * Gets the number of ratings for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The number of ratings
     */
    public int getRatingCount(String barrioId) {
        return getRatings(barrioId).size();
    }

    /**
     * Gets the highest rating for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The highest rating, or 0 if no ratings
     */
    public int getHighestRating(String barrioId) {
        List<Map<String, Object>> ratings = getRatings(barrioId);
        if (ratings.isEmpty()) return 0;

        int highest = 0;
        for (Map<String, Object> rating : ratings) {
            int stars = (int) rating.get("stars");
            if (stars > highest) {
                highest = stars;
            }
        }

        return highest;
    }

    /**
     * Gets the lowest rating for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The lowest rating, or 0 if no ratings
     */
    public int getLowestRating(String barrioId) {
        List<Map<String, Object>> ratings = getRatings(barrioId);
        if (ratings.isEmpty()) return 0;

        int lowest = Integer.MAX_VALUE;
        for (Map<String, Object> rating : ratings) {
            int stars = (int) rating.get("stars");
            if (stars < lowest) {
                lowest = stars;
            }
        }

        return lowest == Integer.MAX_VALUE ? 0 : lowest;
    }

    /**
     * Invalidates the cache for a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void invalidateCache(String barrioId) {
        if (barrioId != null) {
            barrioRatingsCache.remove(barrioId);
        }
    }

    /**
     * Clears all caches.
     */
    public void clearAllCaches() {
        barrioRatingsCache.clear();
        ratingCooldowns.clear();
    }
}
