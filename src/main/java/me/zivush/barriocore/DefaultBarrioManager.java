package me.zivush.barriocore;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages player default barrio data, including loading/saving to the database.
 * Implements memory-efficient handling of default barrio data with periodic saving.
 */
public class DefaultBarrioManager {
    private final BarrioCore plugin;
    private final Database database;
    private final BarrioManager barrioManager;
    
    // In-memory storage for player default barrio data
    private final Map<UUID, String> playerDefaultBarrios = new ConcurrentHashMap<>();
    
    // Task for periodic saving
    private BukkitTask saveTask;
    
    // Save interval in ticks (5 minutes = 6000 ticks)
    private final long saveIntervalTicks;
    
    /**
     * Constructor for the DefaultBarrioManager.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public DefaultBarrioManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();
        this.barrioManager = plugin.getBarrioManager();
        this.saveIntervalTicks = plugin.getConfig().getLong("barrio.save_interval_minutes", 5) * 60 * 20; // minutes -> ticks
        plugin.getLogger().info("DefaultBarrioManager initialized.");
    }
    
    /**
     * Initializes the manager by creating the database table and starting the save task.
     */
    public void initialize() {
        createTable();
        startPeriodicSaving();
        plugin.getLogger().info("DefaultBarrioManager tasks started.");
    }
    
    /**
     * Creates the database table if it doesn't exist.
     */
    private void createTable() {
        try {
            database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS player_default_barrios (" +
                "player_uuid VARCHAR(36) PRIMARY KEY, " +
                "barrio_id VARCHAR(8), " +
                "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE SET NULL)"
            );
            plugin.getLogger().info("Created player_default_barrios table if it didn't exist.");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error creating player_default_barrios table", e);
        }
    }
    
    /**
     * Starts the periodic saving task.
     */
    private void startPeriodicSaving() {
        saveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::saveAllDefaultBarrios, saveIntervalTicks, saveIntervalTicks);
        plugin.getLogger().info("Started periodic saving of default barrio data every " + (saveIntervalTicks / 20 / 60) + " minutes.");
    }
    
    /**
     * Stops the periodic saving task.
     */
    public void stopPeriodicSaving() {
        if (saveTask != null && !saveTask.isCancelled()) {
            saveTask.cancel();
            saveTask = null;
            plugin.getLogger().info("Stopped periodic saving of default barrio data.");
        }
    }
    
    /**
     * Loads a player's default barrio from the database.
     *
     * @param playerUuid The UUID of the player
     */
    public void loadPlayerDefaultBarrio(UUID playerUuid) {
        if (playerUuid == null) return;
        
        try {
            database.executeQuery(
                "SELECT barrio_id FROM player_default_barrios WHERE player_uuid = ?",
                rs -> {
                    if (rs.next()) {
                        String barrioId = rs.getString("barrio_id");
                        if (barrioId != null) {
                            playerDefaultBarrios.put(playerUuid, barrioId);
                            plugin.getLogger().fine("Loaded default barrio " + barrioId + " for player " + playerUuid);
                        }
                    }
                    return null;
                },
                playerUuid.toString()
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error loading default barrio for player " + playerUuid, e);
        }
    }
    
    /**
     * Saves all default barrios to the database.
     */
    public void saveAllDefaultBarrios() {
        plugin.getLogger().info("Saving all default barrios to database...");
        
        for (Map.Entry<UUID, String> entry : playerDefaultBarrios.entrySet()) {
            UUID playerUuid = entry.getKey();
            String barrioId = entry.getValue();
            
            try {
                database.executeUpdate(
                    "INSERT INTO player_default_barrios (player_uuid, barrio_id) VALUES (?, ?) " +
                    "ON DUPLICATE KEY UPDATE barrio_id = ?",
                    playerUuid.toString(),
                    barrioId,
                    barrioId
                );
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error saving default barrio for player " + playerUuid, e);
            }
        }
        
        plugin.getLogger().info("Finished saving all default barrios to database.");
    }
    
    /**
     * Saves a player's default barrio to the database.
     *
     * @param playerUuid The UUID of the player
     */
    public void savePlayerDefaultBarrio(UUID playerUuid) {
        if (playerUuid == null) return;
        
        String barrioId = playerDefaultBarrios.get(playerUuid);
        if (barrioId == null) {
            // Remove from database if exists
            try {
                database.executeUpdate(
                    "DELETE FROM player_default_barrios WHERE player_uuid = ?",
                    playerUuid.toString()
                );
            } catch (Exception e) {
                plugin.getLogger().log(Level.SEVERE, "Error removing default barrio for player " + playerUuid, e);
            }
            return;
        }
        
        try {
            database.executeUpdate(
                "INSERT INTO player_default_barrios (player_uuid, barrio_id) VALUES (?, ?) " +
                "ON DUPLICATE KEY UPDATE barrio_id = ?",
                playerUuid.toString(),
                barrioId,
                barrioId
            );
            plugin.getLogger().fine("Saved default barrio " + barrioId + " for player " + playerUuid);
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error saving default barrio for player " + playerUuid, e);
        }
    }
    
    /**
     * Sets a player's default barrio.
     *
     * @param playerUuid The UUID of the player
     * @param barrioId The ID of the barrio
     * @return true if successful, false otherwise
     */
    public boolean setPlayerDefaultBarrio(UUID playerUuid, String barrioId) {
        if (playerUuid == null || barrioId == null) return false;
        
        // Check if barrio exists
        if (barrioManager.getBarrioData(barrioId) == null) {
            plugin.getLogger().warning("Attempted to set default barrio to non-existent barrio " + barrioId + " for player " + playerUuid);
            return false;
        }
        
        // Update in memory
        playerDefaultBarrios.put(playerUuid, barrioId);
        
        // Save to database
        savePlayerDefaultBarrio(playerUuid);
        
        return true;
    }
    
    /**
     * Gets a player's default barrio.
     *
     * @param playerUuid The UUID of the player
     * @return The ID of the default barrio, or null if not set
     */
    public String getPlayerDefaultBarrio(UUID playerUuid) {
        if (playerUuid == null) return null;
        
        String barrioId = playerDefaultBarrios.get(playerUuid);
        
        // If no default barrio is set, try to find the first barrio owned by the player
        if (barrioId == null) {
            barrioId = findFirstPlayerBarrio(playerUuid);
            if (barrioId != null) {
                // Set this as the default barrio
                setPlayerDefaultBarrio(playerUuid, barrioId);
            }
        } else {
            // Verify the barrio still exists
            if (barrioManager.getBarrioData(barrioId) == null) {
                // Default barrio has been deleted, find a new one
                playerDefaultBarrios.remove(playerUuid);
                barrioId = findFirstPlayerBarrio(playerUuid);
                if (barrioId != null) {
                    // Set this as the default barrio
                    setPlayerDefaultBarrio(playerUuid, barrioId);
                }
            }
        }
        
        return barrioId;
    }
    
    /**
     * Finds the first barrio owned by a player.
     *
     * @param playerUuid The UUID of the player
     * @return The ID of the first barrio, or null if none found
     */
    private String findFirstPlayerBarrio(UUID playerUuid) {
        if (playerUuid == null) return null;
        
        for (String barrioId : barrioManager.getAllBarrioIds()) {
            BarrioManager.BarrioData barrioData = barrioManager.getBarrioData(barrioId);
            if (barrioData != null && playerUuid.equals(barrioData.getOwnerUuid())) {
                return barrioId;
            }
        }
        
        return null;
    }
    
    /**
     * Unloads a player's default barrio data.
     *
     * @param playerUuid The UUID of the player
     */
    public void unloadPlayerDefaultBarrio(UUID playerUuid) {
        if (playerUuid == null) return;
        
        // Save before unloading
        savePlayerDefaultBarrio(playerUuid);
        
        // Remove from memory
        playerDefaultBarrios.remove(playerUuid);
        plugin.getLogger().fine("Unloaded default barrio for player " + playerUuid);
    }
    
    /**
     * Handles barrio deletion by updating any players who had it as their default.
     *
     * @param barrioId The ID of the deleted barrio
     */
    public void handleBarrioDeletion(String barrioId) {
        if (barrioId == null) return;
        
        for (Map.Entry<UUID, String> entry : playerDefaultBarrios.entrySet()) {
            if (barrioId.equals(entry.getValue())) {
                UUID playerUuid = entry.getKey();
                // Find a new default barrio for this player
                String newDefaultBarrioId = findFirstPlayerBarrio(playerUuid);
                if (newDefaultBarrioId != null) {
                    // Set the new default barrio
                    setPlayerDefaultBarrio(playerUuid, newDefaultBarrioId);
                    Player player = Bukkit.getPlayer(playerUuid);
                    if (player != null && player.isOnline()) {
                        player.sendMessage(plugin.getMessage("messages.default_barrio_updated"));
                    }
                } else {
                    // No barrios left, remove default
                    playerDefaultBarrios.remove(playerUuid);
                    savePlayerDefaultBarrio(playerUuid);
                }
            }
        }
    }
}
