package me.zivush.barriocore;

import me.zivush.barriocore.chat.BarrioChatManager;
import me.zivush.barriocore.gadgets.BarrioGadgetsManager;
import me.zivush.barriocore.upgrades.BarrioUpgradesManager;
import me.zivush.barriocore.gui.BaseGUI;
import me.zivush.barriocore.gui.GUIListener;
import me.zivush.barriocore.playerheads.PlayerHeadManager;
import me.zivush.barriocore.ratings.BarrioRatingManager;
import me.zivush.barriocore.ratings.TopRatedBarrioManager;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerChatEvent;

import java.util.Collections;
import java.util.function.Consumer;
import me.zivush.barriocore.listeners.BarrioChatListener;
import me.zivush.barriocore.listeners.BarrioGadgetsListener;
import me.zivush.barriocore.listeners.BarrioHungerListener;
import me.zivush.barriocore.listeners.BarrioPermissionsListener;
import me.zivush.barriocore.listeners.BarrioPlayerDataListener;
import me.zivush.barriocore.listeners.BarrioPortalListener;
import me.zivush.barriocore.listeners.BarrioRankListener;
import me.zivush.barriocore.listeners.BarrioRespawnListener;
import me.zivush.barriocore.listeners.BarrioWorldSettingsListener;
import me.zivush.barriocore.listeners.DefaultBarrioListener;
import me.zivush.barriocore.listeners.PlayerHeadListener;
import me.zivush.barriocore.modes.BarrioModeManager;
import me.zivush.barriocore.permissions.BarrioPermissionManager;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import me.zivush.barriocore.ranks.BarrioRankManager;
import me.zivush.barriocore.worldsettings.BarrioWorldSettingsManager;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.logging.Handler;
import java.util.logging.ConsoleHandler;

public class BarrioCore extends JavaPlugin {
    private Database database;
    private FileConfiguration guiConfig;
    private FileConfiguration messagesConfig;
    private Map<UUID, BaseGUI> openGuis = new HashMap<>();
    private BarrioModeManager modeManager;
    private BarrioGadgetsManager gadgetsManager;
    private BarrioUpgradesManager upgradesManager;
    private BarrioPermissionManager permissionManager;
    private BarrioRankManager rankManager;
    private BarrioPlayerDataManager playerDataManager;
    private BarrioWorldSettingsManager worldSettingsManager;
    private BarrioChatManager chatManager;
    private BarrioManager barrioManager;
    private DefaultBarrioManager defaultBarrioManager;
    private BarrioRatingManager ratingManager;
    private TopRatedBarrioManager topRatedBarrioManager;
    private PlayerHeadManager playerHeadManager;

    @Override
    public void onEnable() {
        // Save default config.yml if it doesn't exist
        saveDefaultConfig();

        // Load debug setting
        loadDebugSetting();

        // Load gui.yml
        saveResource("gui.yml", false);
        File guiFile = new File(getDataFolder(), "gui.yml");
        guiConfig = YamlConfiguration.loadConfiguration(guiFile);

        // Load messages.yml
        saveResource("messages.yml", false);
        File messagesFile = new File(getDataFolder(), "messages.yml");
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);

        // Initialize database
        database = new Database(this);

        // Create database table if it doesn't exist
        database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrios (" +
                        "id VARCHAR(8) PRIMARY KEY, " +
                        "uuid VARCHAR(36) NOT NULL, " +
                        "creation_time BIGINT NOT NULL, " +
                        "nickname VARCHAR(64))"
        );

        database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_player_data (" +
                        "barrio_id VARCHAR(8), " +
                        "player_uuid VARCHAR(36), " +
                        "player_rank VARCHAR(16) NOT NULL DEFAULT 'VISITOR', " +
                        "is_banned BOOLEAN NOT NULL DEFAULT FALSE, " +
                        "ban_date BIGINT NOT NULL DEFAULT 0, " +
                        "first_join_time BIGINT NOT NULL, " +
                        "last_location_world VARCHAR(64), " +
                        "last_location_x DOUBLE NOT NULL DEFAULT 0, " +
                        "last_location_y DOUBLE NOT NULL DEFAULT 0, " +
                        "last_location_z DOUBLE NOT NULL DEFAULT 0, " +
                        "PRIMARY KEY (barrio_id, player_uuid), " +
                        "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
        );

        database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_gadgets (" +
                        "barrio_id VARCHAR(8) PRIMARY KEY, " +
                        "toggle_creeper_damage BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "toggle_creeper_block_damage BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "toggle_ghast_damage BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "toggle_ghast_block_damage BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "toggle_fire_spread BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "toggle_enderman_grief BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "toggle_ravager_grief BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "show_entry_title BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "show_entry_chat BOOLEAN NOT NULL DEFAULT TRUE, " +
                        "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
        );

        database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_permissions (" +
                        "barrio_id VARCHAR(8), " +
                        "permission VARCHAR(32), " +
                        "value_visitor BOOLEAN, " +
                        "value_resident BOOLEAN, " +
                        "value_trusted BOOLEAN, " +
                        "PRIMARY KEY (barrio_id, permission), " +
                        "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
        );

        database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_modes (" +
                        "barrio_id VARCHAR(8) PRIMARY KEY, " +
                        "mode VARCHAR(16) NOT NULL, " +
                        "group_check VARCHAR(16), " +
                        "check_time VARCHAR(16), " +
                        "online_in_world BOOLEAN, " +
                        "rent_price DOUBLE, " +
                        "last_check BIGINT, " +
                        "next_check BIGINT, " +
                        "FOREIGN KEY (barrio_id) REFERENCES barrios(id) ON DELETE CASCADE)"
        );

        // Initialize Managers
        modeManager = new BarrioModeManager(this);
        gadgetsManager = new BarrioGadgetsManager(this);
        upgradesManager = new BarrioUpgradesManager(this);
        chatManager = new BarrioChatManager(this);
        database.executeQuery(
                "SELECT COUNT(*) as count FROM barrio_permissions",
                rs -> {
                    if (rs.next()) {
                        getLogger().info("Found " + rs.getInt("count") + " permissions in database");
                    }
                    return null;
                }
        );
        // Initialize playerDataManager first
        playerDataManager = new BarrioPlayerDataManager(this);
        playerDataManager.initialize();

        // Initialize rankManager, which now uses playerDataManager
        rankManager = new BarrioRankManager(this);
        rankManager.initialize();

        // Initialize worldSettingsManager
        worldSettingsManager = new BarrioWorldSettingsManager(this);
        worldSettingsManager.initialize();

        // Migrate nicknames from settings table to main barrios table
        migrateNicknames();

        // Add barrio_chat_only column to barrio_world_settings table if it doesn't exist
        migrateBarrioChatOnlyColumn();

        // Initialize barrioManager
        barrioManager = new BarrioManager(this);
        barrioManager.initialize();

        // Initialize defaultBarrioManager
        defaultBarrioManager = new DefaultBarrioManager(this);
        defaultBarrioManager.initialize();

        // Initialize permissionManager which depends on rankManager
        getLogger().info("Initializing permission manager");
        permissionManager = new BarrioPermissionManager(this);
        permissionManager.startPeriodicSaving();
        getLogger().info("Permission manager initialized");

        // Initialize ratingManager
        getLogger().info("Initializing rating manager");
        ratingManager = new BarrioRatingManager(this);
        if (ratingManager == null) {
            getLogger().severe("Failed to create rating manager");
        } else {
            getLogger().info("Rating manager created successfully");
            ratingManager.initialize();
            getLogger().info("Rating manager initialized");

            // Initialize topRatedBarrioManager after ratingManager and permissionManager
            getLogger().info("Initializing top-rated barrio manager");
            topRatedBarrioManager = new TopRatedBarrioManager(this);
            topRatedBarrioManager.initialize();
            getLogger().info("Top-rated barrio manager initialized");
        }

        // Initialize playerHeadManager
        getLogger().info("Initializing player head manager");
        playerHeadManager = new PlayerHeadManager(this);
        playerHeadManager.initialize();

        // Set the playerHeadManager in the HeadUtils class
        me.zivush.barriocore.util.HeadUtils.setPlayerHeadManager(playerHeadManager);
        getLogger().info("Player head manager initialized");
        // Register command
        getCommand("barrio").setExecutor(new BarrioCommand(this, database));
        getCommand("barrio").setTabCompleter(new BarrioTabCompleter(this));
        // Register GUI listener
        getServer().getPluginManager().registerEvents(new GUIListener(this, database), this);
        getServer().getPluginManager().registerEvents(new BarrioPortalListener(this), this);
        getServer().getPluginManager().registerEvents(new BarrioGadgetsListener(this), this);
        getServer().getPluginManager().registerEvents(new BarrioPermissionsListener(this, permissionManager), this);
        getServer().getPluginManager().registerEvents(new me.zivush.barriocore.listeners.BarrioUpgradesListener(this), this);
        getServer().getPluginManager().registerEvents(new BarrioRankListener(this, rankManager), this);
        getServer().getPluginManager().registerEvents(new BarrioPlayerDataListener(this, playerDataManager), this);
        getServer().getPluginManager().registerEvents(new BarrioWorldSettingsListener(this), this);
        getServer().getPluginManager().registerEvents(new BarrioChatListener(this, chatManager), this);
        getServer().getPluginManager().registerEvents(new DefaultBarrioListener(this, defaultBarrioManager), this);
        getServer().getPluginManager().registerEvents(new BarrioHungerListener(this), this);
        getServer().getPluginManager().registerEvents(new BarrioRespawnListener(this), this);
        getServer().getPluginManager().registerEvents(new PlayerHeadListener(this, playerHeadManager), this);

        // Register PlaceholderAPI expansion if available
        if (getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            getLogger().info("PlaceholderAPI found, registering expansion...");
            new me.zivush.barriocore.placeholders.BarrioPlaceholderExpansion(this).register();
            getLogger().info("PlaceholderAPI expansion registered successfully!");
        }

        getLogger().info("BarrioCore enabled!");
    }

    @Override
    public void onDisable() {
        getLogger().info("BarrioCore disabling...");

        // Stop managers and save data
        if (playerDataManager != null) {
            playerDataManager.shutdown(); // Handles stopping tasks and final save
        }
        if (rankManager != null) {
            rankManager.shutdown(); // Handles stopping tasks and final save
        }
        if (permissionManager != null) {
            permissionManager.stopPeriodicSaving(); // Handles stopping tasks and final save
        }
        if (gadgetsManager != null) {
            gadgetsManager.stopInactivityCheck();
        }
        if (upgradesManager != null) {
            upgradesManager.shutdown();
        }
        if (worldSettingsManager != null) {
            worldSettingsManager.shutdown();
        }

        if (barrioManager != null) {
            barrioManager.shutdown();
        }

        if (defaultBarrioManager != null) {
            defaultBarrioManager.stopPeriodicSaving();
            defaultBarrioManager.saveAllDefaultBarrios();
        }

        if (ratingManager != null) {
            ratingManager.shutdown();
        }

        if (topRatedBarrioManager != null) {
            topRatedBarrioManager.shutdown();
        }

        if (playerHeadManager != null) {
            playerHeadManager.shutdown();
        }

        // Close DB connection pool
        if (database != null) {
            database.close();
        }
        getLogger().info("BarrioCore disabled!");
    }

    // --- Debug Utilities ---
    private boolean isDebugEnabled = false;

    /**
     * Checks if debug logging is enabled in the config
     *
     * @return true if debug is enabled, false otherwise
     */
    public boolean isDebugEnabled() {
        return isDebugEnabled;
    }

    /**
     * Logs a debug message if debug is enabled in the config
     *
     * @param message The message to log
     */
    public void debug(String message) {
        if (isDebugEnabled) {
            getLogger().info(message);
        }
    }

    /**
     * Logs a fine level debug message if debug is enabled in the config
     *
     * @param message The message to log
     */
    public void debugFine(String message) {
        if (isDebugEnabled) {
            getLogger().fine(message);
        }
    }

    /**
     * Logs a finer level debug message if debug is enabled in the config
     *
     * @param message The message to log
     */
    public void debugFiner(String message) {
        if (isDebugEnabled) {
            getLogger().finer(message);
        }
    }

    /**
     * Logs a finest level debug message if debug is enabled in the config
     *
     * @param message The message to log
     */
    public void debugFinest(String message) {
        if (isDebugEnabled) {
            getLogger().finest(message);
        }
    }

    /**
     * Logs an informational message only if debug is enabled
     * This is for messages that would normally use getLogger().info() but should only
     * be shown when debug mode is enabled
     *
     * @param message The message to log
     */
    public void debugInfo(String message) {
        if (isDebugEnabled) {
            getLogger().info(message);
        }
    }

    /**
     * Loads the debug setting from config
     */
    public void loadDebugSetting() {
        isDebugEnabled = getConfig().getBoolean("debug.enabled", false);
        getLogger().info("Debug mode is " + (isDebugEnabled ? "enabled" : "disabled"));

        // Configure Java logger levels based on debug setting
        if (isDebugEnabled) {
            Logger rootLogger = getLogger().getParent();
            rootLogger.setLevel(Level.FINE);
            // Make sure console handler shows fine/finer/finest messages
            for (Handler handler : rootLogger.getHandlers()) {
                if (handler instanceof ConsoleHandler) {
                    handler.setLevel(Level.FINE);
                }
            }
        } else {
            // When debug is disabled, set the level to INFO to prevent fine/finer/finest messages
            Logger rootLogger = getLogger().getParent();
            rootLogger.setLevel(Level.INFO);
        }
    }

    /**
     * Reloads all configuration files
     */
    public void reloadConfigurations() {
        // Reload config.yml
        reloadConfig();

        // Reload debug setting
        loadDebugSetting();

        // Reload gui.yml
        File guiFile = new File(getDataFolder(), "gui.yml");
        guiConfig = YamlConfiguration.loadConfiguration(guiFile);

        // Reload messages.yml
        File messagesFile = new File(getDataFolder(), "messages.yml");
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);

        getLogger().info("All configurations reloaded");
    }

    // --- Getters ---
    public Database getDatabase() { return database; }
    public FileConfiguration getGuiConfig() { return guiConfig; }
    public BarrioGadgetsManager getGadgetsManager() { return gadgetsManager; }
    public BarrioUpgradesManager getUpgradesManager() { return upgradesManager; }
    public FileConfiguration getMessagesConfig() { return messagesConfig; }
    public BarrioModeManager getModeManager() { return modeManager; }
    public BarrioPermissionManager getPermissionManager() { return permissionManager; }
    public BarrioRankManager getRankManager() { return rankManager; }
    public BarrioPlayerDataManager getPlayerDataManager() { return playerDataManager; }
    public BarrioWorldSettingsManager getWorldSettingsManager() { return worldSettingsManager; }
    public BarrioChatManager getChatManager() { return chatManager; }
    public BarrioManager getBarrioManager() { return barrioManager; }
    public DefaultBarrioManager getDefaultBarrioManager() { return defaultBarrioManager; }
    public BarrioRatingManager getRatingManager() {
        if (ratingManager == null) {
            getLogger().severe("Rating manager is null in getRatingManager method");
        }
        return ratingManager;
    }

    public TopRatedBarrioManager getTopRatedBarrioManager() {
        return topRatedBarrioManager;
    }

    public PlayerHeadManager getPlayerHeadManager() {
        return playerHeadManager;
    }

    // --- GUI Management ---
    public void setOpenGui(UUID playerUuid, BaseGUI gui) { openGuis.put(playerUuid, gui); }
    public BaseGUI getOpenGui(UUID playerUuid) { return openGuis.get(playerUuid); }
    public void removeOpenGui(UUID playerUuid) { openGuis.remove(playerUuid); } // Good practice to have remove

    /**
     * Migrates nicknames from the barrio_world_settings table to the barrios table.
     * This is a one-time migration to move the nickname field to the main table.
     */
    private void migrateNicknames() {
        getLogger().info("Starting migration of barrio nicknames from settings table to main barrios table...");
        try {
            // Get all nicknames from the settings table
            database.executeQuery(
                "SELECT barrio_id, nickname FROM barrio_world_settings WHERE nickname IS NOT NULL",
                rs -> {
                    int count = 0;
                    while (rs.next()) {
                        String barrioId = rs.getString("barrio_id");
                        String nickname = rs.getString("nickname");

                        if (nickname != null && !nickname.isEmpty()) {
                            // Update the main barrios table with the nickname
                            database.executeUpdate(
                                "UPDATE barrios SET nickname = ? WHERE id = ?",
                                nickname, barrioId
                            );
                            count++;
                        }
                    }
                    getLogger().info("Migrated " + count + " barrio nicknames to the main barrios table.");
                    return null;
                }
            );
        } catch (Exception e) {
            getLogger().severe("Error migrating nicknames: " + e.getMessage());
        }
    }

    // --- Message Util ---
    public String getMessage(String key) {
        return ChatColor.translateAlternateColorCodes('&', messagesConfig.getString(key, "&cMessage not found: " + key));
    }

    /**
     * Migrates the database to add the barrio_chat_only column to the barrio_world_settings table if it doesn't exist.
     */
    private void migrateBarrioChatOnlyColumn() {
        try {
            // Check if the column exists
            database.executeQuery(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                "WHERE TABLE_NAME = 'barrio_world_settings' AND COLUMN_NAME = 'barrio_chat_only'",
                rs -> {
                    if (rs.next() && rs.getInt(1) == 0) {
                        // Column doesn't exist, add it
                        getLogger().info("Adding barrio_chat_only column to barrio_world_settings table...");
                        database.executeUpdate(
                            "ALTER TABLE barrio_world_settings ADD COLUMN barrio_chat_only BOOLEAN NOT NULL DEFAULT FALSE"
                        );
                        getLogger().info("barrio_chat_only column added successfully.");

                        // Migrate existing settings from barrio_gadgets if that column exists
                        database.executeQuery(
                            "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS " +
                            "WHERE TABLE_NAME = 'barrio_gadgets' AND COLUMN_NAME = 'barrio_chat_only'",
                            gadgetsRs -> {
                                if (gadgetsRs.next() && gadgetsRs.getInt(1) > 0) {
                                    getLogger().info("Migrating barrio_chat_only settings from barrio_gadgets to barrio_world_settings...");
                                    database.executeUpdate(
                                        "UPDATE barrio_world_settings ws " +
                                        "JOIN barrio_gadgets g ON ws.barrio_id = g.barrio_id " +
                                        "SET ws.barrio_chat_only = g.barrio_chat_only"
                                    );
                                    getLogger().info("Migration of barrio_chat_only settings completed.");
                                }
                                return null;
                            }
                        );
                    } else {
                        getLogger().info("barrio_chat_only column already exists in barrio_world_settings table.");
                    }
                    return null;
                }
            );
        } catch (Exception e) {
            getLogger().severe("Error migrating barrio_chat_only column: " + e.getMessage());
        }
    }
    // Overload for replacements
    public String getMessage(String key, String... replacements) {
        String message = getMessage(key);
        if (replacements.length % 2 != 0) {
            getLogger().warning("Invalid number of replacements for message key: " + key);
            return message;
        }
        for (int i = 0; i < replacements.length; i += 2) {
            message = message.replace(replacements[i], replacements[i+1]);
        }
        return message;
    }


    /**
     * Opens a sign editor for a player and handles the result.
     *
     * @param player The player to open the sign editor for
     * @param callback The callback to handle the result
     */
    public void openSignEditor(Player player, Consumer<String[]> callback) {
        // Create a sign at a temporary location
        Location signLoc = new Location(player.getWorld(), 0, 0, 0);

        // Use SignGUI plugin if available
        if (getServer().getPluginManager().getPlugin("SignGUI") != null) {
            try {
                de.rapha149.signgui.SignGUI.builder()
                        .setLines("", "", "", "")
                        .setHandler((p, result) -> {
                            callback.accept(result.getLines());
                            return Collections.emptyList();
                        })
                        .build()
                        .open(player);
            } catch (Exception e) {
                getLogger().severe("Error opening SignGUI: " + e.getMessage());
                player.sendMessage(ChatColor.RED + "Error opening sign editor. Please try again.");
            }
        } else {
            // Fallback to chat input
            player.sendMessage(ChatColor.YELLOW + "Enter your message in chat:");

            // Register a chat listener
            PlayerChatEvent chatEvent = new PlayerChatEvent(player, "");
            getServer().getPluginManager().registerEvents(new Listener() {
                @EventHandler
                public void onChat(AsyncPlayerChatEvent event) {
                    if (event.getPlayer().equals(player)) {
                        event.setCancelled(true);
                        String message = event.getMessage();
                        callback.accept(new String[]{message, "", "", ""});
                        HandlerList.unregisterAll(this);
                    }
                }
            }, this);
        }
    }
}