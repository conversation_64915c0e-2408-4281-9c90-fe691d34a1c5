package me.zivush.barriocore.playerdata;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import me.zivush.barriocore.ranks.Rank;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages player data for barrios, including loading/unloading based on activity.
 */
public class BarrioPlayerDataManager {
    private final BarrioCore plugin;
    private final Database database;
    private final Map<String, Map<UUID, BarrioPlayerData>> loadedPlayerData = new ConcurrentHashMap<>();
    private final Map<String, Long> lastActivity = new ConcurrentHashMap<>();
    private final long inactivityUnloadMillis;
    private final long saveIntervalTicks;
    private BukkitTask inactivityCheckTask;
    private BukkitTask saveTask;

    /**
     * Constructor for the BarrioPlayerDataManager.
     *
     * @param plugin The BarrioCore plugin instance
     */
    public BarrioPlayerDataManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();
        this.inactivityUnloadMillis = plugin.getConfig().getLong("player_data.inactivity_unload_minutes", 5) * 60 * 1000;
        this.saveIntervalTicks = plugin.getConfig().getLong("player_data.save_interval_minutes", 5) * 60 * 20; // minutes -> ticks
        plugin.getLogger().info("BarrioPlayerDataManager initialized.");
    }

    /**
     * Initializes the manager by starting the necessary tasks.
     */
    public void initialize() {
        startInactivityCheck();
        startPeriodicSaving();
        plugin.getLogger().info("BarrioPlayerDataManager tasks started.");
    }

    /**
     * Shuts down the manager by stopping tasks and saving data.
     */
    public void shutdown() {
        plugin.getLogger().info("BarrioPlayerDataManager shutting down...");

        if (inactivityCheckTask != null) {
            inactivityCheckTask.cancel();
            plugin.getLogger().info("Inactivity check task cancelled.");
        }

        if (saveTask != null) {
            saveTask.cancel();
            plugin.getLogger().info("Periodic save task cancelled.");
        }

        plugin.getLogger().info("Performing final save of all player data...");
        saveAllPlayerData(); // Final save on shutdown
        plugin.getLogger().info("BarrioPlayerDataManager shutdown complete.");
    }

    /**
     * Gets player data for a specific player in a specific barrio.
     * Creates a new entry if the player has never joined the barrio before.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @return The player data
     */
    public BarrioPlayerData getPlayerData(String barrioId, UUID playerUuid) {
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("Attempted to get player data with null barrioId or playerUuid.");
            return null;
        }

        // Ensure player data is loaded for this barrio
        loadPlayerDataForBarrio(barrioId);

        Map<UUID, BarrioPlayerData> barrioPlayerDataMap = loadedPlayerData.get(barrioId);
        if (barrioPlayerDataMap == null) {
            plugin.getLogger().warning("Player data map was null after attempting load for barrio: " + barrioId);
            return null;
        }

        // Get existing player data or create new entry
        return barrioPlayerDataMap.computeIfAbsent(playerUuid, uuid -> {
            BarrioPlayerData newData = new BarrioPlayerData(barrioId, uuid);
            plugin.debugFine("Created new player data for " + uuid + " in barrio " + barrioId);
            return newData;
        });
    }

    /**
     * Gets the rank of a player in a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @return The player's rank (defaults to VISITOR if no data exists)
     */
    public Rank getRank(String barrioId, UUID playerUuid) {
        BarrioPlayerData playerData = getPlayerData(barrioId, playerUuid);
        return playerData != null ? playerData.getRank() : Rank.VISITOR;
    }

    /**
     * Gets the map of player data for a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The map of player data, or null if not loaded
     */
    public Map<UUID, BarrioPlayerData> getPlayerDataMap(String barrioId) {
        if (barrioId == null) return null;

        // Ensure player data is loaded for this barrio
        loadPlayerDataForBarrio(barrioId);

        // Return the map (may be null if loading failed)
        return loadedPlayerData.get(barrioId);
    }

    /**
     * Sets the rank of a player in a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @param rank The rank to set
     * @return true if successful, false otherwise
     */
    public boolean setRank(String barrioId, UUID playerUuid, Rank rank) {
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("Attempted to set rank with null barrioId or playerUuid.");
            return false;
        }
        if (rank == Rank.OWNER) {
            plugin.getLogger().warning("Attempted to set rank to OWNER directly for player " + playerUuid + " in barrio " + barrioId);
            return false; // Cannot set OWNER rank this way
        }

        BarrioPlayerData playerData = getPlayerData(barrioId, playerUuid);
        if (playerData == null) {
            plugin.getLogger().severe("Failed to get or create player data for " + playerUuid + " in barrio " + barrioId);
            return false;
        }

        playerData.setRank(rank);
        return true;
    }

    /**
     * Checks if a player is banned from a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @return true if the player is banned, false otherwise
     */
    public boolean isBanned(String barrioId, UUID playerUuid) {
        BarrioPlayerData playerData = getPlayerData(barrioId, playerUuid);
        return playerData != null && playerData.isBanned();
    }

    /**
     * Bans a player from a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @return true if successful, false otherwise
     */
    public boolean banPlayer(String barrioId, UUID playerUuid) {
        BarrioPlayerData playerData = getPlayerData(barrioId, playerUuid);
        if (playerData == null) {
            plugin.getLogger().severe("Failed to get or create player data for " + playerUuid + " in barrio " + barrioId);
            return false;
        }

        playerData.setBanned(true);
        return true;
    }

    /**
     * Unbans a player from a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @return true if successful, false otherwise
     */
    public boolean unbanPlayer(String barrioId, UUID playerUuid) {
        BarrioPlayerData playerData = getPlayerData(barrioId, playerUuid);
        if (playerData == null) {
            plugin.getLogger().severe("Failed to get or create player data for " + playerUuid + " in barrio " + barrioId);
            return false;
        }

        playerData.setBanned(false);
        return true;
    }

    /**
     * Updates the last location of a player in a specific barrio.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @param location The player's location
     * @return true if successful, false otherwise
     */
    public boolean updatePlayerLocation(String barrioId, UUID playerUuid, Location location) {
        if (location == null) {
            return false;
        }

        BarrioPlayerData playerData = getPlayerData(barrioId, playerUuid);
        if (playerData == null) {
            plugin.getLogger().severe("Failed to get or create player data for " + playerUuid + " in barrio " + barrioId);
            return false;
        }

        playerData.setLastLocation(location);
        return true;
    }

    /**
     * Loads player data for a specific barrio from the database.
     *
     * @param barrioId The ID of the barrio
     */
    public void loadPlayerDataForBarrio(String barrioId) {
        if (barrioId == null) return;

        // Update activity regardless of whether it's loaded or not
        updateActivity(barrioId);

        // Check if already loaded
        if (loadedPlayerData.containsKey(barrioId)) {
            return; // Already loaded
        }

        plugin.debugInfo("Loading player data for barrio: " + barrioId);
        Map<UUID, BarrioPlayerData> barrioPlayerDataMap = new ConcurrentHashMap<>();

        // 1. Get the Owner UUID from the 'barrios' table
        UUID ownerUUID = getOwnerUUID(barrioId);
        if (ownerUUID != null) {
            // Create player data for the owner with OWNER rank
            BarrioPlayerData ownerData = new BarrioPlayerData(
                    barrioId, ownerUUID, Rank.OWNER, false, 0,
                    System.currentTimeMillis(), null, 0, 0, 0);
            barrioPlayerDataMap.put(ownerUUID, ownerData);
            plugin.debugFine("Loaded OWNER data: " + ownerUUID + " for barrio " + barrioId);
        } else {
            plugin.getLogger().warning("Could not find owner for barrio: " + barrioId + ". Player data may be incomplete.");
        }

        // 2. Load player data from the database
        try {
            database.executeQuery(
                    "SELECT * FROM barrio_player_data WHERE barrio_id = ?",
                    rs -> {
                        int count = 0;
                        while (rs.next()) {
                            UUID playerUuid = UUID.fromString(rs.getString("player_uuid"));

                            // Skip if this is the owner (already added above)
                            if (ownerUUID != null && playerUuid.equals(ownerUUID)) {
                                continue;
                            }

                            // Parse rank from string
                            String rankStr = rs.getString("player_rank");
                            Rank rank = Rank.VISITOR; // Default
                            try {
                                rank = Rank.valueOf(rankStr);
                            } catch (IllegalArgumentException e) {
                                plugin.getLogger().warning("Invalid rank '" + rankStr + "' for player " + playerUuid + " in barrio " + barrioId);
                            }

                            // Create player data object
                            BarrioPlayerData playerData = new BarrioPlayerData(
                                    barrioId,
                                    playerUuid,
                                    rank,
                                    rs.getBoolean("is_banned"),
                                    rs.getLong("ban_date"),
                                    rs.getLong("first_join_time"),
                                    rs.getString("last_location_world"),
                                    rs.getDouble("last_location_x"),
                                    rs.getDouble("last_location_y"),
                                    rs.getDouble("last_location_z")
                            );

                            barrioPlayerDataMap.put(playerUuid, playerData);
                            count++;
                        }
                        plugin.debugFine("Loaded " + count + " player data entries from barrio_player_data table for " + barrioId);
                        return null;
                    },
                    barrioId
            );
        } catch (RuntimeException e) {
            plugin.getLogger().log(Level.SEVERE, "Database error loading player data for barrio: " + barrioId, e);
        }

        // Store the loaded map
        loadedPlayerData.put(barrioId, barrioPlayerDataMap);
        plugin.debugInfo("Finished loading player data for barrio: " + barrioId + ". Total loaded: " + barrioPlayerDataMap.size());
    }

    /**
     * Gets the UUID of the owner of a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The UUID of the owner, or null if not found
     */
    private UUID getOwnerUUID(String barrioId) {
        return database.executeQuery(
                "SELECT uuid FROM barrios WHERE id = ?",
                rs -> {
                    if (rs.next()) {
                        try {
                            return UUID.fromString(rs.getString("uuid"));
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid UUID format for owner of barrio " + barrioId + ": " + rs.getString("uuid"));
                            return null;
                        }
                    }
                    return null;
                },
                barrioId
        );
    }

    /**
     * Updates the activity timestamp for a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void updateActivity(String barrioId) {
        if (barrioId != null) {
            lastActivity.put(barrioId, System.currentTimeMillis());
            plugin.debugFinest("Updated activity for barrio: " + barrioId);
        }
    }

    /**
     * Starts the inactivity check task.
     */
    private void startInactivityCheck() {
        long checkInterval = 60 * 20L; // Check every minute (adjust if needed)
        inactivityCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    unloadInactiveBarrios();
                } catch (Exception e) {
                    plugin.getLogger().log(Level.SEVERE, "Error during player data inactivity check", e);
                }
            }
        }.runTaskTimerAsynchronously(plugin, checkInterval, checkInterval);
        plugin.debugFine("Player data inactivity check task started.");
    }

    /**
     * Starts the periodic saving task.
     */
    private void startPeriodicSaving() {
        saveTask = new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    saveAllPlayerData();
                } catch (Exception e) {
                    plugin.getLogger().log(Level.SEVERE, "Error during player data periodic save", e);
                }
            }
        }.runTaskTimerAsynchronously(plugin, saveIntervalTicks, saveIntervalTicks);
        plugin.debugFine("Player data periodic save task started.");
    }

    /**
     * Unloads player data for inactive barrios.
     */
    private void unloadInactiveBarrios() {
        long now = System.currentTimeMillis();
        plugin.debugFiner("Running inactivity check for player data...");

        // Clean up potential mismatch
        loadedPlayerData.keySet().retainAll(lastActivity.keySet());

        // Create a map of barrio IDs with players present
        Map<String, Boolean> barriosWithPlayers = new HashMap<>();

        // Process all online players once
        for (Player player : Bukkit.getOnlinePlayers()) {
            String barrioId = getBarrioIdFromWorld(player.getWorld());
            if (barrioId != null) {
                barriosWithPlayers.put(barrioId, true);
            }
        }

        // Check each barrio for inactivity
        for (Iterator<Map.Entry<String, Long>> it = lastActivity.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, Long> entry = it.next();
            String barrioId = entry.getKey();
            long lastSeen = entry.getValue();

            if (now - lastSeen > inactivityUnloadMillis) {
                boolean playerPresent = barriosWithPlayers.containsKey(barrioId);

                if (!playerPresent) {
                    plugin.debugInfo("Unloading inactive player data for barrio: " + barrioId);

                    // Check if the barrio still exists before attempting to save
                    if (barrioExists(barrioId)) {
                        // Save data BEFORE removing from memory
                        savePlayerDataForBarrio(barrioId, true); // Save synchronously before unload
                    } else {
                        plugin.getLogger().info("Skipping save for barrio " + barrioId + " as it no longer exists in the database");
                    }

                    // Remove from memory
                    loadedPlayerData.remove(barrioId);
                    it.remove(); // Remove from lastActivity
                    plugin.debugFine("Successfully unloaded player data for barrio: " + barrioId);
                } else {
                    // Players are present, update activity to prevent immediate re-check unload
                    updateActivity(barrioId);
                }
            }
        }
    }

    /**
     * Saves all player data to the database.
     */
    public void saveAllPlayerData() {
        plugin.debugInfo("Saving all player data...");
        // Create a copy of the keys to avoid ConcurrentModificationException
        Set<String> barrioIds = new HashSet<>(loadedPlayerData.keySet());

        for (String barrioId : barrioIds) {
            // The savePlayerDataForBarrio method will check if the barrio exists
            savePlayerDataForBarrio(barrioId, true); // Use sync=true to force save all data
        }
        plugin.debugInfo("Finished saving all player data.");
    }

    /**
     * Checks if a barrio exists in the database.
     *
     * @param barrioId The ID of the barrio to check
     * @return true if the barrio exists, false otherwise
     */
    private boolean barrioExists(String barrioId) {
        if (barrioId == null) return false;

        try {
            Boolean exists = database.executeQuery(
                "SELECT 1 FROM barrios WHERE id = ? LIMIT 1",
                rs -> rs.next(),
                barrioId
            );
            return Boolean.TRUE.equals(exists);
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Error checking if barrio exists: " + barrioId, e);
            return false;
        }
    }

    /**
     * Saves player data for a specific barrio to the database.
     *
     * @param barrioId The ID of the barrio
     * @param sync Whether to save synchronously
     */
    public void savePlayerDataForBarrio(String barrioId, boolean sync) {
        if (barrioId == null) return;

        Map<UUID, BarrioPlayerData> playerDataMap = loadedPlayerData.get(barrioId);
        if (playerDataMap == null || playerDataMap.isEmpty()) {
            plugin.debugFine("No player data to save for barrio: " + barrioId);
            return;
        }

        // Check if the barrio still exists in the database before saving
        if (!barrioExists(barrioId)) {
            plugin.getLogger().info("Skipping save for barrio " + barrioId + " as it no longer exists in the database");
            // Remove from memory since the barrio doesn't exist anymore
            loadedPlayerData.remove(barrioId);
            lastActivity.remove(barrioId);
            return;
        }

        plugin.debugInfo("Saving player data for barrio: " + barrioId + " (sync: " + sync + ")");

        Runnable saveTask = () -> {
            int savedCount = 0;
            for (BarrioPlayerData playerData : playerDataMap.values()) {
                // Skip if not dirty and not a forced save
                // When sync=true, save all data regardless of dirty flag
                if (!playerData.isDirty() && !sync) {
                    continue;
                }

                // Skip owner rank (not stored in player_data table)
                if (playerData.getRank() == Rank.OWNER) {
                    continue;
                }

                try {
                    database.executeUpdate(
                            "INSERT INTO barrio_player_data " +
                            "(barrio_id, player_uuid, player_rank, is_banned, ban_date, first_join_time, " +
                            "last_location_world, last_location_x, last_location_y, last_location_z) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                            "ON DUPLICATE KEY UPDATE " +
                            "player_rank = VALUES(player_rank), " +
                            "is_banned = VALUES(is_banned), " +
                            "ban_date = VALUES(ban_date), " +
                            "last_location_world = VALUES(last_location_world), " +
                            "last_location_x = VALUES(last_location_x), " +
                            "last_location_y = VALUES(last_location_y), " +
                            "last_location_z = VALUES(last_location_z)",
                            playerData.getBarrioId(),
                            playerData.getPlayerUuid().toString(),
                            playerData.getRank().name(),
                            playerData.isBanned(),
                            playerData.getBanDate(),
                            playerData.getFirstJoinTime(),
                            playerData.getLastLocationWorld(),
                            playerData.getLastLocationX(),
                            playerData.getLastLocationY(),
                            playerData.getLastLocationZ()
                    );
                    playerData.clearDirty();
                    savedCount++;
                } catch (Exception e) {
                    plugin.getLogger().log(Level.SEVERE, "Error saving player data for " + playerData.getPlayerUuid() + " in barrio " + barrioId, e);
                }
            }
            plugin.debugInfo("Saved " + savedCount + " player data entries for barrio: " + barrioId);
        };

        if (sync) {
            saveTask.run();
        } else {
            new BukkitRunnable() {
                @Override
                public void run() {
                    saveTask.run();
                }
            }.runTaskAsynchronously(plugin);
        }
    }

    /**
     * Forcefully unloads player data for a barrio, saving it first if requested.
     *
     * @param barrioId The ID of the barrio
     * @param saveFirst Whether to save the data before unloading
     */
    public void forceUnloadBarrioPlayerData(String barrioId, boolean saveFirst) {
        if (barrioId == null) return;
        plugin.debugInfo("Force unloading player data for barrio: " + barrioId + " (Save first: " + saveFirst + ")");
        if (saveFirst) {
            savePlayerDataForBarrio(barrioId, true); // Save synchronously
        }
        loadedPlayerData.remove(barrioId);
        lastActivity.remove(barrioId);
    }

    /**
     * Unloads player data for a barrio without saving it.
     * Used when deleting a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void unloadPlayerData(String barrioId) {
        forceUnloadBarrioPlayerData(barrioId, false);
    }

    /**
     * Gets the barrio ID from a world.
     *
     * @param world The world
     * @return The barrio ID, or null if not a barrio world
     */
    public static String getBarrioIdFromWorld(World world) {
        if (world == null) return null;
        String worldName = world.getName();
        if (worldName.startsWith("Barrios/")) {
            // More efficient than splitting the entire string
            int startIndex = 8; // "Barrios/".length()
            int endIndex = worldName.indexOf('/', startIndex);
            if (endIndex == -1) {
                // No second slash, return everything after "Barrios/"
                return worldName.substring(startIndex);
            } else {
                // Return the part between the first and second slash
                return worldName.substring(startIndex, endIndex);
            }
        }
        return null;
    }
}
