package me.zivush.barriocore.playerdata;

import me.zivush.barriocore.ranks.Rank;
import org.bukkit.Location;

import java.util.UUID;

/**
 * Represents player data for a specific player in a specific barrio.
 * This includes rank, ban status, location data, and join time.
 */
public class BarrioPlayerData {
    private final String barrioId;
    private final UUID playerUuid;
    private Rank rank;
    private boolean banned;
    private long banDate;
    private final long firstJoinTime;
    private String lastLocationWorld;
    private double lastLocationX;
    private double lastLocationY;
    private double lastLocationZ;
    private boolean dirty; // Flag to track if data has been modified and needs saving

    /**
     * Constructor for creating a new player data entry.
     * 
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     * @param rank The player's rank in the barrio
     * @param banned Whether the player is banned from the barrio
     * @param banDate The timestamp when the player was banned (0 if not banned)
     * @param firstJoinTime The timestamp when the player first joined the barrio
     * @param lastLocationWorld The world name of the player's last location
     * @param lastLocationX The X coordinate of the player's last location
     * @param lastLocationY The Y coordinate of the player's last location
     * @param lastLocationZ The Z coordinate of the player's last location
     */
    public BarrioPlayerData(String barrioId, UUID playerUuid, Rank rank, boolean banned, 
                           long banDate, long firstJoinTime, String lastLocationWorld,
                           double lastLocationX, double lastLocationY, double lastLocationZ) {
        this.barrioId = barrioId;
        this.playerUuid = playerUuid;
        this.rank = rank;
        this.banned = banned;
        this.banDate = banDate;
        this.firstJoinTime = firstJoinTime;
        this.lastLocationWorld = lastLocationWorld;
        this.lastLocationX = lastLocationX;
        this.lastLocationY = lastLocationY;
        this.lastLocationZ = lastLocationZ;
        this.dirty = false;
    }

    /**
     * Constructor for creating a new player data entry with default values.
     * 
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player
     */
    public BarrioPlayerData(String barrioId, UUID playerUuid) {
        this(barrioId, playerUuid, Rank.VISITOR, false, 0, System.currentTimeMillis(), 
             null, 0, 0, 0);
    }

    // Getters
    public String getBarrioId() { return barrioId; }
    public UUID getPlayerUuid() { return playerUuid; }
    public Rank getRank() { return rank; }
    public boolean isBanned() { return banned; }
    public long getBanDate() { return banDate; }
    public long getFirstJoinTime() { return firstJoinTime; }
    public String getLastLocationWorld() { return lastLocationWorld; }
    public double getLastLocationX() { return lastLocationX; }
    public double getLastLocationY() { return lastLocationY; }
    public double getLastLocationZ() { return lastLocationZ; }
    public boolean isDirty() { return dirty; }

    // Setters (mark as dirty when modified)
    public void setRank(Rank rank) {
        if (this.rank != rank) {
            this.rank = rank;
            this.dirty = true;
        }
    }

    public void setBanned(boolean banned) {
        if (this.banned != banned) {
            this.banned = banned;
            if (banned) {
                this.banDate = System.currentTimeMillis();
            }
            this.dirty = true;
        }
    }

    public void setBanDate(long banDate) {
        if (this.banDate != banDate) {
            this.banDate = banDate;
            this.dirty = true;
        }
    }

    public void setLastLocation(Location location) {
        if (location != null) {
            this.lastLocationWorld = location.getWorld().getName();
            this.lastLocationX = location.getX();
            this.lastLocationY = location.getY();
            this.lastLocationZ = location.getZ();
            this.dirty = true;
        }
    }

    public void setLastLocation(String world, double x, double y, double z) {
        this.lastLocationWorld = world;
        this.lastLocationX = x;
        this.lastLocationY = y;
        this.lastLocationZ = z;
        this.dirty = true;
    }

    /**
     * Resets the dirty flag after saving.
     */
    public void clearDirty() {
        this.dirty = false;
    }

    /**
     * Creates a Location object from the stored location data.
     * 
     * @return The Location object, or null if the world is not loaded
     */
    public Location getLastLocation() {
        if (lastLocationWorld == null) {
            return null;
        }
        
        org.bukkit.World world = org.bukkit.Bukkit.getWorld(lastLocationWorld);
        if (world == null) {
            return null;
        }
        
        return new Location(world, lastLocationX, lastLocationY, lastLocationZ);
    }
}
