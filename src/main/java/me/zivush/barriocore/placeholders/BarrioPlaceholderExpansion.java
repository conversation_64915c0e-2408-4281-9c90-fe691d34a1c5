package me.zivush.barriocore.placeholders;

import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.BarrioManager;
import me.zivush.barriocore.gadgets.BarrioGadgetSettings;
import me.zivush.barriocore.modes.BarrioMode;
import me.zivush.barriocore.playerdata.BarrioPlayerData;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import me.zivush.barriocore.ranks.Rank;
import me.zivush.barriocore.ratings.BarrioRatingManager;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * This class will be registered through the register-method in the
 * plugins onEnable-method.
 */
public class BarrioPlaceholderExpansion extends PlaceholderExpansion {

    private final BarrioCore plugin;

    /**
     * Since we register the expansion inside our own plugin, we
     * can simply use this method here to get an instance of our
     * plugin.
     *
     * @param plugin
     *        The instance of our plugin.
     */
    public BarrioPlaceholderExpansion(BarrioCore plugin) {
        this.plugin = plugin;
    }

    /**
     * Because this is an internal class,
     * you must override this method to let PlaceholderAPI
     * know to not unregister your expansion class when
     * PlaceholderAPI is reloaded
     *
     * @return true to persist through reloads
     */
    @Override
    public boolean persist() {
        return true;
    }

    /**
     * Because this is a internal class, this check is not needed
     * and we can simply return {@code true}
     *
     * @return Always true since it's an internal class.
     */
    @Override
    public boolean canRegister() {
        return true;
    }

    /**
     * The name of the person who created this expansion should go here.
     * <br>For convenience do we return the author from the plugin.yml
     *
     * @return The name of the author as a String.
     */
    @Override
    public @NotNull String getAuthor() {
        return plugin.getDescription().getAuthors().toString();
    }

    /**
     * The placeholder identifier should go here.
     * <br>This is what tells PlaceholderAPI to call our onRequest
     * method to obtain a value if a placeholder starts with our
     * identifier.
     * <br>The identifier has to be lowercase and can't contain _ or %
     *
     * @return The identifier in {@code %<identifier>_<value>%} as String.
     */
    @Override
    public @NotNull String getIdentifier() {
        return "barrio";
    }

    /**
     * This is the version of the expansion.
     * <br>You don't have to use numbers, since it is set as a String.
     *
     * For convenience do we return the version from the plugin.yml
     *
     * @return The version as a String.
     */
    @Override
    public @NotNull String getVersion() {
        return plugin.getDescription().getVersion();
    }

    /**
     * This is the method called when a placeholder with our identifier
     * is found and needs a value.
     * <br>We specify the value identifier in this method.
     * <br>Since version 2.9.1 can you use OfflinePlayers in your requests.
     *
     * @param  player
     *         A {@link org.bukkit.OfflinePlayer OfflinePlayer}.
     * @param  identifier
     *         A String containing the identifier/value.
     *
     * @return possibly-null String of the requested identifier.
     */
    @Override
    public String onRequest(OfflinePlayer player, @NotNull String identifier) {
        if (player == null || !player.isOnline()) {
            return "";
        }

        Player onlinePlayer = player.getPlayer();
        if (onlinePlayer == null) {
            return "";
        }

        // Get the barrio ID from the player's current world
        String barrioId = BarrioPlayerDataManager.getBarrioIdFromWorld(onlinePlayer.getWorld());
        String notInBarrioValue = plugin.getConfig().getString("placeholders.not_in_barrio", "N/A");

        // Date formatter for consistent date formatting
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");

        // Check if player is in a barrio
        boolean inBarrio = barrioId != null;

        // %barrio_nickname%
        if (identifier.equals("nickname")) {
            if (!inBarrio) {
                return notInBarrioValue;
            }

            // Get the barrio data
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                return notInBarrioValue;
            }

            // Return the nickname or the barrio ID if nickname is null
            String nickname = barrioData.getNickname();
            return nickname != null ? nickname : barrioId;
        }

        // %barrio_owner% - True/False
        else if (identifier.equals("owner")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_owner");
            }

            // Get the barrio data
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                return getDefaultValue("barrio_owner");
            }

            // Get the owner UUID
            UUID ownerUuid = barrioData.getOwnerUuid();
            if (ownerUuid == null) {
                return "False";
            }

            // Check if the player is the owner
            return ownerUuid.equals(onlinePlayer.getUniqueId()) ? "True" : "False";
        }

        // %barrio_owner_name% - Displays owner name
        else if (identifier.equals("owner_name")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_owner_name");
            }

            // Get the barrio data
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                return getDefaultValue("barrio_owner_name");
            }

            // Get the owner UUID
            UUID ownerUuid = barrioData.getOwnerUuid();
            if (ownerUuid == null) {
                return getDefaultValue("barrio_owner_name");
            }

            // Get the owner's name
            return plugin.getServer().getOfflinePlayer(ownerUuid).getName();
        }

        // %barrio_max_players% - Displays max players allowed in barrio
        else if (identifier.equals("max_players")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_max_players");
            }

            // Currently there's no max players limit in the code, so we'll return a default value
            // This can be updated later when max players functionality is implemented
            return "Unlimited";
        }

        // %barrio_player_perm% - Displays the permission group of player (Visitor, Resident, Trusted, Owner)
        else if (identifier.equals("player_perm")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_player_perm");
            }

            // Get the player's rank in the barrio
            Rank playerRank = plugin.getRankManager().getRank(barrioId, onlinePlayer.getUniqueId());
            return playerRank.name();
        }

        // %barrio_creation_date% - Displays when the barrio was created (Mm/Dd/Yyyy)
        else if (identifier.equals("creation_date")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_creation_date");
            }

            // Get the barrio data
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                return getDefaultValue("barrio_creation_date");
            }

            // Format the creation date
            long creationTime = barrioData.getCreationTime();
            return dateFormat.format(new Date(creationTime));
        }

        // %barrio_mode% - Displays the removal mode of the barrio (UNLIMITED/INACTIVITY/RENTING)
        else if (identifier.equals("mode")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_mode");
            }

            try {
                // Get the barrio mode
                BarrioMode mode = plugin.getModeManager().getBarrioMode(barrioId);
                return mode != null ? mode.name() : getDefaultValue("barrio_mode");
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting barrio mode for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_mode");
            }
        }

        // %barrio_auto_payment% - Displays True/False for automatic payment
        else if (identifier.equals("auto_payment")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_auto_payment");
            }

            try {
                // Check if the barrio is in RENTING mode
                BarrioMode mode = plugin.getModeManager().getBarrioMode(barrioId);
                if (mode == BarrioMode.RENTING) {
                    return "True";
                } else {
                    return "False";
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting barrio auto payment for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_auto_payment");
            }
        }

        // %barrio_next_payment_date% - Displays the next payment date (Mm/Dd/Yyyy)
        else if (identifier.equals("next_payment_date")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_next_payment_date");
            }

            try {
                // Get the next payment date
                long nextPaymentTime = plugin.getModeManager().getNextCheckTime(barrioId);
                if (nextPaymentTime > 0) {
                    return dateFormat.format(new Date(nextPaymentTime));
                } else {
                    return getDefaultValue("barrio_next_payment_date");
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting next payment date for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_next_payment_date");
            }
        }

        // %barrio_last_visit_date% - Displays the last player who visited the world (Mm/Dd/Yyyy)
        else if (identifier.equals("last_visit_date")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_last_visit_date");
            }

            try {
                // Get the last visit date from player data
                // This is an approximation as we don't have a specific last visit date field
                // We'll use the current time since the player is currently visiting
                return dateFormat.format(new Date(System.currentTimeMillis()));
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting last visit date for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_last_visit_date");
            }
        }

        // %barrio_status% - Displays visitor access status (Locked/Unlocked)
        else if (identifier.equals("status")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_status");
            }

            try {
                // Check the visit_toggle permission for visitors
                Map<String, Map<Rank, Boolean>> barrioPerms = plugin.getPermissionManager().getBarrioPermissionsForAllRanks(barrioId);
                boolean visitorAllowed = false;

                if (barrioPerms != null) {
                    Map<Rank, Boolean> visitPerms = barrioPerms.get("visit_toggle");
                    if (visitPerms != null && visitPerms.get(Rank.VISITOR) != null) {
                        visitorAllowed = visitPerms.get(Rank.VISITOR);
                    }
                }
                return visitorAllowed ? "Unlocked" : "Locked";
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting barrio status for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_status");
            }
        }

        // %barrio_gadget_status__<gadget>% - True / False if a gadget is active
        else if (identifier.startsWith("gadget_status__")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_gadget_status");
            }

            try {
                // Extract the gadget name from the identifier
                String gadgetName = identifier.substring("gadget_status__".length());

                // Get the gadget settings
                BarrioGadgetSettings gadgetSettings = plugin.getGadgetsManager().getSettings(barrioId);
                if (gadgetSettings == null) {
                    return getDefaultValue("barrio_gadget_status");
                }

                // Check the status of the requested gadget
                boolean status = false;
                switch (gadgetName.toLowerCase()) {
                    case "creeper_damage":
                        status = gadgetSettings.isCreeperDamageEnabled();
                        break;
                    case "creeper_block_damage":
                        status = gadgetSettings.isCreeperBlockDamageEnabled();
                        break;
                    case "ghast_damage":
                        status = gadgetSettings.isGhastDamageEnabled();
                        break;
                    case "ghast_block_damage":
                        status = gadgetSettings.isGhastBlockDamageEnabled();
                        break;
                    case "fire_spread":
                        status = gadgetSettings.isFireSpreadEnabled();
                        break;
                    case "enderman_grief":
                        status = gadgetSettings.isEndermanGriefEnabled();
                        break;
                    case "ravager_grief":
                        status = gadgetSettings.isRavagerGriefEnabled();
                        break;
                    case "show_entry_title":
                        status = gadgetSettings.isShowEntryTitleEnabled();
                        break;
                    case "show_entry_chat":
                        status = gadgetSettings.isShowEntryChatEnabled();
                        break;
                    default:
                        return getDefaultValue("barrio_gadget_status");
                }

                return status ? "True" : "False";
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting gadget status for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_gadget_status");
            }
        }

        // %barrio_total_ratings% - Displays the total ratings for the barrio
        else if (identifier.equals("total_ratings")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_total_ratings");
            }

            try {
                // Get the ratings
                BarrioRatingManager ratingManager = plugin.getRatingManager();
                List<Map<String, Object>> ratings = ratingManager.getRatings(barrioId);
                return String.valueOf(ratings.size());
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting total ratings for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_total_ratings");
            }
        }

        // %barrio_average_ratings% - Displays the average rating of the barrio from 1.0 to 5.0
        else if (identifier.equals("average_ratings")) {
            if (!inBarrio) {
                return getDefaultValue("barrio_average_ratings");
            }

            try {
                // Get the ratings
                BarrioRatingManager ratingManager = plugin.getRatingManager();
                List<Map<String, Object>> ratings = ratingManager.getRatings(barrioId);

                if (ratings.isEmpty()) {
                    return getDefaultValue("barrio_average_ratings");
                }

                // Calculate the average
                int totalStars = 0;
                for (Map<String, Object> rating : ratings) {
                    totalStars += (int) rating.get("stars");
                }

                double average = (double) totalStars / ratings.size();
                DecimalFormat df = new DecimalFormat("#.0");
                return df.format(average);
            } catch (Exception e) {
                plugin.getLogger().warning("Error getting average ratings for " + barrioId + ": " + e.getMessage());
                return getDefaultValue("barrio_average_ratings");
            }
        }

        // We return null if an invalid placeholder was provided
        return null;
    }

    /**
     * Gets a default value for a placeholder from the config.
     *
     * @param placeholder The placeholder key
     * @return The default value from config, or a fallback value if not found
     */
    private String getDefaultValue(String placeholder) {
        return plugin.getConfig().getString("placeholders.default_values." + placeholder, "N/A");
    }
}
