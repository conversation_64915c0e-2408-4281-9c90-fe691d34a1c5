package me.zivush.barriocore.hooks;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.BarrioManager;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.plugin.Plugin;

/**
 * Hook for Custom-Crops plugin integration.
 * Handles permission-based control for sprinkler activation, crop harvesting, and pot watering.
 * This class is only loaded if Custom-Crops plugin is present.
 */
public class CustomCropsHook implements Listener {
    
    private final BarrioCore plugin;
    private final BarrioManager barrioManager;
    private boolean isCustomCropsEnabled = false;
    
    public CustomCropsHook(BarrioCore plugin) {
        this.plugin = plugin;
        this.barrioManager = plugin.getBarrioManager();
        
        // Check if Custom-Crops is available
        Plugin customCropsPlugin = plugin.getServer().getPluginManager().getPlugin("CustomCrops");
        if (customCropsPlugin != null && customCropsPlugin.isEnabled()) {
            this.isCustomCropsEnabled = true;
            plugin.getLogger().info("Custom-Crops integration enabled!");
        } else {
            plugin.getLogger().info("Custom-Crops not found - integration disabled");
        }
    }
    
    /**
     * Check if Custom-Crops integration is enabled
     */
    public boolean isEnabled() {
        return isCustomCropsEnabled;
    }
    
    /**
     * Handles watering can water pot events from Custom-Crops
     * Cancels the event if the player doesn't have custom_crops_access permission in the barrio
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onWateringCanWaterPot(net.momirealms.customcrops.api.event.WateringCanWaterPotEvent event) {
        if (!isCustomCropsEnabled) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Check each pot location for barrio permissions
        for (net.momirealms.customcrops.common.util.Pair<net.momirealms.customcrops.api.core.world.Pos3, String> potPair : event.pots()) {
            net.momirealms.customcrops.api.core.world.Pos3 pos = potPair.left();
            Location location = new Location(
                plugin.getServer().getWorld(pos.world()), 
                pos.x(), 
                pos.y(), 
                pos.z()
            );
            
            String barrioId = barrioManager.getBarrioIdAtLocation(location);
            if (barrioId != null) {
                // Check if player has custom_crops_access permission in this barrio
                if (!plugin.getPermissionManager().hasPermission(barrioId, player, "custom_crops_access")) {
                    event.setCancelled(true);
                    player.sendMessage(plugin.getMessage("messages.custom_crops_no_permission")
                        .replace("%action%", "water pots"));
                    plugin.debug("Cancelled watering can water pot event for player " + player.getName() + 
                               " in barrio " + barrioId + " - no custom_crops_access permission");
                    return; // Cancel for all pots if any one is in a restricted barrio
                }
            }
        }
    }
    
    /**
     * Handles crop interact events from Custom-Crops
     * Cancels the event if the player doesn't have custom_crops_access permission in the barrio
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onCropInteract(net.momirealms.customcrops.api.event.CropInteractEvent event) {
        if (!isCustomCropsEnabled) {
            return;
        }
        
        Player player = event.getPlayer();
        Location location = event.location();
        
        String barrioId = barrioManager.getBarrioIdAtLocation(location);
        if (barrioId != null) {
            // Check if player has custom_crops_access permission in this barrio
            if (!plugin.getPermissionManager().hasPermission(barrioId, player, "custom_crops_access")) {
                event.setCancelled(true);
                player.sendMessage(plugin.getMessage("messages.custom_crops_no_permission")
                    .replace("%action%", "harvest crops"));
                plugin.debug("Cancelled crop interact event for player " + player.getName() + 
                           " in barrio " + barrioId + " - no custom_crops_access permission");
            }
        }
    }
    
    /**
     * Handles sprinkler interact events from Custom-Crops
     * Cancels the event if the player doesn't have custom_crops_access permission in the barrio
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onSprinklerInteract(net.momirealms.customcrops.api.event.SprinklerInteractEvent event) {
        if (!isCustomCropsEnabled) {
            return;
        }
        
        Player player = event.getPlayer();
        Location location = event.location();
        
        String barrioId = barrioManager.getBarrioIdAtLocation(location);
        if (barrioId != null) {
            // Check if player has custom_crops_access permission in this barrio
            if (!plugin.getPermissionManager().hasPermission(barrioId, player, "custom_crops_access")) {
                event.setCancelled(true);
                player.sendMessage(plugin.getMessage("messages.custom_crops_no_permission")
                    .replace("%action%", "activate sprinklers"));
                plugin.debug("Cancelled sprinkler interact event for player " + player.getName() + 
                           " in barrio " + barrioId + " - no custom_crops_access permission");
            }
        }
    }
}
