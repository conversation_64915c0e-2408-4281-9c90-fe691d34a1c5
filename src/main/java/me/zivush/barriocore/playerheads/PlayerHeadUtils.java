package me.zivush.barriocore.playerheads;

import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.profile.PlayerProfile;
import org.bukkit.profile.PlayerTextures;

import java.net.URL;
import java.util.Base64;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Utility class for handling player head textures using modern Bukkit API
 */
public class PlayerHeadUtils {
    private static final Logger LOGGER = Bukkit.getLogger();

    /**
     * Extracts the texture value from a player
     *
     * @param player The player to extract the texture from
     * @return The texture value, or null if extraction failed
     */
    public static String extractTextureValue(Player player) {
        if (player == null) return null;

        try {
            // Get the player's profile
            PlayerProfile profile = player.getPlayerProfile();
            if (profile == null) return null;

            // Get the textures from the profile
            PlayerTextures textures = profile.getTextures();
            if (textures == null) return null;

            // Get the skin URL
            URL skinUrl = textures.getSkin();
            if (skinUrl == null) return null;

            // Convert the URL to a base64 string
            String skinUrlStr = skinUrl.toString();
            String base64Texture = Base64.getEncoder().encodeToString(skinUrlStr.getBytes());

            return base64Texture;
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Failed to extract texture value from player: " + player.getName(), e);
        }

        return null;
    }

    /**
     * Applies a texture value to a player head item
     *
     * @param head The ItemStack to apply the texture to
     * @param textureValue The texture value to apply
     * @return The modified ItemStack, or the original if application failed
     */
    public static ItemStack applyTextureValueToHead(ItemStack head, String textureValue) {
        if (head == null || head.getType() != org.bukkit.Material.PLAYER_HEAD || textureValue == null) {
            return head;
        }

        try {
            SkullMeta meta = (SkullMeta) head.getItemMeta();
            if (meta == null) return head;

            // Create a new player profile
            PlayerProfile profile = Bukkit.createPlayerProfile(UUID.randomUUID());
            PlayerTextures textures = profile.getTextures();

            // Decode the base64 texture
            byte[] decodedBytes = Base64.getDecoder().decode(textureValue);
            String skinUrl = new String(decodedBytes);

            // Set the skin URL
            textures.setSkin(new URL(skinUrl));
            profile.setTextures(textures);

            // Apply the profile to the skull
            meta.setOwnerProfile(profile);
            head.setItemMeta(meta);
            return head;
        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Failed to apply texture value to skull", e);
            return head;
        }
    }

    /**
     * Applies a player to a head item using the standard Bukkit API
     *
     * @param head The ItemStack to apply the player to
     * @param player The OfflinePlayer to apply
     * @return The modified ItemStack
     */
    public static ItemStack applyPlayerToHead(ItemStack head, OfflinePlayer player) {
        if (head == null || head.getType() != org.bukkit.Material.PLAYER_HEAD || player == null) {
            return head;
        }

        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return head;

        meta.setOwningPlayer(player);
        head.setItemMeta(meta);

        return head;
    }
}
