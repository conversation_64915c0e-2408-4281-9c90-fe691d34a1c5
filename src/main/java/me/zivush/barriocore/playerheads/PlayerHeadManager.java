package me.zivush.barriocore.playerheads;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.scheduler.BukkitTask;

import java.sql.SQLException;
import java.util.Base64;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * Manages player head textures, caching them in memory and periodically saving to database.
 */
public class PlayerHeadManager {
    private final BarrioCore plugin;
    private final Database database;

    // Cache of player head textures
    private final Map<UUID, String> playerHeadTextures = new ConcurrentHashMap<>();

    // Set of players with updated textures that need to be saved
    private final Set<UUID> updatedPlayers = ConcurrentHashMap.newKeySet();

    // Scheduler tasks
    private BukkitTask saveTask;

    // Configuration
    private final long saveIntervalTicks;

    /**
     * Constructor for PlayerHeadManager
     *
     * @param plugin The BarrioCore plugin instance
     */
    public PlayerHeadManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.database = plugin.getDatabase();

        // Get save interval from config (default to 5 minutes)
        int saveIntervalMinutes = plugin.getConfig().getInt("player_heads.save_interval_minutes", 5);
        this.saveIntervalTicks = saveIntervalMinutes * 60 * 20L; // Convert minutes to ticks

        // Create the database table if it doesn't exist
        createTable();
    }

    /**
     * Initializes the manager, loading data and starting scheduled tasks
     */
    public void initialize() {
        // Load all player head textures from database
        loadAllPlayerHeads();

        // Start periodic saving task
        startPeriodicSaving();

        plugin.getLogger().info("PlayerHeadManager initialized");
    }

    /**
     * Creates the database table if it doesn't exist
     */
    private void createTable() {
        try {
            database.executeUpdate(
                "CREATE TABLE IF NOT EXISTS barrio_player_heads (" +
                "player_uuid VARCHAR(36) PRIMARY KEY, " +
                "texture_value TEXT NOT NULL, " +
                "last_updated BIGINT NOT NULL)"
            );
            plugin.getLogger().info("Created barrio_player_heads table if it didn't exist");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error creating barrio_player_heads table", e);
        }
    }

    /**
     * Loads all player head textures from the database
     */
    private void loadAllPlayerHeads() {
        try {
            database.executeQuery(
                "SELECT player_uuid, texture_value FROM barrio_player_heads",
                rs -> {
                    int count = 0;
                    while (rs.next()) {
                        try {
                            UUID playerUuid = UUID.fromString(rs.getString("player_uuid"));
                            String textureValue = rs.getString("texture_value");

                            playerHeadTextures.put(playerUuid, textureValue);
                            count++;
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid UUID in barrio_player_heads table: " + rs.getString("player_uuid"));
                        }
                    }
                    plugin.getLogger().info("Loaded " + count + " player head textures from database");
                    return null;
                }
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error loading player head textures", e);
        }
    }

    /**
     * Starts the periodic saving task
     */
    private void startPeriodicSaving() {
        // Cancel existing task if it exists
        if (saveTask != null && !saveTask.isCancelled()) {
            saveTask.cancel();
        }

        // Start new task
        saveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::saveUpdatedPlayerHeads, saveIntervalTicks, saveIntervalTicks);
        plugin.getLogger().info("Started periodic saving of player head textures (every " + (saveIntervalTicks / 20 / 60) + " minutes)");
    }

    /**
     * Saves all updated player head textures to the database
     */
    private void saveUpdatedPlayerHeads() {
        if (updatedPlayers.isEmpty()) {
            plugin.debugFine("No player head textures to save");
            return;
        }

        plugin.getLogger().info("Saving " + updatedPlayers.size() + " updated player head textures to database");

        // Create a copy of the set to avoid concurrent modification
        Set<UUID> playersToSave = ConcurrentHashMap.newKeySet();
        playersToSave.addAll(updatedPlayers);

        for (UUID playerUuid : playersToSave) {
            String textureValue = playerHeadTextures.get(playerUuid);
            if (textureValue != null) {
                try {
                    database.executeUpdate(
                        "INSERT INTO barrio_player_heads (player_uuid, texture_value, last_updated) " +
                        "VALUES (?, ?, ?) " +
                        "ON DUPLICATE KEY UPDATE texture_value = ?, last_updated = ?",
                        playerUuid.toString(), textureValue, System.currentTimeMillis(),
                        textureValue, System.currentTimeMillis()
                    );

                    // Remove from updated players set
                    updatedPlayers.remove(playerUuid);
                } catch (Exception e) {
                    plugin.getLogger().log(Level.SEVERE, "Error saving player head texture for " + playerUuid, e);
                }
            }
        }

        plugin.getLogger().info("Saved player head textures to database. Remaining to save: " + updatedPlayers.size());
    }

    /**
     * Checks if a player has a cached head texture
     *
     * @param playerUuid The UUID of the player
     * @return true if the player has a cached texture, false otherwise
     */
    public boolean hasPlayerHeadTexture(UUID playerUuid) {
        return playerHeadTextures.containsKey(playerUuid);
    }

    /**
     * Gets the cached head texture for a player
     *
     * @param playerUuid The UUID of the player
     * @return The texture value, or null if not cached
     */
    public String getPlayerHeadTexture(UUID playerUuid) {
        return playerHeadTextures.get(playerUuid);
    }

    /**
     * Updates the player head texture in the cache
     *
     * @param player The player whose head texture to update
     */
    public void updatePlayerHeadTexture(Player player) {
        if (player == null) return;

        try {
            UUID playerUuid = player.getUniqueId();

            // Extract texture value using our utility
            String textureValue = extractTextureValue(player);

            if (textureValue != null) {
                // Check if the texture has changed
                String existingTexture = playerHeadTextures.get(playerUuid);
                if (existingTexture == null || !existingTexture.equals(textureValue)) {
                    // Update the cache and mark for saving
                    playerHeadTextures.put(playerUuid, textureValue);
                    updatedPlayers.add(playerUuid);
                    plugin.debugFine("Updated player head texture for " + player.getName() + " (" + playerUuid + ")");
                }
            } else {
                plugin.debugFine("Could not extract texture value for player: " + player.getName());
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Error updating player head texture for " + player.getName(), e);
        }
    }

    /**
     * Extracts the texture value from a player
     *
     * @param player The player to extract the texture from
     * @return The texture value, or null if extraction failed
     */
    private String extractTextureValue(Player player) {
        return PlayerHeadUtils.extractTextureValue(player);
    }

    /**
     * Applies a cached texture to a player head item
     *
     * @param head The ItemStack to apply the texture to
     * @param playerUuid The UUID of the player whose texture to apply
     * @return The modified ItemStack, or the original if no texture was found
     */
    public ItemStack applyTextureToHead(ItemStack head, UUID playerUuid) {
        if (head == null || head.getType() != org.bukkit.Material.PLAYER_HEAD || playerUuid == null) {
            return head;
        }

        try {
            String textureValue = playerHeadTextures.get(playerUuid);
            if (textureValue == null) {
                // No cached texture, fall back to standard method
                OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
                return applyPlayerToHead(head, player);
            }

            // Apply the texture using our utility
            ItemStack result = PlayerHeadUtils.applyTextureValueToHead(head, textureValue);
            if (result != null) {
                return result;
            } else {
                // If texture application failed, fall back to standard method
                plugin.debugFine("Failed to apply texture value, falling back to standard method for " + playerUuid);
                OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
                return applyPlayerToHead(head, player);
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "Error applying texture to head for " + playerUuid, e);
            // Fall back to standard method on error
            OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
            return applyPlayerToHead(head, player);
        }
    }

    /**
     * Applies a player to a head item using the standard Bukkit API
     *
     * @param head The ItemStack to apply the player to
     * @param player The OfflinePlayer to apply
     * @return The modified ItemStack
     */
    public ItemStack applyPlayerToHead(ItemStack head, OfflinePlayer player) {
        return PlayerHeadUtils.applyPlayerToHead(head, player);
    }

    /**
     * Applies a texture value to a head item
     *
     * @param head The ItemStack to apply the texture to
     * @param textureValue The texture value to apply
     * @return The modified ItemStack
     */
    private ItemStack applyTextureValueToHead(ItemStack head, String textureValue) {
        return PlayerHeadUtils.applyTextureValueToHead(head, textureValue);
    }

    /**
     * Saves all updated player head textures and shuts down the manager
     */
    public void shutdown() {
        // Cancel the save task
        if (saveTask != null && !saveTask.isCancelled()) {
            saveTask.cancel();
        }

        // Save all updated player heads
        saveUpdatedPlayerHeads();

        plugin.getLogger().info("PlayerHeadManager shut down");
    }
}
