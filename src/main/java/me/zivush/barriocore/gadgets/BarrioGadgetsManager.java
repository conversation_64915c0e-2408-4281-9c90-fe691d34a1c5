package me.zivush.barriocore.gadgets;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

public class BarrioGadgetsManager {

    private final BarrioCore plugin;
    private final Map<String, BarrioGadgetSettings> loadedSettings = new ConcurrentHashMap<>();
    private final Map<String, Long> lastActivity = new ConcurrentHashMap<>();
    private final long inactivityTimeoutMillis;
    private BukkitTask inactivityCheckTask;

    public BarrioGadgetsManager(BarrioCore plugin) {
        this.plugin = plugin;
        this.inactivityTimeoutMillis = plugin.getConfig().getLong("gadgets.inactivity_unload_minutes", 5) * 60 * 1000;
        startInactivityCheck();
    }

    public BarrioGadgetSettings getSettings(String barrioId) {
        updateActivity(barrioId); // Update activity on access
        boolean alreadyLoaded = loadedSettings.containsKey(barrioId);
        BarrioGadgetSettings settings = loadedSettings.computeIfAbsent(barrioId, this::loadSettingsFromDb);
        if (!alreadyLoaded) {
            plugin.debugFine("Loaded settings for barrio: " + barrioId + " via getSettings()");
        }
        return settings;
    }

    public void loadSettingsForBarrio(String barrioId) {
        if (!loadedSettings.containsKey(barrioId)) {
            BarrioGadgetSettings settings = loadSettingsFromDb(barrioId);
            loadedSettings.put(barrioId, settings);
        } else {
            // Just update activity timestamp without reloading from DB
            plugin.debugFinest("Settings already loaded for barrio: " + barrioId);
        }
        updateActivity(barrioId);
    }

    private BarrioGadgetSettings loadSettingsFromDb(String barrioId) {
        plugin.getLogger().info("Loading gadget settings for barrio: " + barrioId);
        return plugin.getDatabase().executeQuery(
                "SELECT * FROM barrio_gadgets WHERE barrio_id = ?",
                rs -> {
                    if (rs.next()) {
                        boolean creeperDamage = rs.getBoolean("toggle_creeper_damage");
                        boolean creeperBlockDamage;
                        boolean ghastDamage = rs.getBoolean("toggle_ghast_damage");
                        boolean ghastBlockDamage;

                        try {
                            creeperBlockDamage = rs.getBoolean("toggle_creeper_block_damage");
                        } catch (SQLException e) {
                            // If the column doesn't exist yet, use the same value as creeperDamage for backward compatibility
                            creeperBlockDamage = creeperDamage;
                            plugin.getLogger().warning("toggle_creeper_block_damage column not found, using toggle_creeper_damage value");
                        }

                        try {
                            ghastBlockDamage = rs.getBoolean("toggle_ghast_block_damage");
                        } catch (SQLException e) {
                            // If the column doesn't exist yet, use the same value as ghastDamage for backward compatibility
                            ghastBlockDamage = ghastDamage;
                            plugin.getLogger().warning("toggle_ghast_block_damage column not found, using toggle_ghast_damage value");
                        }

                        return new BarrioGadgetSettings(
                                barrioId,
                                creeperDamage,
                                creeperBlockDamage,
                                ghastDamage,
                                ghastBlockDamage,
                                rs.getBoolean("toggle_fire_spread"),
                                rs.getBoolean("toggle_enderman_grief"),
                                rs.getBoolean("toggle_ravager_grief"),
                                rs.getBoolean("show_entry_title"),
                                rs.getBoolean("show_entry_chat")
                        );
                    } else {
                        // Check if the barrio exists before creating default settings
                        boolean barrioExists = plugin.getDatabase().executeQuery(
                                "SELECT 1 FROM barrios WHERE id = ?",
                                innerRs -> {
                                    try {
                                        return innerRs.next();
                                    } catch (SQLException e) {
                                        plugin.getLogger().log(Level.SEVERE, "Error checking if barrio exists", e);
                                        return false;
                                    }
                                },
                                barrioId
                        );

                        if (barrioExists) {
                            // No settings found, create defaults and insert
                            BarrioGadgetSettings defaultSettings = new BarrioGadgetSettings(barrioId);
                            saveSettingsToDb(defaultSettings); // Save the defaults
                            return defaultSettings;
                        } else {
                            // Barrio doesn't exist, just return default settings without saving
                            plugin.getLogger().warning("Creating temporary gadget settings for non-existent barrio: " + barrioId);
                            return new BarrioGadgetSettings(barrioId);
                        }
                    }
                },
                barrioId
        );
    }

    public void saveSettings(BarrioGadgetSettings settings) {
        if (settings == null) return;
        // Update cache immediately
        loadedSettings.put(settings.getBarrioId(), settings);
        // Save to DB asynchronously to prevent main thread lag
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                saveSettingsToDb(settings);
            }
        }.runTaskAsynchronously(plugin);
    }

    private void saveSettingsToDb(BarrioGadgetSettings settings) {
        // Check if the barrio exists before trying to save settings
        boolean barrioExists = plugin.getDatabase().executeQuery(
                "SELECT 1 FROM barrios WHERE id = ?",
                rs -> {
                    try {
                        return rs.next();
                    } catch (SQLException e) {
                        plugin.getLogger().log(Level.SEVERE, "Error checking if barrio exists", e);
                        return false;
                    }
                },
                settings.getBarrioId()
        );

        if (!barrioExists) {
            plugin.getLogger().warning("Cannot save gadget settings for barrio " + settings.getBarrioId() + ": Barrio does not exist");
            return;
        }

        plugin.getDatabase().executeUpdate(
                "INSERT INTO barrio_gadgets (barrio_id, toggle_creeper_damage, toggle_creeper_block_damage, toggle_ghast_damage, toggle_ghast_block_damage, toggle_fire_spread, toggle_enderman_grief, toggle_ravager_grief, show_entry_title, show_entry_chat) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                        "ON DUPLICATE KEY UPDATE " +
                        "toggle_creeper_damage = VALUES(toggle_creeper_damage), " +
                        "toggle_creeper_block_damage = VALUES(toggle_creeper_block_damage), " +
                        "toggle_ghast_damage = VALUES(toggle_ghast_damage), " +
                        "toggle_ghast_block_damage = VALUES(toggle_ghast_block_damage), " +
                        "toggle_fire_spread = VALUES(toggle_fire_spread), " +
                        "toggle_enderman_grief = VALUES(toggle_enderman_grief), " +
                        "toggle_ravager_grief = VALUES(toggle_ravager_grief), " +
                        "show_entry_title = VALUES(show_entry_title), " +
                        "show_entry_chat = VALUES(show_entry_chat)",
                settings.getBarrioId(),
                settings.isCreeperDamageEnabled(),
                settings.isCreeperBlockDamageEnabled(),
                settings.isGhastDamageEnabled(),
                settings.isGhastBlockDamageEnabled(),
                settings.isFireSpreadEnabled(),
                settings.isEndermanGriefEnabled(),
                settings.isRavagerGriefEnabled(),
                settings.isShowEntryTitleEnabled(),
                settings.isShowEntryChatEnabled()
        );
        plugin.debugFiner("Saved gadget settings for barrio: " + settings.getBarrioId());
    }

    public void updateActivity(String barrioId) {
        if (barrioId != null) {
            lastActivity.put(barrioId, System.currentTimeMillis());
            plugin.debugFinest("Updated activity for barrio: " + barrioId);
        }
    }

    private void startInactivityCheck() {
        inactivityCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                unloadInactiveBarrios();
            }
            // Check every minute
        }.runTaskTimer(plugin, 1200L, 1200L); // 60 seconds * 20 ticks/second
    }

    public void stopInactivityCheck() {
        if (inactivityCheckTask != null && !inactivityCheckTask.isCancelled()) {
            inactivityCheckTask.cancel();
        }
    }

    private void unloadInactiveBarrios() {
        long now = System.currentTimeMillis();
        plugin.debugFiner("Running inactivity check for gadget settings...");
        loadedSettings.keySet().retainAll(lastActivity.keySet()); // Clean up potential mismatch

        // Create a map of barrio IDs with players present
        Map<String, Boolean> barriosWithPlayers = new HashMap<>();

        // Process all online players once, rather than for each barrio
        for (Player player : Bukkit.getOnlinePlayers()) {
            String barrioId = getBarrioIdFromWorld(player.getWorld());
            if (barrioId != null) {
                barriosWithPlayers.put(barrioId, true);
            }
        }

        // Create a list to store barrios to remove (to avoid ConcurrentModificationException)
        List<String> barriosToRemove = new ArrayList<>();

        // Check each barrio for inactivity
        lastActivity.forEach((barrioId, lastSeen) -> {
            if (now - lastSeen > inactivityTimeoutMillis) {
                // Check if any player is currently in this barrio's worlds
                if (!barriosWithPlayers.containsKey(barrioId)) {
                    plugin.getLogger().info("Unloading inactive gadget settings for barrio: " + barrioId);
                    barriosToRemove.add(barrioId);
                } else {
                    plugin.debugFiner("Skipping unload for barrio " + barrioId + ": Players present.");
                }
            }
        });

        // Remove the inactive barrios
        for (String barrioId : barriosToRemove) {
            loadedSettings.remove(barrioId);
            lastActivity.remove(barrioId);
        }

        plugin.debugFiner("Inactivity check finished. Loaded settings: " + loadedSettings.size());
    }

    /**
     * Unloads gadget settings for a barrio from memory.
     * Used when deleting a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void unloadSettings(String barrioId) {
        if (barrioId == null) return;
        loadedSettings.remove(barrioId);
        lastActivity.remove(barrioId);
    }

    // Helper to get barrio ID from world name, returns null if not a barrio world
    public static String getBarrioIdFromWorld(World world) {
        if (world == null) return null;
        String worldName = world.getName();
        if (worldName.startsWith("Barrios/")) {
            // More efficient than splitting the entire string
            int startIndex = 8; // "Barrios/".length()
            int endIndex = worldName.indexOf('/', startIndex);
            if (endIndex == -1) {
                // No second slash, return everything after "Barrios/"
                return worldName.substring(startIndex);
            } else {
                // Return the part between the first and second slash
                return worldName.substring(startIndex, endIndex);
            }
        }
        return null;
    }
}