package me.zivush.barriocore.gadgets;

public class BarrioGadgetSettings {
    private final String barrioId;
    private boolean creeperDamage; // Now represents player damage only
    private boolean creeperBlockDamage; // New field for block damage
    private boolean ghastDamage; // Now represents player damage only
    private boolean ghastBlockDamage; // New field for block damage
    private boolean fireSpread;
    private boolean endermanGrief;
    private boolean ravagerGrief;
    private boolean showEntryTitle;
    private boolean showEntryChat;
    // Constructor for loading from DB or creating defaults
    public BarrioGadgetSettings(String barrioId, boolean creeperDamage, boolean creeperBlockDamage,
                              boolean ghastDamage, boolean ghastBlockDamage, boolean fireSpread,
                              boolean endermanGrief, boolean ravagerGrief, boolean showEntryTitle,
                              boolean showEntryChat) {
        this.barrioId = barrioId;
        this.creeperDamage = creeperDamage;
        this.creeperBlockDamage = creeperBlockDamage;
        this.ghastDamage = ghastDamage;
        this.ghastBlockDamage = ghastBlockDamage;
        this.fireSpread = fireSpread;
        this.endermanGrief = endermanGrief;
        this.ravagerGrief = ravagerGrief;
        this.showEntryTitle = showEntryTitle;
        this.showEntryChat = showEntryChat;
    }

    // Constructor for backward compatibility
    public BarrioGadgetSettings(String barrioId, boolean creeperDamage, boolean ghastDamage, boolean fireSpread,
                              boolean endermanGrief, boolean ravagerGrief, boolean showEntryTitle,
                              boolean showEntryChat) {
        this(barrioId, creeperDamage, creeperDamage, ghastDamage, ghastDamage, fireSpread, endermanGrief, ravagerGrief, showEntryTitle, showEntryChat);
    }

    // Constructor for backward compatibility with creeper block damage but not ghast block damage
    public BarrioGadgetSettings(String barrioId, boolean creeperDamage, boolean creeperBlockDamage, boolean ghastDamage, boolean fireSpread,
                              boolean endermanGrief, boolean ravagerGrief, boolean showEntryTitle,
                              boolean showEntryChat) {
        this(barrioId, creeperDamage, creeperBlockDamage, ghastDamage, ghastDamage, fireSpread, endermanGrief, ravagerGrief, showEntryTitle, showEntryChat);
    }

    // Default constructor (creates a new row with defaults)
    public BarrioGadgetSettings(String barrioId) {
        this(barrioId, true, true, true, true, true, true, true, true, true);
    }


    // --- Getters ---
    public String getBarrioId() { return barrioId; }
    public boolean isCreeperDamageEnabled() { return creeperDamage; } // Player damage
    public boolean isCreeperBlockDamageEnabled() { return creeperBlockDamage; } // Block damage
    public boolean isGhastDamageEnabled() { return ghastDamage; } // Player damage
    public boolean isGhastBlockDamageEnabled() { return ghastBlockDamage; } // Block damage
    public boolean isFireSpreadEnabled() { return fireSpread; }
    public boolean isEndermanGriefEnabled() { return endermanGrief; }
    public boolean isRavagerGriefEnabled() { return ravagerGrief; }
    public boolean isShowEntryTitleEnabled() { return showEntryTitle; }
    public boolean isShowEntryChatEnabled() { return showEntryChat; }

    // --- Toggles (Setters) ---
    public void setCreeperDamage(boolean creeperDamage) { this.creeperDamage = creeperDamage; } // Player damage
    public void setCreeperBlockDamage(boolean creeperBlockDamage) { this.creeperBlockDamage = creeperBlockDamage; } // Block damage
    public void setGhastDamage(boolean ghastDamage) { this.ghastDamage = ghastDamage; } // Player damage
    public void setGhastBlockDamage(boolean ghastBlockDamage) { this.ghastBlockDamage = ghastBlockDamage; } // Block damage
    public void setFireSpread(boolean fireSpread) { this.fireSpread = fireSpread; }
    public void setEndermanGrief(boolean endermanGrief) { this.endermanGrief = endermanGrief; }
    public void setRavagerGrief(boolean ravagerGrief) { this.ravagerGrief = ravagerGrief; }
    public void setShowEntryTitle(boolean showEntryTitle) { this.showEntryTitle = showEntryTitle; }
    public void setShowEntryChat(boolean showEntryChat) { this.showEntryChat = showEntryChat; }
}