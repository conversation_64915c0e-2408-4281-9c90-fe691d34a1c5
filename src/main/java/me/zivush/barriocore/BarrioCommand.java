package me.zivush.barriocore;

import com.onarandombox.MultiverseCore.MultiverseCore;
import me.zivush.barriocore.gadgets.BarrioGadgetSettings;
import me.zivush.barriocore.gadgets.BarrioGadgetsManager;
import me.zivush.barriocore.gui.*;
import me.zivush.barriocore.modes.BarrioMode;
import me.zivush.barriocore.playerdata.BarrioPlayerData;
import me.zivush.barriocore.playerdata.BarrioPlayerDataManager;
import me.zivush.barriocore.ranks.BarrioRankManager;
import me.zivush.barriocore.ranks.Rank;
import org.bukkit.*;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.io.File;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class BarrioCommand implements CommandExecutor {
    private final BarrioCore plugin;
    private final Database database;
    private final BarrioRankManager rankManager;
    private final BarrioPlayerDataManager playerDataManager;
    private final Map<UUID, TransferRequest> pendingTransfers = new HashMap<>();
    private final Map<UUID, Long> pendingDeleteConfirmations = new HashMap<>();
    private final Map<UUID, Long> createCooldowns = new HashMap<>();

    private static class TransferRequest {
        final String barrioId;
        final UUID targetPlayer;
        final long timestamp;

        TransferRequest(String barrioId, UUID targetPlayer) {
            this.barrioId = barrioId;
            this.targetPlayer = targetPlayer;
            this.timestamp = System.currentTimeMillis();
        }

        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > 60000; // 1 minute expiration
        }
    }

    public BarrioCommand(BarrioCore plugin, Database database) {
        this.plugin = plugin;
        this.database = database;
        this.rankManager = plugin.getRankManager();
        this.playerDataManager = plugin.getPlayerDataManager();
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(plugin.getMessage("messages.player_only"));
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Open the main barrio GUI when no arguments are provided
            openMainGUI(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "admin":
                handleAdminCommands(player, args);
                break;
            case "visit":
            case "tp":
                handleVisitCommand(player, args);
                break;
            case "transfer":
                handleTransferCommand(player, args);
                break;
            case "create":
                handleCreateCommand(player, args);
                break;
            case "expand":
                handleExpandCommand(player, args);
                break;
            case "members":
                handleMembersCommand(player, args);
                break;
            case "ban":
                handleBanCommand(player, args);
                break;
            case "unban":
                handleUnbanCommand(player, args);
                break;
            case "kick":
                handleKickCommand(player, args);
                break;
            case "gadget":
                handleGadgetCommand(player, args);
                break;
            case "rent":
                handleRentCommand(player, args);
                break;
            case "info":
                handleInfoCommand(player);
                break;
            case "setspawn":
                handleSetSpawnCommand(player);
                break;
            case "setdefault":
                handleSetDefaultCommand(player);
                break;
            case "home":
                handleHomeCommand(player, args);
                break;
            case "spawn":
                handleSpawnCommand(player);
                break;
            case "permissions":
                handlePermissionsCommand(player, args);
                break;
            case "settings":
                handleSettingsCommand(player);
                break;
            case "upgrades":
                handleUpgradesCommand(player);
                break;
            case "delete":
                handleDeleteCommand(player);
                break;
            case "rate":
                handleRateCommand(player, args);
                break;
            case "ratings":
                handleRatingsCommand(player, args);
                break;
            default:
                player.sendMessage(plugin.getMessage("messages.invalid_usage"));
                break;
        }
        return true;
    }

    private void handleAdminCommands(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        switch (args[1].toLowerCase()) {
            case "create":
                handleAdminCreate(player, args);
                break;
            case "delete":
                handleAdminDelete(player, args);
                break;
            case "transfer":
                handleAdminTransfer(player, args);
                break;
            case "teleport":
                handleAdminTeleport(player, args);
                break;
            case "expand":
                handleAdminExpand(player, args);
                break;
            case "mode":
                handleAdminMode(player, args);
                break;
            case "setserverspawn":
                handleSetServerSpawn(player);
                break;
            case "setspawn":
                handleAdminSetSpawn(player);
                break;
            case "settings":
                handleAdminSettings(player, args);
                break;
            case "gadgets":
                handleAdminGadgets(player, args);
                break;
            case "reload":
                handleAdminReload(player);
                break;
            case "debug":
                handleAdminDebug(player, args);
                break;
            default:
                player.sendMessage(plugin.getMessage("messages.invalid_usage"));
        }
    }

    private void handleAdminSettings(Player player, String[] args) {
        // Check permission
        if (!player.hasPermission("barrio.settings.*")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Check if the command has the correct number of arguments
        if (args.length != 4) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        String setting = args[2].toLowerCase();
        String toggleValue = args[3].toLowerCase();
        boolean enabled = toggleValue.equals("true");

        // Get the world settings for this barrio
        me.zivush.barriocore.worldsettings.BarrioWorldSettings settings = plugin.getWorldSettingsManager().getWorldSettings(barrioId);
        if (settings == null) {
            player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
            return;
        }

        // Apply the setting based on the setting name
        switch (setting) {
            case "pvp":
                settings.setPvpEnabled(enabled);
                player.sendMessage(plugin.getMessage("messages.settings_pvp_" + (enabled ? "enabled" : "disabled")));
                break;
            case "hunger":
                settings.setHungerLossEnabled(enabled);
                player.sendMessage(plugin.getMessage("messages.settings_hunger_" + (enabled ? "enabled" : "disabled")));
                break;
            case "time":
                try {
                    // Accept specific time mode values
                    me.zivush.barriocore.worldsettings.BarrioWorldSettings.TimeMode timeMode =
                        me.zivush.barriocore.worldsettings.BarrioWorldSettings.TimeMode.valueOf(toggleValue.toUpperCase());
                    settings.setTimeMode(timeMode);
                    player.sendMessage(plugin.getMessage("messages.settings_time_changed")
                        .replace("%mode%", timeMode.name()));
                } catch (IllegalArgumentException e) {
                    // If invalid value, show available options
                    me.zivush.barriocore.worldsettings.BarrioWorldSettings.TimeMode[] timeModes =
                        me.zivush.barriocore.worldsettings.BarrioWorldSettings.TimeMode.values();
                    StringBuilder options = new StringBuilder();
                    for (me.zivush.barriocore.worldsettings.BarrioWorldSettings.TimeMode mode : timeModes) {
                        options.append(mode.name()).append(", ");
                    }
                    player.sendMessage(ChatColor.RED + "Invalid time mode. Available options: " +
                        options.substring(0, options.length() - 2));
                }
                break;
            case "weather":
                try {
                    // Accept specific weather mode values
                    me.zivush.barriocore.worldsettings.BarrioWorldSettings.WeatherMode weatherMode =
                        me.zivush.barriocore.worldsettings.BarrioWorldSettings.WeatherMode.valueOf(toggleValue.toUpperCase());
                    settings.setWeatherMode(weatherMode);

                    // Set the fixed weather type based on the mode
                    switch (weatherMode) {
                        case CLEAR:
                            settings.setFixedWeather(WeatherType.CLEAR);
                            break;
                        case STORM:
                        case RAIN:
                            settings.setFixedWeather(WeatherType.DOWNFALL);
                            break;
                        default:
                            // OFF mode - don't change fixed weather
                            break;
                    }

                    player.sendMessage(plugin.getMessage("messages.settings_weather_changed")
                        .replace("%mode%", weatherMode.name()));
                } catch (IllegalArgumentException e) {
                    // If invalid value, show available options
                    me.zivush.barriocore.worldsettings.BarrioWorldSettings.WeatherMode[] weatherModes =
                        me.zivush.barriocore.worldsettings.BarrioWorldSettings.WeatherMode.values();
                    StringBuilder options = new StringBuilder();
                    for (me.zivush.barriocore.worldsettings.BarrioWorldSettings.WeatherMode mode : weatherModes) {
                        options.append(mode.name()).append(", ");
                    }
                    player.sendMessage(ChatColor.RED + "Invalid weather mode. Available options: " +
                        options.substring(0, options.length() - 2));
                }
                break;
            case "difficulty":
                try {
                    // Accept specific difficulty values
                    Difficulty difficulty = Difficulty.valueOf(toggleValue.toUpperCase());
                    settings.setDifficulty(difficulty);
                    player.sendMessage(plugin.getMessage("messages.settings_difficulty_changed")
                        .replace("%difficulty%", difficulty.name()));
                } catch (IllegalArgumentException e) {
                    // If invalid value, show available options
                    Difficulty[] difficulties = Difficulty.values();
                    StringBuilder options = new StringBuilder();
                    for (Difficulty diff : difficulties) {
                        options.append(diff.name()).append(", ");
                    }
                    player.sendMessage(ChatColor.RED + "Invalid difficulty. Available options: " +
                        options.substring(0, options.length() - 2));
                }
                break;
            case "spawn":
                settings.setUseBarrioSpawn(enabled);
                player.sendMessage(plugin.getMessage("messages.settings_spawn_" + (enabled ? "enabled" : "disabled")));
                break;
            case "respawn":
                settings.setUseServerSpawn(enabled);
                player.sendMessage(plugin.getMessage("messages.settings_respawn_" + (enabled ? "server" : "barrio")));
                break;
            case "rent":
                settings.setRentEnabled(enabled);
                player.sendMessage(plugin.getMessage("messages.rent_toggled_" + (enabled ? "on" : "off")));
                break;
            case "chat":
                settings.setBarrioChatOnly(enabled);
                player.sendMessage(plugin.getMessage("messages.settings_barrio_chat_enabled")
                    .replace("%status%", enabled ?
                        plugin.getMessage("messages.gadgets_status_enabled") :
                        plugin.getMessage("messages.gadgets_status_disabled")));
                break;
            default:
                player.sendMessage(plugin.getMessage("messages.invalid_setting"));
                return;
        }

        // Save the settings
        plugin.getWorldSettingsManager().saveWorldSettings(barrioId, false);
    }

    private void handleAdminGadgets(Player player, String[] args) {
        // Check permission
        if (!player.hasPermission("barrio.gadgets.*")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Check if the command has the correct number of arguments
        if (args.length != 4) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        String gadget = args[2].toLowerCase();
        String toggleValue = args[3].toLowerCase();
        boolean enabled = toggleValue.equals("true");

        // Get the gadget settings for this barrio
        BarrioGadgetSettings settings = plugin.getGadgetsManager().getSettings(barrioId);
        if (settings == null) {
            player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
            return;
        }

        // Apply the setting based on the gadget name
        switch (gadget) {
            case "creeper_damage":
                settings.setCreeperDamage(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_creeper_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            case "creeper_block_damage":
                settings.setCreeperBlockDamage(enabled);
                player.sendMessage("Creeper block damage " + (enabled ? "enabled" : "disabled"));
                break;
            case "ghast_damage":
                settings.setGhastDamage(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_ghast_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            case "fire_spread":
                settings.setFireSpread(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_fire_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            case "enderman_grief":
                settings.setEndermanGrief(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_enderman_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            case "ravager_grief":
                settings.setRavagerGrief(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_ravager_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            case "entry_title":
                settings.setShowEntryTitle(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_title_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            case "entry_chat":
                settings.setShowEntryChat(enabled);
                player.sendMessage(plugin.getMessage("messages.gadgets_chat_toggle")
                    .replace("%status%", enabled ? "enabled" : "disabled"));
                break;
            default:
                player.sendMessage(plugin.getMessage("messages.invalid_gadget"));
                return;
        }

        // Save the settings
        plugin.getGadgetsManager().saveSettings(settings);
    }

    private void handleSetServerSpawn(Player player) {
        if (!player.hasPermission("barrio.admin.setserverspawn")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        Location loc = player.getLocation();
        plugin.getConfig().set("server_spawn.world", loc.getWorld().getName());
        plugin.getConfig().set("server_spawn.x", loc.getX());
        plugin.getConfig().set("server_spawn.y", loc.getY());
        plugin.getConfig().set("server_spawn.z", loc.getZ());
        plugin.saveConfig();

        player.sendMessage(plugin.getMessage("messages.server_spawn_set"));
    }

    /**
     * Handles the admin setspawn command.
     * Sets the spawn location for the current barrio, bypassing normal permission checks.
     *
     * @param player The player executing the command
     */
    private void handleAdminSetSpawn(Player player) {
        if (!player.hasPermission("barrio.admin.setspawn")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        Location loc = player.getLocation();
        player.getWorld().setSpawnLocation(loc.getBlockX(), loc.getBlockY(), loc.getBlockZ());
        player.sendMessage(plugin.getMessage("messages.spawn_set") + " (Admin override)");
    }

    /**
     * Handles the admin reload command.
     * Reloads all configuration files.
     *
     * @param player The player executing the command
     */
    private void handleAdminReload(Player player) {
        if (!player.hasPermission("barrio.admin.reload")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        try {
            // Reload all configuration files
            plugin.reloadConfigurations();
            player.sendMessage(plugin.getMessage("messages.config_reloaded"));
        } catch (Exception e) {
            player.sendMessage(plugin.getMessage("messages.reload_error"));
            plugin.getLogger().severe("Error reloading configuration: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handles the admin debug command.
     * Toggles debug mode on or off.
     *
     * @param player The player executing the command
     * @param args The command arguments
     */
    private void handleAdminDebug(Player player, String[] args) {
        if (!player.hasPermission("barrio.admin.debug")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length < 3) {
            player.sendMessage(ChatColor.RED + "Usage: /barrio admin debug <on|off>");
            return;
        }

        String option = args[2].toLowerCase();
        boolean enabled;

        if (option.equals("on")) {
            enabled = true;
        } else if (option.equals("off")) {
            enabled = false;
        } else {
            player.sendMessage(ChatColor.RED + "Usage: /barrio admin debug <on|off>");
            return;
        }

        // Update config
        plugin.getConfig().set("debug.enabled", enabled);
        plugin.saveConfig();

        // Reload debug setting
        plugin.loadDebugSetting();

        player.sendMessage(ChatColor.GREEN + "Debug mode is now " + (enabled ? "enabled" : "disabled") + ".");
    }


    private void handleAdminCreate(Player player, String[] args) {
        if (args.length != 4 || (!args[2].equalsIgnoreCase("regular") && !args[2].equalsIgnoreCase("template"))) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String type = args[2].toLowerCase();
        if (!player.hasPermission("barrio.admin.create." + type)) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        Player targetPlayer = Bukkit.getPlayer(args[3]);
        if (targetPlayer == null) {
            player.sendMessage(plugin.getMessage("messages.player_not_found"));
            return;
        }

        if (type.equals("regular")) {
            BarrioCreateRegularGUI gui = new BarrioCreateRegularGUI(plugin, database, targetPlayer);
            plugin.setOpenGui(targetPlayer.getUniqueId(), gui);
            gui.open();
        } else {
            BarrioCreateTemplateGUI gui = new BarrioCreateTemplateGUI(plugin, database, targetPlayer);
            plugin.setOpenGui(targetPlayer.getUniqueId(), gui);
            gui.open();
        }

        player.sendMessage(plugin.getMessage("messages.barrio_created_for").replace("%player%", targetPlayer.getName()));
    }

    private void handleAdminDelete(Player player, String[] args) {
        if (!player.hasPermission("barrio.admin.delete")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length < 3) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        // Use OfflinePlayer instead of Player to support offline players
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(args[2]);

        // Check if the player has ever played on the server
        if (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_never_played", "%player%", args[2]));
            return;
        }

        String barrioId = getBarrioIdByIndex(targetPlayer.getUniqueId().toString(), args.length == 4 ? args[3] : "default");
        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
            return;
        }

        deleteBarrio(barrioId);
        player.sendMessage(plugin.getMessage("messages.barrio_deleted"));
    }

    private void handleAdminTransfer(Player player, String[] args) {
        if (!player.hasPermission("barrio.admin.transfer")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }
        if (args.length != 4) { // Requires /barrio admin transfer <barrioId> <newOwnerName>
            player.sendMessage(plugin.getMessage("messages.invalid_usage_admin_transfer", "%command%", "/barrio admin transfer <barrioId> <newOwnerName>"));
            return;
        }

        String barrioId = args[2];
        String newOwnerName = args[3];

        // Verify barrio exists
        UUID currentOwnerUUID = rankManager.getOwnerUUID(barrioId); // Use helper from RankManager
        if (currentOwnerUUID == null) {
            // Maybe check DB directly if rank manager might not have it loaded?
            // Or rely on RankManager's getOwnerUUID which checks DB.
            if (getOwnerUUIDFromDB(barrioId) == null) { // Add a direct DB check helper if needed
                player.sendMessage(plugin.getMessage("messages.barrio_id_not_found", "%barrio_id%", barrioId));
                return;
            } else {
                plugin.getLogger().info("Admin Transfer: Barrio " + barrioId + " found in DB but owner wasn't cached. Proceeding.");
                // Force load ranks for the barrio before proceeding if not loaded
                rankManager.loadRanksForBarrio(barrioId);
                currentOwnerUUID = rankManager.getOwnerUUID(barrioId); // Try again after load
                if (currentOwnerUUID == null) {
                    plugin.getLogger().severe("Admin Transfer: Failed to get owner UUID for " + barrioId + " even after load attempt.");
                    player.sendMessage(plugin.getMessage("messages.command_failed")); // Generic error
                    return;
                }
            }
        }


        OfflinePlayer newOwnerOffline = Bukkit.getOfflinePlayer(newOwnerName);
        if (!newOwnerOffline.hasPlayedBefore() && !newOwnerOffline.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_never_played", "%player%", newOwnerName));
            return;
        }
        UUID newOwnerUuid = newOwnerOffline.getUniqueId();

        // --- Perform Transfer (Admin version - no confirmation needed) ---

        // 1. Update Database using BarrioManager
        try {
            plugin.getBarrioManager().updateBarrioOwner(barrioId, newOwnerUuid);
            plugin.getLogger().info("Database (Admin): Transferred ownership of barrio " + barrioId + " to " + newOwnerUuid);
        } catch (RuntimeException e) {
            plugin.getLogger().log(java.util.logging.Level.SEVERE, "Database error during ADMIN barrio ownership transfer for " + barrioId, e);
            player.sendMessage(plugin.getMessage("messages.database_error"));
            return;
        }

        // 2. Update RankManager Cache
        // Force update the owner in memory to ensure immediate consistency
        boolean memoryUpdateSuccess = rankManager.forceUpdateOwnerInMemory(
            barrioId,
            newOwnerUuid,
            currentOwnerUUID
        );

        if (!memoryUpdateSuccess) {
            plugin.getLogger().warning("Memory update for admin ownership transfer failed for barrio " +
                barrioId + ". Owner may not be correctly reflected until server restart.");
        }

        // --- Messaging ---
        String newOwnerDisplayName = newOwnerOffline.getName() != null ? newOwnerOffline.getName() : newOwnerName;
        player.sendMessage(plugin.getMessage("messages.barrio_transferred_admin", "%barrio_id%", barrioId, "%player%", newOwnerDisplayName));

        // Notify new owner if online
        Player targetOnline = newOwnerOffline.getPlayer();
        if (targetOnline != null) {
            targetOnline.sendMessage(plugin.getMessage("messages.transfer_received_admin", "%admin%", player.getName(), "%barrio_id%", barrioId));
        }
        // Optionally notify old owner if online
        OfflinePlayer oldOwnerOffline = Bukkit.getOfflinePlayer(currentOwnerUUID);
        Player oldOwnerOnline = oldOwnerOffline.getPlayer();
        if (oldOwnerOnline != null) {
            oldOwnerOnline.sendMessage(plugin.getMessage("info.barrio_transferred_away_admin", "%barrio_id%", barrioId, "%admin%", player.getName()));
        }
    }

    private void handleAdminTeleport(Player player, String[] args) {
        if (!player.hasPermission("barrio.admin.teleport")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length < 3) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        // Use OfflinePlayer instead of Player to support offline players
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(args[2]);

        // Check if the player has ever played on the server
        if (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_not_found"));
            return;
        }

        String barrioId = getBarrioIdByIndex(targetPlayer.getUniqueId().toString(), args.length == 4 ? args[3] : "default");
        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
            return;
        }

        teleportToBarrio(player, barrioId);
        String targetName = targetPlayer.getName() != null ? targetPlayer.getName() : args[2];
        player.sendMessage(plugin.getMessage("messages.teleported").replace("%player%", targetName));
    }

    private void handleVisitCommand(Player player, String[] args) {
        if (!player.hasPermission("barrio.cmd.visit")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length < 2) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        // Check for top-rated parameter
        if (args[1].equalsIgnoreCase("top-rated")) {
            if (!player.hasPermission("barrio.cmd.visit.toprated")) {
                player.sendMessage(plugin.getMessage("messages.no_permission"));
                return;
            }

            // Get the top-rated barrio ID
            String topRatedBarrioId = plugin.getTopRatedBarrioManager().getTopRatedBarrioId();
            if (topRatedBarrioId == null) {
                player.sendMessage(plugin.getMessage("messages.rating.no_top_rated"));
                return;
            }

            // Check if player is banned from the barrio
            if (BarrioCommand.isPlayerBannedFromBarrio(plugin.getDatabase(), topRatedBarrioId, player.getUniqueId().toString())) {
                player.sendMessage(plugin.getMessage("messages.player_banned_message"));
                return;
            }

            // Get the player's rank in this barrio
            Rank playerRank = plugin.getRankManager().getRank(topRatedBarrioId, player.getUniqueId());

            // Check visit permission based on the player's rank
            Map<String, Map<Rank, Boolean>> barrioPerms = plugin.getPermissionManager().getBarrioPermissionsForAllRanks(topRatedBarrioId);
            boolean canVisit = false;

            if (barrioPerms != null) {
                Map<Rank, Boolean> visitPerms = barrioPerms.get("visit_toggle");
                if (visitPerms != null) {
                    // Get permission for the player's specific rank
                    Boolean rankPerm = visitPerms.get(playerRank);
                    if (rankPerm != null) {
                        canVisit = rankPerm;
                    } else {
                        // If no specific permission is set for this rank, check the default
                        canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
                    }
                } else {
                    // If no permission entry exists, check the default
                    canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
                }
            } else {
                // If no permissions are set for this barrio, check the default
                canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
            }

            if (!canVisit) {
                player.sendMessage(plugin.getMessage("messages.no_permission"));
                return;
            }

            // Teleport to the top-rated barrio
            teleportToBarrio(player, topRatedBarrioId);

            // Get barrio info for the message
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(topRatedBarrioId);
            String barrioName = barrioData != null && barrioData.getNickname() != null ? barrioData.getNickname() : topRatedBarrioId;
            double avgRating = plugin.getRatingManager().getAverageRating(topRatedBarrioId);
            int ratingCount = plugin.getRatingManager().getRatingCount(topRatedBarrioId);

            // Send success message
            if (ratingCount > 0) {
                player.sendMessage(plugin.getMessage("messages.teleported_top_rated")
                        .replace("%barrio_name%", barrioName)
                        .replace("%rating%", String.format("%.1f", avgRating))
                        .replace("%count%", String.valueOf(ratingCount)));
            } else {
                player.sendMessage(plugin.getMessage("messages.teleported_top_rated_no_ratings")
                        .replace("%barrio_name%", barrioName));
            }
            return;
        }

        // Regular visit command
        // Use OfflinePlayer instead of Player to support offline players
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(args[1]);

        // Check if the player has ever played on the server
        if (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_not_found"));
            return;
        }

        String barrioId = getBarrioIdByIndex(targetPlayer.getUniqueId().toString(), args.length == 3 ? args[2] : "default");
        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
            return;
        }

        if (BarrioCommand.isPlayerBannedFromBarrio(plugin.getDatabase(), barrioId, player.getUniqueId().toString())) {
            player.sendMessage(plugin.getMessage("messages.player_banned_message"));
            return;
        }

        // Get the player's rank in this barrio
        Rank playerRank = plugin.getRankManager().getRank(barrioId, player.getUniqueId());

        // Check visit permission based on the player's rank
        Map<String, Map<Rank, Boolean>> barrioPerms = plugin.getPermissionManager().getBarrioPermissionsForAllRanks(barrioId);
        boolean canVisit = false;

        if (barrioPerms != null) {
            Map<Rank, Boolean> visitPerms = barrioPerms.get("visit_toggle");
            if (visitPerms != null) {
                // Get permission for the player's specific rank
                Boolean rankPerm = visitPerms.get(playerRank);
                if (rankPerm != null) {
                    canVisit = rankPerm;
                } else {
                    // If no specific permission is set for this rank, check the default
                    canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
                }
            } else {
                // If no permission entry exists, check the default
                canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
            }
        } else {
            // If no permissions are set for this barrio, check the default
            canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
        }

        if (!canVisit) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        teleportToBarrio(player, barrioId);
        String targetName = targetPlayer.getName() != null ? targetPlayer.getName() : args[1];
        player.sendMessage(plugin.getMessage("messages.teleported").replace("%player%", targetName));
    }

    private void handleAdminMode(Player player, String[] args) {
        if (!player.hasPermission("barrio.admin.mode")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length < 3) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        BarrioMode mode;
        try {
            mode = BarrioMode.valueOf(args[2].toUpperCase());
        } catch (IllegalArgumentException e) {
            player.sendMessage(plugin.getMessage("messages.invalid_mode"));
            return;
        }

        String time = args.length > 3 ? args[3] : null;
        String groupCheck = args.length > 4 ? args[4] : "ALL";
        boolean onlineInWorld = args.length > 5 && Boolean.parseBoolean(args[5]);
        double rentPrice = (mode == BarrioMode.RENTING || mode == BarrioMode.RENTING_OFF) ?
                (args.length > 4 ? Double.parseDouble(args[4]) : 1000.0) : 0.0;

        plugin.getModeManager().setBarrioMode(barrioId, mode, time, groupCheck, onlineInWorld, rentPrice);
        player.sendMessage(plugin.getMessage("messages.mode_changed").replace("%mode%", mode.name()));
    }


    private void handleTransferCommand(Player player, String[] args) {
        // 1. Permission Check
        if (!player.hasPermission("barrio.cmd.transfer")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // 2. Handle Confirmation Subcommand Immediately
        // Allows `/barrio transfer confirm`
        if (args.length >= 2 && args[1].equalsIgnoreCase("confirm")) {
            handleTransferConfirm(player); // Delegate to the confirmation handler
            return;
        }

        // 3. Check if Player is in a Barrio World
        String worldName = player.getWorld().getName();
        String barrioId = BarrioRankManager.getBarrioIdFromWorld(player.getWorld());

        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // 4. Verify Player is the Owner using RankManager
        if (rankManager.getRank(barrioId, player.getUniqueId()) != Rank.OWNER) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // 5. Validate Argument Count (Expecting: /barrio transfer <targetPlayer>)
        if (args.length != 2) {
            // Provide specific usage message (add to messages.yml)
            player.sendMessage(plugin.getMessage("messages.invalid_usage_transfer", "%usage%", "/barrio transfer <player>"));
            return;
        }

        // 6. Identify Target Player (Allow offline)
        String targetName = args[1];
        OfflinePlayer targetOfflinePlayer = Bukkit.getOfflinePlayer(targetName); // Use Bukkit.getOfflinePlayer(UUID) if name lookup fails or UUID is preferred

        // Check if the target player has ever played on the server
        if (!targetOfflinePlayer.hasPlayedBefore() && !targetOfflinePlayer.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_never_played", "%player%", targetName));
            return;
        }
        UUID targetUuid = targetOfflinePlayer.getUniqueId();

        // 7. Prevent Self-Transfer
        if (targetUuid.equals(player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.self_transfer"));
            return;
        }

        // 8. Create and Store Pending Transfer Request
        pendingTransfers.put(player.getUniqueId(), new TransferRequest(barrioId, targetUuid));

        // 9. Send Initiation Messages to the Sender
        String targetDisplayName = targetOfflinePlayer.getName() != null ? targetOfflinePlayer.getName() : targetName; // Use real name if available
        player.sendMessage(plugin.getMessage("messages.transfer_initiated", "%player%", targetDisplayName));
        // Instruct the player how to confirm (add message to messages.yml)
        player.sendMessage(plugin.getMessage("messages.transfer_confirm", "%command%", "/barrio transfer confirm"));
    }


    private void handleTransferConfirm(Player player) {
        // 1. Retrieve Pending Request
        TransferRequest request = pendingTransfers.get(player.getUniqueId());

        // 2. Validate Request Existence and Expiration
        if (request == null || request.isExpired()) {
            player.sendMessage(plugin.getMessage("messages.no_pending_transfer"));
            pendingTransfers.remove(player.getUniqueId()); // Clean up expired/missing request just in case
            return;
        }

        // 3. Verify Target Player Still Exists (or at least played before)
        // Important check in case the target account was deleted or is invalid
        OfflinePlayer targetOfflinePlayer = Bukkit.getOfflinePlayer(request.targetPlayer);
        if (!targetOfflinePlayer.hasPlayedBefore() && !targetOfflinePlayer.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_not_found")); // Target might have gone offline permanently or never existed
            pendingTransfers.remove(player.getUniqueId()); // Clean up invalid request
            return;
        }

        // --- Perform Transfer Operations ---
        try {
            // 4. Update the database 'barrios' table to change the owner UUID
            // This is the most critical step.
            plugin.getBarrioManager().updateBarrioOwner(request.barrioId, request.targetPlayer);

            // 5. Update ranks in memory using RankManager
            // Force update the owner in memory to ensure immediate consistency
            // This will update both the new owner's rank to OWNER and remove the old owner's OWNER rank
            boolean memoryUpdateSuccess = rankManager.forceUpdateOwnerInMemory(
                request.barrioId,
                request.targetPlayer,
                player.getUniqueId()
            );

            if (!memoryUpdateSuccess) {
                plugin.getLogger().warning("Memory update for ownership transfer failed for barrio " +
                    request.barrioId + ". Owner may not be correctly reflected until server restart.");
            }

            // 6. Remove the pending request *after* successful database update
            pendingTransfers.remove(player.getUniqueId());

            // --- Messaging ---
            // 7. Notify the initiator
            String targetDisplayName = targetOfflinePlayer.getName() != null ? targetOfflinePlayer.getName() : request.targetPlayer.toString();
            player.sendMessage(plugin.getMessage("messages.transfer_confirmed", "%player%", targetDisplayName));

            // 8. Notify the target player if they are online
            Player targetOnline = targetOfflinePlayer.getPlayer();
            if (targetOnline != null) {
                targetOnline.sendMessage(plugin.getMessage("transfer_received", "%player%", player.getName()));
            }

        } catch (RuntimeException dbException) {
            // 9. Handle Database Failure during transfer
            plugin.getLogger().log(java.util.logging.Level.SEVERE, "Critical error during barrio ownership transfer DB update for barrio " + request.barrioId, dbException);
            player.sendMessage(plugin.getMessage("messages.transfer_failed_db")); // Add specific DB error message
            // Do NOT remove the pending request here, allow retry or admin intervention.
        }
    }
    private void handleGadgetCommand(Player player, String[] args) {
        // Keep the base command permission check (optional, but good practice)
        if (!player.hasPermission("barrio.cmd.gadget")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        String barrioId = BarrioGadgetsManager.getBarrioIdFromWorld(player.getWorld());

        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // --- CORRECTED PERMISSION CHECK ---
        // Instead of just checking if the player is TRUSTED,
        // check if the barrio's permissions allow gadget changing via the toggle.
        // The permissionManager.hasPermission call already includes the OWNER/TRUSTED bypass.
        if (!plugin.getPermissionManager().hasPermission(barrioId, player, "gadget_change_toggle")) {
            // You might want a specific message here, or reuse the general one.
            // Example specific message (add to messages.yml):
            // player.sendMessage(plugin.getMessage("error.gadget_access_denied"));
            // Using general message for now:
            player.sendMessage(plugin.getMessage("messages.no_permission")); // Or a more specific message
            return;
        }
        // --- END OF CORRECTION ---


        // If the permission check passes, open the GUI
        BarrioGadgetsGUI gui = new BarrioGadgetsGUI(plugin, player, barrioId);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }

    private void handleRentCommand(Player player, String[] args) {
        // Check permission
        if (!player.hasPermission("barrio.cmd.rent")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];

        // Check if player is the owner of the barrio
        String ownerUuid = plugin.getDatabase().executeQuery(
                "SELECT uuid FROM barrios WHERE id = ?",
                rs -> rs.next() ? rs.getString("uuid") : null,
                barrioId
        );

        if (ownerUuid == null || !ownerUuid.equals(player.getUniqueId().toString())) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // Check if the barrio has a renting mode
        String currentMode = plugin.getDatabase().executeQuery(
                "SELECT mode FROM barrio_modes WHERE barrio_id = ?",
                rs -> rs.next() ? rs.getString("mode") : null,
                barrioId
        );

        if (currentMode == null || (!currentMode.equals("RENTING") && !currentMode.equals("RENTING_OFF"))) {
            player.sendMessage(plugin.getMessage("messages.not_renting_mode"));
            return;
        }

        // Check command arguments
        if (args.length < 2) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String option = args[1].toLowerCase();
        if (option.equals("on") || option.equals("off")) {
            // Handle on/off toggle
            // Get current rent settings
            plugin.getDatabase().executeQuery(
                    "SELECT check_time, group_check, online_in_world, rent_price FROM barrio_modes WHERE barrio_id = ?",
                    rs -> {
                        if (rs.next()) {
                            String checkTime = rs.getString("check_time");
                            String groupCheck = rs.getString("group_check");
                            boolean onlineInWorld = rs.getBoolean("online_in_world");
                            double rentPrice = rs.getDouble("rent_price");

                            // Set the new mode
                            BarrioMode newMode = option.equals("on") ? BarrioMode.RENTING : BarrioMode.RENTING_OFF;
                            plugin.getModeManager().setBarrioMode(barrioId, newMode, checkTime, groupCheck, onlineInWorld, rentPrice);

                            // Send success message
                            if (option.equals("on")) {
                                player.sendMessage(plugin.getMessage("messages.rent_toggled_on"));
                            } else {
                                player.sendMessage(plugin.getMessage("messages.rent_toggled_off"));
                            }
                        }
                        return null;
                    },
                    barrioId
            );
        } else if (option.equals("extend")) {
            // Handle extend command
            handleRentExtendCommand(player, barrioId);
        } else {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
        }
    }

    private void handleRentExtendCommand(Player player, String barrioId) {
        // Get current rent settings
        plugin.getDatabase().executeQuery(
                "SELECT check_time, next_check, rent_price FROM barrio_modes WHERE barrio_id = ?",
                rs -> {
                    if (rs.next()) {
                        String checkTime = rs.getString("check_time");
                        long nextCheck = rs.getLong("next_check");
                        double rentPrice = rs.getDouble("rent_price");

                        // Check if player has enough money to pay rent
                        if (plugin.getModeManager().collectRent(player.getUniqueId().toString(), rentPrice)) {
                            // Calculate new next_check time by adding check_time to current next_check
                            long timeToAdd = plugin.getModeManager().parseTime(checkTime);
                            long newNextCheck = nextCheck + timeToAdd;

                            // Update next_check in database
                            plugin.getDatabase().executeUpdate(
                                    "UPDATE barrio_modes SET next_check = ? WHERE barrio_id = ?",
                                    newNextCheck, barrioId
                            );

                            // Calculate days difference for message
                            long currentTime = System.currentTimeMillis();
                            int daysBefore = (int) ((nextCheck - currentTime) / (1000 * 60 * 60 * 24));
                            int daysAfter = (int) ((newNextCheck - currentTime) / (1000 * 60 * 60 * 24));

                            // Send success message
                            player.sendMessage(plugin.getMessage("messages.rent_extended")
                                    .replace("%amount%", String.valueOf(rentPrice))
                                    .replace("%days%", String.valueOf(daysAfter)));
                        } else {
                            // Not enough money
                            player.sendMessage(plugin.getMessage("messages.insufficient_funds_extend")
                                    .replace("%amount%", String.valueOf(rentPrice)));
                        }
                    }
                    return null;
                },
                barrioId
        );
    }

    private void handleInfoCommand(Player player) {
        if (!player.hasPermission("barrio.cmd.info")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];

        // Get barrio info from database
        database.executeQuery(
                "SELECT * FROM barrios WHERE id = ?",
                rs -> {
                    if (rs.next()) {
                        String ownerUuidStr = rs.getString("uuid");
                        long creationTime = rs.getLong("creation_time");
                        Date creationDate = new Date(creationTime);

                        // Get the nickname from BarrioManager instead of database
                        // This ensures we get the most up-to-date nickname, even if it hasn't been saved to the database yet
                        String nickname = plugin.getBarrioManager().getBarrioNickname(barrioId);

                        // Get owner name from UUID
                        String ownerName = "Unknown";
                        if (ownerUuidStr != null && !ownerUuidStr.isEmpty()) {
                            try {
                                UUID ownerUuid = UUID.fromString(ownerUuidStr);
                                ownerName = Bukkit.getOfflinePlayer(ownerUuid).getName();
                                if (ownerName == null) {
                                    ownerName = "Unknown Player";
                                }
                            } catch (IllegalArgumentException e) {
                                plugin.getLogger().warning("Invalid UUID format for barrio " + barrioId + ": " + ownerUuidStr);
                            }
                        }

                        WorldBorder border = player.getWorld().getWorldBorder();
                        int borderSize = (int) border.getSize();

                        String mode = plugin.getModeManager().getBarrioMode(barrioId).name();

                        // Check if this is the default barrio for the player
                        String defaultBarrioId = plugin.getDefaultBarrioManager().getPlayerDefaultBarrio(player.getUniqueId());
                        boolean isDefault = barrioId.equals(defaultBarrioId);
                        String defaultMarker = isDefault ? plugin.getMessage("messages.default_barrio_marker") : "";

                        // Send title
                        String title = plugin.getMessage("messages.info_barrio_info_title")
                                .replace("%barrio_id%", barrioId)
                                .replace("%owner%", ownerName);
                        player.sendTitle(title, "", 10, 70, 20);

                        // Send chat message
                        String info = plugin.getMessage("messages.info_barrio_info_chat")
                                .replace("%creation_date%", new SimpleDateFormat("MM/dd/yyyy").format(creationDate))
                                .replace("%border_size%", String.valueOf(borderSize))
                                .replace("%mode%", mode);
                        player.sendMessage(info);

                        // Show default marker if this is the default barrio
                        if (isDefault) {
                            player.sendMessage(defaultMarker);
                        }

                        // Show nickname if available
                        if (nickname != null && !nickname.isEmpty()) {
                            player.sendMessage(plugin.getMessage("messages.info_barrio_nickname")
                                    .replace("%nickname%", nickname));
                        }
                    }
                    return null;
                },
                barrioId
        );
    }

    private void handleSetSpawnCommand(Player player) {
        if (!player.hasPermission("barrio.cmd.setspawn")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        if (!isPlayerRankTrustedOrHigher(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.no_permission_setspawn"));
            return;
        }

        Location loc = player.getLocation();
        player.getWorld().setSpawnLocation(loc.getBlockX(), loc.getBlockY(), loc.getBlockZ());
        player.sendMessage(plugin.getMessage("messages.spawn_set"));
    }

    /**
     * Handles the setdefault command.
     * Sets the current barrio as the player's default barrio.
     *
     * @param player The player executing the command
     */
    private void handleSetDefaultCommand(Player player) {
        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Extract the barrio ID from the world name
        String barrioId = worldName.split("/")[1];

        // Check if this barrio belongs to the player
        if (!isPlayerOwner(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.not_your_barrio"));
            return;
        }

        // Set this barrio as the default
        plugin.getDefaultBarrioManager().setPlayerDefaultBarrio(player.getUniqueId(), barrioId);
        player.sendMessage(plugin.getMessage("messages.default_barrio_set"));
    }


    private void openTypeSelectionGUI(Player player) {
        int maxBarrios = getMaxBarrios(player);
        int currentBarrios = getCurrentBarrios(player);
        if (currentBarrios >= maxBarrios) {
            player.sendMessage(plugin.getMessage("messages.max_barrios_reached").replace("%max%", String.valueOf(maxBarrios)));
            return;
        }

        BarrioTypeGUI gui = new BarrioTypeGUI(plugin, database, player);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }

    private void handleCreateCommand(Player player, String[] args) {
        if (args.length == 1) {
            openTypeSelectionGUI(player);
            return;
        }

        // Check cooldown
        int cooldownSeconds = plugin.getConfig().getInt("create.cooldown_seconds", 0);
        if (cooldownSeconds > 0) {
            UUID playerUuid = player.getUniqueId();
            long currentTime = System.currentTimeMillis();
            Long lastCreateTime = createCooldowns.get(playerUuid);

            if (lastCreateTime != null) {
                long elapsedTime = currentTime - lastCreateTime;
                long remainingTime = (cooldownSeconds * 1000) - elapsedTime;

                if (remainingTime > 0) {
                    // Player is still on cooldown
                    String timeStr = formatTimeRemaining(remainingTime);
                    player.sendMessage(plugin.getMessage("messages.create_cooldown").replace("%time%", timeStr));
                    return;
                }
            }
        }

        int maxBarrios = getMaxBarrios(player);
        int currentBarrios = getCurrentBarrios(player);
        if (currentBarrios >= maxBarrios) {
            player.sendMessage(plugin.getMessage("messages.max_barrios_reached").replace("%max%", String.valueOf(maxBarrios)));
            return;
        }

        if (args.length == 2) {
            if (args[1].equalsIgnoreCase("regular")) {
                BarrioCreateRegularGUI gui = new BarrioCreateRegularGUI(plugin, database, player);
                plugin.setOpenGui(player.getUniqueId(), gui);
                gui.open();
                return;
            } else if (args[1].equalsIgnoreCase("template")) {
                BarrioCreateTemplateGUI gui = new BarrioCreateTemplateGUI(plugin, database, player);
                plugin.setOpenGui(player.getUniqueId(), gui);
                gui.open();
                return;
            }
        }
        player.sendMessage(plugin.getMessage("messages.invalid_usage"));
    }

    private void handleExpandCommand(Player player, String[] args) {
        if (!player.hasPermission("barrio.cmd.expand")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }
        expandPlayerBarrio(player);
    }

    private void handleAdminExpand(Player player, String[] args) {
        if (!player.hasPermission("barrio.admin.expand")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }
        if (args.length != 3) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }
        adminExpandBarrio(player, args[2]);
    }

    private void handleMembersCommand(Player player, String[] args) {
        String worldName = player.getWorld().getName();
        String barrioId = BarrioRankManager.getBarrioIdFromWorld(player.getWorld()); // Use helper

        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Check if player has permission to manage members (e.g., TRUSTED or OWNER)
        Rank playerRankInBarrio = rankManager.getRank(barrioId, player.getUniqueId());
        if (!playerRankInBarrio.isAtLeast(Rank.TRUSTED)) { // Check rank level
            player.sendMessage(plugin.getMessage("messages.no_permission")); // Or a more specific message like "error.must_be_trusted_or_owner"
            return;
        }

        // If no arguments, open the members list GUI (only for OWNER)
        if (args.length == 1) {
            // Only owners can see the full members list
            if (playerRankInBarrio != Rank.OWNER) {
                player.sendMessage(plugin.getMessage("messages.not_owner"));
                return;
            }

            // Open the members list GUI
            BarrioMembersListGUI gui = new BarrioMembersListGUI(plugin, player, barrioId);
            plugin.setOpenGui(player.getUniqueId(), gui);
            gui.open();
            return;
        }


        // --- Handle opening manage GUI (args.length == 3) ---
        if (args.length == 3 && args[1].equalsIgnoreCase("manage")) { // Changed subcommand for clarity
            String targetName = args[2];
            // Use OfflinePlayer to handle players not currently online
            OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(targetName);

            // Check if player exists in the barrio's player data
            boolean playerExistsInBarrio = false;

            // First, check if player has ever played on the server
            if (targetPlayer.hasPlayedBefore() || targetPlayer.isOnline()) {
                playerExistsInBarrio = true;
            } else {
                // If not, check if they exist in the barrio's player data
                plugin.getPlayerDataManager().loadPlayerDataForBarrio(barrioId);
                Map<UUID, BarrioPlayerData> playerDataMap = plugin.getPlayerDataManager().getPlayerDataMap(barrioId);
                if (playerDataMap != null) {
                    // Try to find the player by name in the player data
                    for (UUID uuid : playerDataMap.keySet()) {
                        OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(uuid);
                        if (offlinePlayer.getName() != null && offlinePlayer.getName().equalsIgnoreCase(targetName)) {
                            targetPlayer = offlinePlayer;
                            playerExistsInBarrio = true;
                            break;
                        }
                    }
                }
            }

            if (!playerExistsInBarrio) {
                player.sendMessage(plugin.getMessage("messages.player_not_found"));
                return;
            }

            // Pass OfflinePlayer to the GUI
            BarrioMemberManageGUI gui = new BarrioMemberManageGUI(plugin, player, targetPlayer, barrioId);
            plugin.setOpenGui(player.getUniqueId(), gui);
            gui.open();
            return;
        }

        // --- Handle setting rank (args.length == 4) ---
        if (args.length == 4) {
            if (!args[1].equalsIgnoreCase("set")) {
                player.sendMessage(plugin.getMessage("messages.invalid_usage_members")); // Specific usage message
                return;
            }
        } else {
            player.sendMessage(plugin.getMessage("messages.invalid_usage_members")); // Specific usage message
            return;
        }

        String targetName = args[2];
        OfflinePlayer targetOfflinePlayer = Bukkit.getOfflinePlayer(targetName); // Use OfflinePlayer
        if (!targetOfflinePlayer.hasPlayedBefore() && !targetOfflinePlayer.isOnline()) {
            player.sendMessage(plugin.getMessage("messages.player_never_played", "%player%", targetName));
            return;
        }
        UUID targetUuid = targetOfflinePlayer.getUniqueId();


        // Prevent setting rank to yourself
        if (targetUuid.equals(player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.cannot_set_own_rank"));
            return;
        }

        String rankInput = args[3].toUpperCase();
        Optional<Rank> targetRankOpt = Rank.fromString(rankInput);

        if (!targetRankOpt.isPresent() || targetRankOpt.get() == Rank.OWNER) {
            player.sendMessage(plugin.getMessage("messages.invalid_rank_set", "%ranks%", "VISITOR, RESIDENT, TRUSTED"));
            return;
        }
        Rank targetRank = targetRankOpt.get(); // Rank is VISITOR, RESIDENT, or TRUSTED

        // Get player's and target's current ranks using the manager
        Rank playerCurrentRank = rankManager.getRank(barrioId, player.getUniqueId());
        Rank targetCurrentRank = rankManager.getRank(barrioId, targetUuid);

        // --- Permission checks based on rank hierarchy ---
        if (playerCurrentRank == Rank.OWNER) {
            // Owner can set any non-owner rank (VISITOR, RESIDENT, TRUSTED)
            // Prevent owner from demoting self (already handled by self-check)
            if (targetCurrentRank == Rank.OWNER) {
                player.sendMessage(plugin.getMessage("messages.cannot_modify_owner")); // Should technically not happen with self-check
                return;
            }
        } else if (playerCurrentRank == Rank.TRUSTED) {
            // Trusted can only set VISITOR or RESIDENT
            if (targetRank == Rank.TRUSTED) {
                player.sendMessage(plugin.getMessage("messages.trusted_cannot_set_trusted"));
                return;
            }
            // Trusted cannot modify other trusted players or the owner
            if (targetCurrentRank.isAtLeast(Rank.TRUSTED)) {
                player.sendMessage(plugin.getMessage("messages.cannot_modify_higher_rank"));
                return;
            }
        } else {
            // Residents/Visitors cannot set ranks
            player.sendMessage(plugin.getMessage("messages.no_permission_set_rank"));
            return;
        }

        // Set the rank using the manager
        boolean success = rankManager.setRank(barrioId, targetUuid, targetRank);

        if (success) {
            String targetDisplayName = targetOfflinePlayer.getName() != null ? targetOfflinePlayer.getName() : targetUuid.toString(); // Use name if available
            player.sendMessage(plugin.getMessage("messages.rank_set",
                    "%player%", targetDisplayName,
                    "%rank%", targetRank.name().toLowerCase()));

            // Notify target player if they are online
            Player onlineTarget = targetOfflinePlayer.getPlayer();
            if (onlineTarget != null) {
                onlineTarget.sendMessage(plugin.getMessage("messages.rank_received",
                        "%owner%", player.getName(), // Using correct placeholder from messages.yml
                        "%rank%", targetRank.name().toLowerCase()));
            }
        } else {
            // Generic error, specific reasons logged by rankManager.setRank
            player.sendMessage(plugin.getMessage("messages.command_failed"));
        }
    }

    // Helper method to get a player's rank in a barrio
    private void handleBanCommand(Player player, String[] args) {
        if (!player.hasPermission("barrio.cmd.ban")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length != 2) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];

        // Check if player is trusted or higher
        if (!isPlayerRankTrustedOrHigher(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // Check if player has moderation_access permission
        if (!plugin.getPermissionManager().hasPermission(barrioId, player, "moderation_access")) {
            player.sendMessage(plugin.getMessage("messages.no_moderation_access"));
            return;
        }

        UUID targetUUID = plugin.getServer().getOfflinePlayer(args[1]).getUniqueId();
        if (isPlayerRankTrustedOrHigher(barrioId, plugin.getServer().getOfflinePlayer(targetUUID).getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.cannot_ban_trusted"));
            return;
        }

        if (playerDataManager.isBanned(barrioId, targetUUID)) {
            player.sendMessage(plugin.getMessage("messages.player_already_banned"));
            return;
        }

        playerDataManager.banPlayer(barrioId, targetUUID);
        Player targetPlayer = plugin.getServer().getPlayer(targetUUID);
        if (targetPlayer != null && targetPlayer.getWorld().getName().startsWith("Barrios/" + barrioId)) {
            teleportToServerSpawn(targetPlayer);
        }

        player.sendMessage(plugin.getMessage("messages.player_banned").replace("%player%", args[1]));
    }

    private void handleUnbanCommand(Player player, String[] args) {
        if (!player.hasPermission("barrio.cmd.unban")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length != 2) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];

        // Check if player is trusted or higher
        if (!isPlayerRankTrustedOrHigher(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // Check if player has moderation_access permission
        if (!plugin.getPermissionManager().hasPermission(barrioId, player, "moderation_access")) {
            player.sendMessage(plugin.getMessage("messages.no_moderation_access"));
            return;
        }

        UUID targetUUID = plugin.getServer().getOfflinePlayer(args[1]).getUniqueId();
        if (!playerDataManager.isBanned(barrioId, targetUUID)) {
            player.sendMessage(plugin.getMessage("messages.player_not_banned"));
            return;
        }

        playerDataManager.unbanPlayer(barrioId, targetUUID);
        player.sendMessage(plugin.getMessage("messages.player_unbanned").replace("%player%", args[1]));
    }

    private void handleKickCommand(Player player, String[] args) {
        if (!player.hasPermission("barrio.cmd.kick")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        if (args.length != 2) {
            player.sendMessage(plugin.getMessage("messages.invalid_usage"));
            return;
        }

        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];

        // Check if player is trusted or higher
        if (!isPlayerRankTrustedOrHigher(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // Check if player has moderation_access permission
        if (!plugin.getPermissionManager().hasPermission(barrioId, player, "moderation_access")) {
            player.sendMessage(plugin.getMessage("messages.no_moderation_access"));
            return;
        }

        Player targetPlayer = plugin.getServer().getPlayer(args[1]);
        if (targetPlayer == null) {
            player.sendMessage(plugin.getMessage("messages.player_not_found"));
            return;
        }

        if (!targetPlayer.getWorld().getName().startsWith("Barrios/" + barrioId)) {
            player.sendMessage(plugin.getMessage("messages.player_not_found"));
            return;
        }

        // Check if the target player is trusted or higher (fixed the bug here)
        if (isPlayerRankTrustedOrHigher(barrioId, targetPlayer.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.cannot_kick_trusted"));
            return;
        }

        teleportToServerSpawn(targetPlayer);
        player.sendMessage(plugin.getMessage("messages.player_kicked").replace("%player%", targetPlayer.getName()));
    }

    private void handleHomeCommand(Player player, String[] args) {
        if (!player.hasPermission("barrio.cmd.home")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Check if the player has any barrios
        int playerBarrioCount = getCurrentBarrios(player);
        if (playerBarrioCount == 0) {
            player.sendMessage(plugin.getMessage("messages.no_own_barrio"));
            return;
        }

        // Get the index from args or use default
        String index = args.length >= 2 ? args[1] : "default";

        // Get the barrio ID
        String barrioId = getBarrioIdByIndex(player.getUniqueId().toString(), index);
        if (barrioId == null) {
            player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
            return;
        }

        // Teleport to the barrio (no ownership check, just like tp command)
        teleportToBarrio(player, barrioId);
        player.sendMessage(plugin.getMessage("messages.teleported_home"));
    }

    private void handleSpawnCommand(Player player) {
        if (!player.hasPermission("barrio.cmd.spawn")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Old behavior of /barrio home - teleport to current barrio's spawn
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        Location spawn = player.getWorld().getSpawnLocation();
        player.teleport(spawn);
        player.sendMessage(plugin.getMessage("messages.teleported_home"));
    }


    /**
     * Check if a player is banned from a barrio.
     * This method is kept for backward compatibility but uses the new player data system.
     *
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player as a string
     * @return true if the player is banned, false otherwise
     */
    private boolean isBanned(String barrioId, String playerUuid) {
        try {
            UUID uuid = UUID.fromString(playerUuid);
            return playerDataManager.isBanned(barrioId, uuid);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid UUID format: " + playerUuid);
            return false;
        }
    }

    /**
     * Static method to check if a player is banned from a barrio.
     * This method is kept for backward compatibility but uses the new player data system.
     *
     * @param database The database instance (not used anymore)
     * @param barrioId The ID of the barrio
     * @param playerUuid The UUID of the player as a string
     * @return true if the player is banned, false otherwise
     */
    public static boolean isPlayerBannedFromBarrio(Database database, String barrioId, String playerUuid) {
        try {
            UUID uuid = UUID.fromString(playerUuid);
            BarrioCore plugin = (BarrioCore) Bukkit.getPluginManager().getPlugin("BarrioCore");
            if (plugin == null) {
                return false;
            }
            return plugin.getPlayerDataManager().isBanned(barrioId, uuid);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    private void teleportToServerSpawn(Player player) {
        World spawnWorld = Bukkit.getWorld(plugin.getConfig().getString("server_spawn.world", "world"));
        double x = plugin.getConfig().getDouble("server_spawn.x", 0);
        double y = plugin.getConfig().getDouble("server_spawn.y", 100);
        double z = plugin.getConfig().getDouble("server_spawn.z", 0);
        Location serverSpawn = new Location(spawnWorld, x, y, z);
        player.teleport(serverSpawn);
    }

    private void handleSettingsCommand(Player player) {
        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        if (barrioId == null || barrioId.isEmpty()) {
            plugin.getLogger().warning("Extracted barrioId is null or empty from world: " + worldName);
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Check if player has admin bypass permission
        boolean hasAdminBypass = player.hasPermission("barrio.admin.bypass");

        if (!hasAdminBypass) {
            // Check if player is owner or trusted with settings_access permission
            UUID playerUuid = player.getUniqueId();
            UUID ownerUuid = getOwnerUUIDFromDB(barrioId);
            boolean isOwner = playerUuid.equals(ownerUuid);
            boolean hasTrustedAccess = false;

            if (!isOwner) {
                // Check if player is trusted and has settings_access permission
                BarrioPlayerData playerData = plugin.getPlayerDataManager().getPlayerData(barrioId, playerUuid);
                if (playerData != null && playerData.getRank() == Rank.TRUSTED) {
                    // Check if trusted player has settings_access permission
                    hasTrustedAccess = plugin.getPermissionManager().hasPermission(barrioId, player, "settings_access");
                }

                if (!hasTrustedAccess) {
                    player.sendMessage(plugin.getMessage("messages.no_permission_settings"));
                    return;
                }
            }
        } else {
            plugin.debug("Player " + player.getName() + " accessed settings for barrio " + barrioId + " with admin bypass");
        }

        // Open the world settings GUI
        BarrioWorldSettingsGUI gui = new BarrioWorldSettingsGUI(plugin, player, barrioId);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }

    private void handleUpgradesCommand(Player player) {
        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        if (barrioId == null || barrioId.isEmpty()) {
            plugin.getLogger().warning("Extracted barrioId is null or empty from world: " + worldName);
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Check if player has admin bypass permission
        boolean hasAdminBypass = player.hasPermission("barrio.admin.bypass");

        if (!hasAdminBypass) {
            // Check if player is owner or trusted with upgrades_access permission
            UUID playerUuid = player.getUniqueId();
            UUID ownerUuid = getOwnerUUIDFromDB(barrioId);
            boolean isOwner = playerUuid.equals(ownerUuid);
            boolean hasTrustedAccess = false;

            if (!isOwner) {
                // Check if player is trusted and has upgrades_access permission
                BarrioPlayerData playerData = plugin.getPlayerDataManager().getPlayerData(barrioId, playerUuid);
                if (playerData != null && playerData.getRank() == Rank.TRUSTED) {
                    // Check if trusted player has upgrades_access permission
                    hasTrustedAccess = plugin.getPermissionManager().hasPermission(barrioId, player, "upgrades_access");
                }

                if (!hasTrustedAccess) {
                    player.sendMessage(plugin.getMessage("messages.no_permission_upgrades"));
                    return;
                }
            }
        } else {
            plugin.debug("Player " + player.getName() + " accessed upgrades for barrio " + barrioId + " with admin bypass");
        }

        // Open the upgrades GUI
        BarrioUpgradesGUI gui = new BarrioUpgradesGUI(plugin, player, barrioId);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }

    private void handleDeleteCommand(Player player) {
        // Check permission
        if (!player.hasPermission("barrio.cmd.delete")) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Check if player is in a barrio
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        if (barrioId == null || barrioId.isEmpty()) {
            plugin.getLogger().warning("Extracted barrioId is null or empty from world: " + worldName);
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Check if player is the owner of the barrio
        UUID playerUuid = player.getUniqueId();
        UUID ownerUuid = getOwnerUUIDFromDB(barrioId);
        if (!playerUuid.equals(ownerUuid)) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // Check for confirmation
        Long lastDeleteRequest = pendingDeleteConfirmations.get(playerUuid);
        long currentTime = System.currentTimeMillis();

        if (lastDeleteRequest != null && currentTime - lastDeleteRequest < 30000) { // 30 seconds confirmation window
            // Confirmed, delete the barrio
            pendingDeleteConfirmations.remove(playerUuid);
            deleteBarrio(barrioId);
            player.sendMessage(plugin.getMessage("messages.barrio_deleted"));
        } else {
            // First request, ask for confirmation
            pendingDeleteConfirmations.put(playerUuid, currentTime);
            player.sendMessage(plugin.getMessage("messages.barrio_delete_confirm"));
        }
    }

    /**
     * Handles the rate command to rate a barrio.
     *
     * @param player The player executing the command
     * @param args The command arguments
     */
    private void handleRateCommand(Player player, String[] args) {
        try {
            if (!player.hasPermission("barrio.cmd.rate")) {
                player.sendMessage(plugin.getMessage("messages.no_permission"));
                return;
            }

            String barrioId;

            // If no arguments, try to rate the current barrio
            if (args.length < 2) {
                String worldName = player.getWorld().getName();
                if (!worldName.startsWith("Barrios/")) {
                    player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
                    return;
                }

                barrioId = worldName.split("/")[1];
                player.sendMessage(plugin.getMessage("messages.rating.current_barrio"));
            } else {
                // Get player name from args
                String playerName = args[1];
                OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);

                // Check if the player exists
                if (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline()) {
                    player.sendMessage(plugin.getMessage("messages.player_not_found"));
                    return;
                }

                // Get the index from args or use default
                String index = args.length >= 3 ? args[2] : "default";

                // Get the barrio ID
                barrioId = getBarrioIdByIndex(targetPlayer.getUniqueId().toString(), index);
                if (barrioId == null) {
                    player.sendMessage(plugin.getMessage("messages.barrio_not_found"));
                    return;
                }
            }

            // Check if the barrio exists
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                player.sendMessage(plugin.getMessage("messages.rating.barrio_not_found"));
                return;
            }

            // Check if player is trying to rate their own barrio
            if (barrioData.getOwnerUuid() != null && barrioData.getOwnerUuid().equals(player.getUniqueId())) {
                player.sendMessage(plugin.getMessage("messages.rating.cannot_rate_own"));
                return;
            }

            // Check if player is on cooldown
            if (plugin.getRatingManager().isOnCooldown(barrioId, player.getUniqueId())) {
                long remainingTime = plugin.getRatingManager().getRemainingCooldown(barrioId, player.getUniqueId());
                String timeStr = formatTimeRemaining(remainingTime);
                player.sendMessage(plugin.getMessage("messages.rating.cooldown")
                        .replace("%time%", timeStr));
                return;
            }

            // If there are more arguments and we're rating the current barrio, try to parse a direct rating
            if (args.length >= 2 && args[1].matches("[1-5]")) {
                try {
                    int stars = Integer.parseInt(args[1]);
                    int minRating = plugin.getConfig().getInt("rating.min_rating", 1);
                    int maxRating = plugin.getConfig().getInt("rating.max_rating", 5);

                    if (stars < minRating || stars > maxRating) {
                        player.sendMessage(plugin.getMessage("messages.rating.invalid_rating")
                                .replace("%min%", String.valueOf(minRating))
                                .replace("%max%", String.valueOf(maxRating)));
                        return;
                    }

                    // Get the message if provided
                    StringBuilder message = new StringBuilder();
                    if (args.length > 2) {
                        for (int i = 2; i < args.length; i++) {
                            message.append(args[i]).append(" ");
                        }
                    }

                    // Submit the rating
                    boolean success = plugin.getRatingManager().saveRating(
                            barrioId, player.getUniqueId(), stars, message.toString().trim());

                    if (success) {
                        String nickname = barrioData.getNickname() != null ?
                                barrioData.getNickname() : barrioId;
                        player.sendMessage(plugin.getMessage("messages.rating.success")
                                .replace("%barrio_name%", nickname)
                                .replace("%stars%", String.valueOf(stars)));
                    } else {
                        player.sendMessage(plugin.getMessage("messages.rating.error"));
                    }
                    return;
                } catch (NumberFormatException e) {
                    player.sendMessage(plugin.getMessage("messages.rating.invalid_rating_format"));
                    return;
                }
            }

            // Open the rating GUI
            BarrioRatingGUI gui = new BarrioRatingGUI(plugin, player, barrioId);
            plugin.setOpenGui(player.getUniqueId(), gui);
            gui.open();
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error handling rate command", e);
            player.sendMessage(plugin.getMessage("messages.rating.error"));
        }
    }

    /**
     * Handles the ratings command to view barrio ratings.
     *
     * @param player The player executing the command
     * @param args The command arguments
     */
    private void handleRatingsCommand(Player player, String[] args) {
        try {
            if (!player.hasPermission("barrio.cmd.ratings")) {
                player.sendMessage(plugin.getMessage("messages.no_permission"));
                return;
            }

            String barrioId;

            // If no barrio ID is provided, use the current barrio
            if (args.length < 2) {
                String worldName = player.getWorld().getName();
                if (!worldName.startsWith("Barrios/")) {
                    player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
                    return;
                }

                barrioId = worldName.split("/")[1];
            } else {
                barrioId = args[1];
            }

            plugin.getLogger().info("Checking if barrio exists: " + barrioId);

            // Check if the barrio exists
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                plugin.getLogger().warning("Barrio not found: " + barrioId);
                player.sendMessage(plugin.getMessage("messages.rating.barrio_not_found"));
                return;
            }

            plugin.getLogger().info("Creating BarrioRatingInfoGUI for barrio: " + barrioId);

            // Open the ratings info GUI
            BarrioRatingInfoGUI gui = new BarrioRatingInfoGUI(plugin, player, barrioId);
            plugin.getLogger().info("Setting open GUI for player: " + player.getName());
            plugin.setOpenGui(player.getUniqueId(), gui);
            plugin.getLogger().info("Opening GUI for player: " + player.getName());
            gui.open();
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error handling ratings command", e);
            e.printStackTrace();
            player.sendMessage(plugin.getMessage("messages.rating.error"));
        }
    }

    /**
     * Formats a time in milliseconds to a human-readable string.
     *
     * @param timeMs The time in milliseconds
     * @return A formatted string (e.g., "2h 30m")
     */
    private String formatTimeRemaining(long timeMs) {
        long seconds = timeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return days + "d " + (hours % 24) + "h";
        } else if (hours > 0) {
            return hours + "h " + (minutes % 60) + "m";
        } else if (minutes > 0) {
            return minutes + "m";
        } else {
            return seconds + "s";
        }
    }

    /**
     * Sets the create cooldown for a player
     *
     * @param player The player to set the cooldown for
     */
    public void setCreateCooldown(Player player) {
        int cooldownSeconds = plugin.getConfig().getInt("create.cooldown_seconds", 0);
        if (cooldownSeconds > 0) {
            createCooldowns.put(player.getUniqueId(), System.currentTimeMillis());
        }
    }

    private void handlePermissionsCommand(Player player, String[] args) {
        String worldName = player.getWorld().getName();
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        String barrioId = worldName.split("/")[1];
        if (barrioId == null || barrioId.isEmpty()) {
            // --- CHANGE START ---
            plugin.getLogger().warning("Extracted barrioId is null or empty from world: " + worldName);
            // --- CHANGE END ---
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // First check if permission management is enabled for everyone
        // Note: This call itself might trigger the error if barrioId is already null here,
        // but the logs suggest it happens later in the GUI loading.
        boolean permissionManagementEnabled = plugin.getPermissionManager().hasPermission(barrioId, player, "permission_toggle");

        // If permission management is not enabled, then check if player is trusted/owner
        if (!permissionManagementEnabled && !isPlayerRankTrustedOrHigher(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.not_owner"));
            return;
        }

        // Check if a specific permission type was requested
        if (args.length > 1) {
            String permType = args[1].toLowerCase();

            if (permType.equals("residents") || permType.equals("resident")) {
                // Open resident permissions GUI
                plugin.debug("Creating BarrioResidentPermissionsEditGUI for player " + player.getName() + " with barrioId: '" + barrioId + "'");
                BarrioResidentPermissionsEditGUI gui = new BarrioResidentPermissionsEditGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), gui);
                gui.open();
                return;
            } else if (permType.equals("visitors") || permType.equals("visitor")) {
                // Open visitor permissions GUI
                plugin.debug("Creating BarrioPermissionsGUI for player " + player.getName() + " with barrioId: '" + barrioId + "'");
                BarrioPermissionsGUI gui = new BarrioPermissionsGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), gui);
                gui.open();
                return;
            } else if (permType.equals("trusted")) {
                // Open trusted permissions GUI
                plugin.debug("Creating BarrioTrustedPermissionsEditGUI for player " + player.getName() + " with barrioId: '" + barrioId + "'");
                BarrioTrustedPermissionsEditGUI gui = new BarrioTrustedPermissionsEditGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), gui);
                gui.open();
                return;
            } else {
                player.sendMessage(plugin.getMessage("messages.invalid_permission_type"));
                return;
            }
        }

        // Default to visitor permissions if no type specified
        plugin.debug("Creating BarrioPermissionsGUI for player " + player.getName() + " with barrioId: '" + barrioId + "'");
        BarrioPermissionsGUI gui = new BarrioPermissionsGUI(plugin, player, barrioId);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }


    private String getBarrioIdByIndex(String uuid, String indexStr) {
        // If index is not specified or is "default", use the default barrio
        if (indexStr == null || indexStr.equalsIgnoreCase("default")) {
            try {
                UUID playerUuid = UUID.fromString(uuid);
                String defaultBarrioId = plugin.getDefaultBarrioManager().getPlayerDefaultBarrio(playerUuid);
                if (defaultBarrioId != null) {
                    return defaultBarrioId;
                }

                // If no default barrio is set, try to get the first barrio
                return plugin.getDatabase().executeQuery(
                        "SELECT id FROM barrios WHERE uuid = ? ORDER BY creation_time ASC LIMIT 1",
                        rs -> rs.next() ? rs.getString("id") : null,
                        uuid
                );
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid UUID format: " + uuid);
            }
        } else {
            try {
                // Use the specified index
                int index = Integer.parseInt(indexStr) - 1;
                if (index < 0) index = 0; // Ensure index is not negative

                return plugin.getDatabase().executeQuery(
                        "SELECT id FROM barrios WHERE uuid = ? ORDER BY creation_time ASC LIMIT 1 OFFSET ?",
                        rs -> rs.next() ? rs.getString("id") : null,
                        uuid,
                        index
                );
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("Invalid index format: " + indexStr);
                // If index is not a number, try to get the first barrio
                return plugin.getDatabase().executeQuery(
                        "SELECT id FROM barrios WHERE uuid = ? ORDER BY creation_time ASC LIMIT 1",
                        rs -> rs.next() ? rs.getString("id") : null,
                        uuid
                );
            }
        }

        return null;
    }

    private int getMaxBarrios(Player player) {
        // Check for wildcard permission first
        if (player.hasPermission("barrio.create.*")) {
            return Integer.MAX_VALUE; // Unlimited barrios
        }

        // Get all permissions the player has
        int maxFound = 0;
        for (String permission : player.getEffectivePermissions().stream()
                .map(perm -> perm.getPermission())
                .filter(perm -> perm.startsWith("barrio.create."))
                .collect(Collectors.toList())) {
            try {
                // Extract the number part after "barrio.create."
                String numberPart = permission.substring("barrio.create.".length());
                int value = Integer.parseInt(numberPart);
                if (value > maxFound) {
                    maxFound = value;
                }
            } catch (NumberFormatException ignored) {
                // Skip permissions that don't have a valid number
            }
        }
        return maxFound;
    }

    private int getCurrentBarrios(Player player) {
        return plugin.getDatabase().executeQuery(
                "SELECT COUNT(*) as count FROM barrios WHERE uuid = ?",
                rs -> rs.next() ? rs.getInt("count") : 0,
                player.getUniqueId().toString()
        );
    }


    private void expandPlayerBarrio(Player player) {
        World world = player.getWorld();
        String worldName = world.getName();

        // Check if the world is a barrio world
        if (!worldName.startsWith("Barrios/")) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }

        // Extract the barrio ID from the world name
        String[] parts = worldName.split("/");
        if (parts.length < 2) {
            player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
            return;
        }
        String barrioId = parts[1];

        // Check if this barrio belongs to the player
        if (!isPlayerOwner(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.not_your_barrio"));
            return;
        }

        WorldBorder border = world.getWorldBorder();
        int currentSize = (int) border.getSize();
        int increment = plugin.getConfig().getInt("border.increment", 25);
        int maxSize = plugin.getConfig().getInt("border.max", 400);

        int newSize = currentSize + increment;
        if (newSize > maxSize) {
            player.sendMessage(plugin.getMessage("messages.max_border_reached"));
            return;
        }

        border.setSize(newSize);
        player.sendMessage(plugin.getMessage("messages.border_expanded").replace("%size%", String.valueOf(newSize)));
    }

    private void adminExpandBarrio(Player player, String sizeStr) {
        try {
            int size = Integer.parseInt(sizeStr);
            World world = player.getWorld();
            String worldName = world.getName();
            if (!worldName.startsWith("Barrios/")) {
                player.sendMessage(plugin.getMessage("messages.not_in_barrio"));
                return;
            }

            WorldBorder border = world.getWorldBorder();
            border.setSize(size);
            player.sendMessage(plugin.getMessage("messages.border_set").replace("%size%", String.valueOf(size)));
        } catch (NumberFormatException e) {
            player.sendMessage(plugin.getMessage("messages.invalid_number"));
        }
    }


    private void transferBarrio(String barrioId, String newOwnerUuid) {
        try {
            UUID ownerUuid = UUID.fromString(newOwnerUuid);
            plugin.getBarrioManager().updateBarrioOwner(barrioId, ownerUuid);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().severe("Invalid UUID format when transferring barrio: " + newOwnerUuid);
        }
    }

    public void deleteBarrio(String barrioId) {
        // Get server spawn location
        World spawnWorld = Bukkit.getWorld(plugin.getConfig().getString("server_spawn.world", "world"));
        double x = plugin.getConfig().getDouble("server_spawn.x", 0);
        double y = plugin.getConfig().getDouble("server_spawn.y", 100);
        double z = plugin.getConfig().getDouble("server_spawn.z", 0);
        Location serverSpawn = new Location(spawnWorld, x, y, z);

        File barrioDir = new File(Bukkit.getWorldContainer(), "Barrios/" + barrioId);
        MultiverseCore mvc = (MultiverseCore) Bukkit.getPluginManager().getPlugin("Multiverse-Core");

        if (barrioDir.exists()) {
            File[] worlds = barrioDir.listFiles();
            if (worlds != null) {
                // Check if it's a regular barrio (has multiple worlds) or template (single world)
                boolean isRegular = Arrays.stream(worlds)
                        .anyMatch(file -> file.getName().equals("world_nether") || file.getName().equals("world_the_end"));

                if (isRegular) {
                    // Handle regular barrio (3 worlds)
                    String[] worldTypes = {"world", "world_nether", "world_the_end"};
                    for (String type : worldTypes) {
                        String worldName = "Barrios/" + barrioId + "/" + type;
                        World world = Bukkit.getWorld(worldName);
                        if (world != null) {
                            world.getPlayers().forEach(player -> player.teleport(serverSpawn));
                            if (mvc != null) {
                                mvc.getMVWorldManager().deleteWorld(worldName, true, true);
                            }
                        }
                    }
                } else {
                    // Handle template barrio (single world)
                    for (File worldFile : worlds) {
                        String worldName = "Barrios/" + barrioId + "/" + worldFile.getName();
                        World world = Bukkit.getWorld(worldName);
                        if (world != null) {
                            world.getPlayers().forEach(player -> player.teleport(serverSpawn));
                            if (mvc != null) {
                                mvc.getMVWorldManager().deleteWorld(worldName, true, true);
                            }
                        }
                    }
                }

                // Delete the barrio directory after all worlds have been deleted
                // This is needed because Multiverse-Core only deletes the world directories, not the parent barrio directory
                if (barrioDir.exists()) {
                    // Check if the directory is empty
                    File[] remainingFiles = barrioDir.listFiles();
                    if (remainingFiles == null || remainingFiles.length == 0) {
                        // Directory is empty, safe to delete
                        if (barrioDir.delete()) {
                            plugin.getLogger().info("Deleted barrio directory: " + barrioDir.getPath());
                        } else {
                            plugin.getLogger().warning("Failed to delete barrio directory: " + barrioDir.getPath());
                        }
                    } else {
                        // Directory still has files, log a warning
                        plugin.getLogger().warning("Barrio directory not empty after world deletion, cannot delete: " + barrioDir.getPath());
                        plugin.getLogger().warning("Remaining files: " + remainingFiles.length);
                        for (File file : remainingFiles) {
                            plugin.getLogger().warning(" - " + file.getName());
                        }
                    }
                }
            }
        }

        // Delete from database using BarrioManager
        plugin.getBarrioManager().deleteBarrio(barrioId);

        // Handle default barrio deletion
        plugin.getDefaultBarrioManager().handleBarrioDeletion(barrioId);
    }

    private void transferBarrioOwnershipDB(String barrioId, String newOwnerUuid) {
        try {
            UUID ownerUuid = UUID.fromString(newOwnerUuid);
            plugin.getBarrioManager().updateBarrioOwner(barrioId, ownerUuid);
            plugin.getLogger().info("Transferred ownership of barrio " + barrioId + " to " + newOwnerUuid + " in database.");
        } catch (RuntimeException e) {
            // Log the error and re-throw it so the calling method (handleTransferConfirm) knows it failed.
            plugin.getLogger().log(java.util.logging.Level.SEVERE, "Failed to transfer ownership in database for barrio " + barrioId + " to " + newOwnerUuid, e);
            throw e;
        }
    }

    private UUID getOwnerUUIDFromDB(String barrioId) {
        // Use BarrioManager to get the owner UUID
        return plugin.getBarrioManager().getBarrioOwner(barrioId);
    }

    private void teleportToBarrio(Player player, String barrioId) {
        // Check if this is a template-based barrio
        String templateName = plugin.getBarrioManager().getBarrioTemplateName(barrioId);

        World world;
        if (templateName != null) {
            // Template-based barrio - use the template name for the world path
            world = Bukkit.getWorld("Barrios/" + barrioId + "/" + templateName);
            plugin.getLogger().info("Attempting to teleport to template-based barrio: Barrios/" + barrioId + "/" + templateName);
        } else {
            // Regular barrio - use the standard world path
            world = Bukkit.getWorld("Barrios/" + barrioId + "/world");
            plugin.getLogger().info("Attempting to teleport to regular barrio: Barrios/" + barrioId + "/world");
        }

        if (world == null) {
            player.sendMessage(plugin.getMessage("messages.world_not_found"));
            plugin.getLogger().warning("World not found for barrio: " + barrioId);
            return;
        }

        // Get the world settings to check the Use Spawn Toggle setting
        me.zivush.barriocore.worldsettings.BarrioWorldSettings settings = plugin.getWorldSettingsManager().getWorldSettings(barrioId);

        // If settings are null or Use Spawn Toggle is enabled, teleport to spawn
        if (settings == null || settings.isUseBarrioSpawn()) {
            Location spawn = world.getSpawnLocation();
            player.teleport(spawn);
            return;
        }

        // If Use Spawn Toggle is disabled, teleport to last known location
        BarrioPlayerData playerData = plugin.getPlayerDataManager().getPlayerData(barrioId, player.getUniqueId());
        if (playerData != null) {
            Location lastLocation = playerData.getLastLocation();
            if (lastLocation != null) {
                // Teleport to last known location
                player.teleport(lastLocation);
                return;
            }
        }

        // Fallback to spawn if no last location is available
        Location spawn = world.getSpawnLocation();
        player.teleport(spawn);
    }
    private boolean isPlayerOwner(String barrioId, UUID playerUuid) {
        // Basic validation
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("isPlayerOwner called with null barrioId or playerUuid.");
            return false;
        }
        // Ensure rankManager is available
        if (rankManager == null) {
            plugin.getLogger().severe("isPlayerOwner: RankManager is null!");
            return false; // Fail safe
        }

        // Get rank from the manager and check if it's OWNER
        try {
            Rank rank = rankManager.getRank(barrioId, playerUuid);
            return rank == Rank.OWNER;
        } catch (Exception e) {
            // Log potential errors from rankManager.getRank if any can occur
            plugin.getLogger().log(java.util.logging.Level.WARNING, "Error getting rank in isPlayerOwner for " + playerUuid + " in barrio " + barrioId, e);
            return false; // Fail safe on error
        }
    }
    private boolean isPlayerRankTrustedOrHigher(String barrioId, UUID playerUuid) {
        // Basic validation
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("isPlayerRankTrustedOrHigher called with null barrioId or playerUuid.");
            return false;
        }
        // Ensure rankManager is available
        if (rankManager == null) {
            plugin.getLogger().severe("isPlayerRankTrustedOrHigher: RankManager is null!");
            return false; // Fail safe
        }

        // Get rank from the manager and check its level
        try {
            Rank rank = rankManager.getRank(barrioId, playerUuid);
            // Use the helper method within the Rank enum
            return rank.isAtLeast(Rank.TRUSTED);
        } catch (Exception e) {
            // Log potential errors from rankManager.getRank if any can occur
            plugin.getLogger().log(java.util.logging.Level.WARNING, "Error getting rank in isPlayerRankTrustedOrHigher for " + playerUuid + " in barrio " + barrioId, e);
            return false; // Fail safe on error
        }
    }

    /**
     * Opens the main barrio GUI for a player.
     *
     * @param player The player to open the GUI for
     */
    private void openMainGUI(Player player) {
        BarrioMainGUI gui = new BarrioMainGUI(plugin, player);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }
}