package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.BarrioManager;
import me.zivush.barriocore.ratings.BarrioRatingManager;
import me.zivush.barriocore.util.HeadUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * GUI for viewing barrio ratings.
 */
public class BarrioRatingInfoGUI extends BaseGUI {
    private final String barrioId;
    private BarrioRatingManager ratingManager;
    private int currentPage = 0;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    private final List<Integer> reviewSlots = new ArrayList<>();

    /**
     * Constructor for the BarrioRatingInfoGUI.
     *
     * @param plugin The BarrioCore plugin instance
     * @param player The player viewing the GUI
     * @param barrioId The ID of the barrio to view
     */
    public BarrioRatingInfoGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_rating_info_gui");
        this.barrioId = barrioId;

        try {
            this.ratingManager = plugin.getRatingManager();

            if (this.ratingManager == null) {
                plugin.getLogger().severe("Rating manager is null in BarrioRatingInfoGUI constructor");
                player.sendMessage(plugin.getMessage("messages.rating.error"));
                player.closeInventory();
                return;
            }

            // Check if the barrio exists before setting up items
            if (plugin.getBarrioManager().getBarrioData(barrioId) == null) {
                plugin.getLogger().warning("Barrio not found in BarrioRatingInfoGUI constructor: " + barrioId);
                player.sendMessage(plugin.getMessage("messages.rating.barrio_not_found"));
                player.closeInventory();
                return;
            }


            // Load review slots from config
            ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
            if (section != null) {
                ConfigurationSection reviewSection = section.getConfigurationSection("review");
                if (reviewSection != null) {
                    reviewSlots.addAll(reviewSection.getIntegerList("slots"));
                } else {
                    plugin.getLogger().warning("Review section is null in BarrioRatingInfoGUI constructor");
                }
            } else {
                plugin.getLogger().warning("GUI config section is null in BarrioRatingInfoGUI constructor");
            }


            setupRatingInfoItems();
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI constructor", e);
            e.printStackTrace();
            player.sendMessage(plugin.getMessage("messages.rating.error"));
            player.closeInventory();
        }
    }

    @Override
    protected void setupItems() {
        // This method is called by BaseGUI constructor, but we need ratingManager to be initialized first
        // So we do nothing here and call setupRatingInfoItems() explicitly after ratingManager is initialized
    }

    /**
     * Sets up the items in the GUI after ratingManager is initialized.
     */
    protected void setupRatingInfoItems() {
        try {

            ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
            if (section == null) {
                plugin.getLogger().warning("GUI config section is null in BarrioRatingInfoGUI.setupItems");
                return;
            }

            // Get barrio info
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData == null) {
                plugin.getLogger().warning("Barrio data is null in BarrioRatingInfoGUI.setupItems: " + barrioId);
                return;
            }

            plugin.getLogger().info("Getting barrio info in BarrioRatingInfoGUI.setupItems");
            String nickname = barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;
            UUID ownerUuid = barrioData.getOwnerUuid();
            OfflinePlayer owner = ownerUuid != null ? Bukkit.getOfflinePlayer(ownerUuid) : null;
            String ownerName = owner != null && owner.getName() != null ? owner.getName() : "Unknown";

            plugin.getLogger().info("Barrio info: nickname=" + nickname + ", owner=" + ownerName);

            // Update title with barrio name
            String title = section.getString("title", "&6Ratings for %barrio_name%");
            title = title.replace("%barrio_name%", nickname)
                    .replace("%owner_name%", ownerName);
            title = ChatColor.translateAlternateColorCodes('&', title);

            // Create a new inventory with the updated title
            inventory = Bukkit.createInventory(player, inventory.getSize(), title);

            // Re-setup decoration
            setupDecoration();

            // Set up back button
            ConfigurationSection backButtonSection = section.getConfigurationSection("back_button");
            if (backButtonSection != null) {
                ItemStack backButton = createItem(backButtonSection);
                List<Integer> backSlots = backButtonSection.getIntegerList("slots");
                for (int slot : backSlots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, backButton);
                    }
                }
            }

            // Set barrio head
            ConfigurationSection headSection = section.getConfigurationSection("barrio_head");
            if (headSection != null) {
                ItemStack head = createBarrioHead(barrioData, headSection);
                for (int slot : headSection.getIntegerList("slots")) {
                    inventory.setItem(slot, head);
                }
            }

            // Set reviews
            setupReviews(section);

            // Set navigation buttons
            setupNavigationButtons(section);

            // Set rate button
            ConfigurationSection rateSection = section.getConfigurationSection("rate_button");
            if (rateSection != null) {
                ItemStack rateButton = createRateButton(barrioData, rateSection);
                for (int slot : rateSection.getIntegerList("slots")) {
                    inventory.setItem(slot, rateButton);
                }
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI.setupItems", e);
            e.printStackTrace();
        }
    }

    /**
     * Creates a player head item for a barrio.
     *
     * @param barrioData The barrio data
     * @param section The configuration section for the head item
     * @return The created ItemStack
     */
    private ItemStack createBarrioHead(BarrioManager.BarrioData barrioData, ConfigurationSection section) {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return head;

        // Get barrio info for placeholders
        UUID ownerUuid = barrioData.getOwnerUuid();
        OfflinePlayer owner = ownerUuid != null ? Bukkit.getOfflinePlayer(ownerUuid) : null;
        String nickname = barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;
        String ownerName = owner != null && owner.getName() != null ? owner.getName() : "Unknown";

        // Get rating info
        double average = ratingManager.getAverageRating(barrioId);
        int count = ratingManager.getRatingCount(barrioId);
        int highest = ratingManager.getHighestRating(barrioId);
        int lowest = ratingManager.getLowestRating(barrioId);

        // Set display name
        String name = section.getString("name", "&6%barrio_name%'s Rating Info");
        name = name.replace("%barrio_name%", nickname)
                .replace("%owner_name%", ownerName);
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            line = line.replace("%barrio_name%", nickname)
                    .replace("%owner_name%", ownerName)
                    .replace("%average%", String.format("%.1f", average))
                    .replace("%count%", String.valueOf(count))
                    .replace("%highest%", String.valueOf(highest))
                    .replace("%lowest%", String.valueOf(lowest));
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        head.setItemMeta(meta);

        // Use our utility class to properly set the head texture for offline players
        if (owner != null) {
            head = HeadUtils.setSkullOwner(head, owner);
        }

        return head;
    }

    /**
     * Sets up the review items in the GUI.
     *
     * @param section The configuration section for the GUI
     */
    private void setupReviews(ConfigurationSection section) {
        try {
            plugin.getLogger().info("Setting up reviews in BarrioRatingInfoGUI.setupReviews");

            ConfigurationSection reviewSection = section.getConfigurationSection("review");
            if (reviewSection == null || reviewSlots.isEmpty()) {
                plugin.getLogger().warning("Review section is null or review slots are empty in BarrioRatingInfoGUI.setupReviews");
                return;
            }

            // Get all ratings
            plugin.getLogger().info("Getting ratings for barrio in BarrioRatingInfoGUI.setupReviews: " + barrioId);
            List<Map<String, Object>> ratings = ratingManager.getRatings(barrioId);
            plugin.getLogger().info("Got " + ratings.size() + " ratings for barrio: " + barrioId);

            // Calculate pagination
            int maxItemsPerPage = reviewSlots.size();
            if (maxItemsPerPage == 0) {
                plugin.getLogger().warning("Max items per page is 0 in BarrioRatingInfoGUI.setupReviews");
                return;
            }

            int startIndex = currentPage * maxItemsPerPage;
            int endIndex = Math.min(startIndex + maxItemsPerPage, ratings.size());

            // Check if current page is valid
            if (ratings.size() > 0 && startIndex >= ratings.size()) {
                currentPage = 0;
                startIndex = 0;
                endIndex = Math.min(maxItemsPerPage, ratings.size());
            }

            plugin.getLogger().info("Adding review items in BarrioRatingInfoGUI.setupReviews: " +
                    "page=" + currentPage + ", startIndex=" + startIndex + ", endIndex=" + endIndex);

            // Add review items
            for (int i = startIndex; i < endIndex; i++) {
                Map<String, Object> rating = ratings.get(i);
                int slotIndex = i - startIndex;

                if (slotIndex < reviewSlots.size()) {
                    int slot = reviewSlots.get(slotIndex);
                    ItemStack review = createReviewItem(rating, reviewSection);
                    inventory.setItem(slot, review);
                }
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI.setupReviews", e);
            e.printStackTrace();
        }
    }

    /**
     * Creates a review item.
     *
     * @param rating The rating data
     * @param section The configuration section for the review item
     * @return The created ItemStack
     */
    private ItemStack createReviewItem(Map<String, Object> rating, ConfigurationSection section) {
        try {
            plugin.getLogger().info("Creating review item in BarrioRatingInfoGUI.createReviewItem");

            Material material = Material.valueOf(section.getString("material", "PAPER"));
            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            if (meta == null) return item;

            // Get rating info
            plugin.getLogger().info("Getting rating info in BarrioRatingInfoGUI.createReviewItem");
            String raterUuid = (String) rating.get("rater");
            int stars = (int) rating.get("stars");
            String message = (String) rating.get("message");
            long date = (long) rating.get("date");

            // Get rater name
            plugin.getLogger().info("Getting rater name in BarrioRatingInfoGUI.createReviewItem: " + raterUuid);
            OfflinePlayer rater = Bukkit.getOfflinePlayer(UUID.fromString(raterUuid));
            String raterName = rater.getName() != null ? rater.getName() : "Unknown";

            // Format date
            String dateStr = dateFormat.format(new Date(date));

            // Format stars
            String starsFormat = section.getString("star_format." + stars, "&6" + "★".repeat(stars) + "&7" + "★".repeat(5 - stars));

            // Set display name
            String name = section.getString("name", "&eReview from %reviewer%");
            name = name.replace("%reviewer%", raterName);
            name = ChatColor.translateAlternateColorCodes('&', name);
            meta.setDisplayName(name);

            // Set lore
            List<String> lore = new ArrayList<>();
            for (String line : section.getStringList("lore")) {
                line = line.replace("%reviewer%", raterName)
                        .replace("%date%", dateStr)
                        .replace("%stars%", starsFormat)
                        .replace("%message%", message != null && !message.isEmpty() ? message : "No message");
                lore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(lore);

            item.setItemMeta(meta);
            return item;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI.createReviewItem", e);
            e.printStackTrace();
            return new ItemStack(Material.PAPER);
        }
    }

    /**
     * Sets up the navigation buttons (previous/next page).
     *
     * @param section The configuration section for the GUI
     */
    private void setupNavigationButtons(ConfigurationSection section) {
        try {

            // Previous page button
            ConfigurationSection prevSection = section.getConfigurationSection("prev_button");
            if (prevSection != null) {
                ItemStack prevButton = createItem(prevSection);

                // Only show if there are previous pages
                if (currentPage > 0) {
                    for (int slot : prevSection.getIntegerList("slots")) {
                        inventory.setItem(slot, prevButton);
                    }
                }
            }

            // Next page button
            ConfigurationSection nextSection = section.getConfigurationSection("next_button");
            if (nextSection != null) {
                ItemStack nextButton = createItem(nextSection);

                // Only show if there are more pages
                int maxItemsPerPage = reviewSlots.size();
                List<Map<String, Object>> ratings = ratingManager.getRatings(barrioId);

                if (maxItemsPerPage > 0 && ratings.size() > (currentPage + 1) * maxItemsPerPage) {
                    for (int slot : nextSection.getIntegerList("slots")) {
                        inventory.setItem(slot, nextButton);
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI.setupNavigationButtons", e);
            e.printStackTrace();
        }
    }

    /**
     * Creates a rate button for the barrio.
     *
     * @param barrioData The barrio data
     * @param section The configuration section for the rate button
     * @return The created ItemStack
     */
    private ItemStack createRateButton(BarrioManager.BarrioData barrioData, ConfigurationSection section) {
        try {

            Material material = Material.valueOf(section.getString("material", "EMERALD"));
            ItemStack item = new ItemStack(material);
            ItemMeta meta = item.getItemMeta();
            if (meta == null) return item;

            // Get barrio info
            String nickname = barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;

            // Set display name
            String name = section.getString("name", "&aRate %barrio_name%");
            name = name.replace("%barrio_name%", nickname);
            name = ChatColor.translateAlternateColorCodes('&', name);
            meta.setDisplayName(name);

            // Set lore
            List<String> lore = new ArrayList<>();
            for (String line : section.getStringList("lore")) {
                line = line.replace("%barrio_name%", nickname);
                lore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(lore);

            item.setItemMeta(meta);
            return item;
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI.createRateButton", e);
            e.printStackTrace();
            return new ItemStack(Material.EMERALD);
        }
    }

    /**
     * Handles click events in the GUI.
     *
     * @param event The InventoryClickEvent
     */
    public void handleClick(InventoryClickEvent event) {
        try {

            int slot = event.getSlot();
            ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
            if (section == null) {
                plugin.getLogger().warning("GUI config section is null in BarrioRatingInfoGUI.handleClick");
                return;
            }

            // Check for back button click
            ConfigurationSection backButtonSection = section.getConfigurationSection("back_button");
            if (backButtonSection != null) {
                List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
                if (backButtonSlots.contains(slot)) {
                    // Handle back button click - go back to main menu
                    player.closeInventory();
                    org.bukkit.Bukkit.dispatchCommand(player, "barrio");
                    return;
                }
            }

            // Check if rate button was clicked
            ConfigurationSection rateSection = section.getConfigurationSection("rate_button");
            if (rateSection != null && rateSection.getIntegerList("slots").contains(slot)) {

                player.closeInventory();
                BarrioRatingGUI ratingGUI = new BarrioRatingGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), ratingGUI);
                ratingGUI.open();
                return;
            }

            // Check if previous page button was clicked
            ConfigurationSection prevSection = section.getConfigurationSection("prev_button");
            if (prevSection != null && prevSection.getIntegerList("slots").contains(slot)) {

                if (currentPage > 0) {
                    currentPage--;
                    setupRatingInfoItems();
                    player.openInventory(inventory);
                }
                return;
            }

            // Check if next page button was clicked
            ConfigurationSection nextSection = section.getConfigurationSection("next_button");
            if (nextSection != null && nextSection.getIntegerList("slots").contains(slot)) {

                int maxItemsPerPage = reviewSlots.size();
                List<Map<String, Object>> ratings = ratingManager.getRatings(barrioId);

                if (maxItemsPerPage > 0 && ratings.size() > (currentPage + 1) * maxItemsPerPage) {
                    currentPage++;
                    setupRatingInfoItems();
                    player.openInventory(inventory);
                }
                return;
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingInfoGUI.handleClick", e);
            e.printStackTrace();
        }
    }
}
