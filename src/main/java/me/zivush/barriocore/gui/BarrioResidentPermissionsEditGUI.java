package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.ranks.Rank;
import org.bukkit.ChatColor;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GUI for editing resident permissions.
 * Unlike the visitor permissions, resident permissions are editable in this GUI.
 */
public class BarrioResidentPermissionsEditGUI extends BaseGUI {
    private String barrioId;
    private Map<String, Boolean> permissions;

    public BarrioResidentPermissionsEditGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_resident_permissions_gui");

        if (barrioId == null) {
            plugin.getLogger().severe("FATAL: barrioId received in BarrioResidentPermissionsEditGUI constructor is NULL!");
            player.closeInventory(); // Prevent opening broken GUI
            return;
        }

        this.barrioId = barrioId;
        setupPermissionsItems();
    }

    private Map<String, Boolean> loadPermissions() {
        // Get the resident permissions from the permission manager
        Map<String, Map<Rank, Boolean>> fullPerms = plugin.getPermissionManager().getFullBarrioPermissions().get(this.barrioId);
        Map<String, Boolean> residentPerms = new HashMap<>();

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) {
            plugin.getLogger().warning("GUI section is null for: " + guiName);
            return residentPerms;
        }

        // Initialize with default values (all true for residents)
        for (String key : guiSection.getKeys(false)) {
            if (key == null || key.equals("title") || key.equals("size") || key.equals("decoration")) {
                continue;
            }

            ConfigurationSection section = guiSection.getConfigurationSection(key);
            if (section != null && section.contains("slots")) {
                // If we have full permissions data, get the resident value
                if (fullPerms != null && fullPerms.containsKey(key) && fullPerms.get(key).containsKey(Rank.RESIDENT)) {
                    residentPerms.put(key, fullPerms.get(key).get(Rank.RESIDENT));
                } else {
                    // Default to true for residents
                    residentPerms.put(key, true);
                }
            }
        }

        return residentPerms;
    }

    @Override
    protected void setupItems() {
        // This is handled by setupPermissionsItems
    }

    protected void setupPermissionsItems() {
        this.permissions = loadPermissions();
        setupDecoration();

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) {
            plugin.getLogger().warning("GUI section is null during item setup for: " + guiName);
            return;
        }

        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration")) {
                continue;
            }

            ConfigurationSection section = guiSection.getConfigurationSection(key);
            if (section != null && section.contains("slots")) {
                ItemStack item = createItem(section);
                boolean currentPermValue = permissions.getOrDefault(key, true);
                updateItemStatus(item, currentPermValue);

                List<Integer> slots = section.getIntegerList("slots");
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, item);
                    }
                }
            }
        }
    }

    private void updateItemStatus(ItemStack item, boolean enabled) {
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            List<String> lore = meta.getLore();
            if (lore != null) {
                List<String> newLore = new ArrayList<>();
                for (String line : lore) {
                    newLore.add(line.replace("%status%",
                            enabled ? "§aEnabled" : "§cDisabled"));
                }
                meta.setLore(newLore);
                item.setItemMeta(meta);
            }
        }
    }

    public void handleClick(InventoryClickEvent event) {
        int clickedSlot = event.getSlot();
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration")) {
                continue;
            }

            ConfigurationSection section = guiSection.getConfigurationSection(key);
            if (section != null && section.contains("slots")) {
                List<Integer> slots = section.getIntegerList("slots");
                if (slots.contains(clickedSlot)) {
                    togglePermission(key);
                    break;
                }
            }
        }
    }

    private void togglePermission(String permission) {
        boolean newValue = !permissions.getOrDefault(permission, true);
        permissions.put(permission, newValue);

        // Update memory for RESIDENT rank
        plugin.getPermissionManager().setPermission(barrioId, permission, Rank.RESIDENT, newValue);

        // Refresh GUI
        setupPermissionsItems();
    }
}
