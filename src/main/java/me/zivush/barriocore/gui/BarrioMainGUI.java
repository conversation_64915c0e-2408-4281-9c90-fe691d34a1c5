package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.BarrioManager;
import me.zivush.barriocore.ranks.Rank;
import me.zivush.barriocore.util.HeadUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Main GUI for the barrio command, showing all barrios and allowing navigation.
 */
public class BarrioMainGUI extends BaseGUI {
    private int currentPage = 0;
    private boolean showOnlyMemberBarrios = false;
    private final List<Integer> headSlots = new ArrayList<>();
    private final Map<Integer, String> barrioSlots = new HashMap<>();
    private final Player player;

    /**
     * Constructor for the BarrioMainGUI.
     *
     * @param plugin The BarrioCore plugin instance
     * @param player The player viewing the GUI
     */
    public BarrioMainGUI(BarrioCore plugin, Player player) {
        super(plugin, player, "barrio_main_gui");
        this.player = player;

        // Load head slots from config
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section != null) {
            ConfigurationSection headSection = section.getConfigurationSection("barrio_head");
            if (headSection != null) {
                headSlots.addAll(headSection.getIntegerList("slots"));
            }
        }

        // Setup the GUI
        setupGuiItems();
    }

    @Override
    protected void setupItems() {
        // This is handled by setupGuiItems
    }

    /**
     * Sets up all GUI items including barrio heads, navigation buttons, and filter button.
     */
    private void setupGuiItems() {
        // Load items from config
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section == null) return;

        // Update title with page info
        String title = section.getString("title", "&6Barrio Main Menu &7(Page %page%)");
        title = title.replace("%page%", String.valueOf(currentPage + 1));
        title = ChatColor.translateAlternateColorCodes('&', title);

        // Create a new inventory with the updated title
        inventory = Bukkit.createInventory(player, inventory.getSize(), title);

        // Re-setup decoration
        setupDecoration();

        // Setup barrio heads
        setupBarrioHeads(section);

        // Setup navigation buttons
        setupNavigationButtons(section);

        // Setup filter button
        setupFilterButton(section);

        // Setup home button
        setupHomeButton(section);

        // Setup settings button
        setupSettingsButton(section);

        // Setup ratings button
        setupRatingsButton(section);

        // Setup top-rated button
        setupTopRatedButton(section);
    }

    /**
     * Sets up the barrio head items in the GUI.
     *
     * @param section The configuration section for the GUI
     */
    private void setupBarrioHeads(ConfigurationSection section) {
        ConfigurationSection headSection = section.getConfigurationSection("barrio_head");
        if (headSection == null) return;

        // Clear previous barrio slots
        barrioSlots.clear();

        // Get all barrios
        List<BarrioManager.BarrioData> barrios = getAllBarrios();

        // Filter barrios if needed
        if (showOnlyMemberBarrios) {
            barrios = barrios.stream()
                    .filter(barrioData -> isPlayerMemberOfBarrio(barrioData.getId()))
                    .collect(Collectors.toList());
        }

        // Calculate pagination
        int maxItemsPerPage = headSlots.size();
        if (maxItemsPerPage == 0) return;

        int startIndex = currentPage * maxItemsPerPage;
        int endIndex = Math.min(startIndex + maxItemsPerPage, barrios.size());

        // Check if current page is valid
        if (barrios.size() > 0 && startIndex >= barrios.size()) {
            currentPage = 0;
            startIndex = 0;
            endIndex = Math.min(maxItemsPerPage, barrios.size());
        }

        // Add barrio heads
        for (int i = startIndex; i < endIndex; i++) {
            BarrioManager.BarrioData barrioData = barrios.get(i);
            int slotIndex = i - startIndex;

            if (slotIndex < headSlots.size()) {
                int slot = headSlots.get(slotIndex);
                ItemStack head = createBarrioHead(barrioData, headSection);
                inventory.setItem(slot, head);
                barrioSlots.put(slot, barrioData.getId());
            }
        }
    }

    /**
     * Creates a player head item for a barrio.
     *
     * @param barrioData The barrio data
     * @param section The configuration section for the head item
     * @return The created ItemStack
     */
    private ItemStack createBarrioHead(BarrioManager.BarrioData barrioData, ConfigurationSection section) {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return head;

        // Get barrio info for placeholders
        UUID ownerUuid = barrioData.getOwnerUuid();
        OfflinePlayer owner = ownerUuid != null ? Bukkit.getOfflinePlayer(ownerUuid) : null;
        String barrioId = barrioData.getId();
        String nickname = barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;
        String ownerName = owner != null && owner.getName() != null ? owner.getName() : "Unknown";
        Rank playerRank = plugin.getRankManager().getRank(barrioId, player.getUniqueId());

        // Set display name
        String name = section.getString("name", "&e%barrio_name%");
        name = name.replace("%barrio_name%", nickname);
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            line = line.replace("%owner_name%", ownerName)
                    .replace("%barrio_id%", barrioId)
                    .replace("%player_rank%", playerRank.name());
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        head.setItemMeta(meta);

        // Use our utility class to properly set the head texture for offline players
        if (owner != null) {
            head = HeadUtils.setSkullOwner(head, owner);
        }

        return head;
    }

    /**
     * Sets up the navigation buttons (previous/next page).
     *
     * @param section The configuration section for the GUI
     */
    private void setupNavigationButtons(ConfigurationSection section) {
        // Previous page button
        ConfigurationSection prevSection = section.getConfigurationSection("prev_button");
        if (prevSection != null) {
            ItemStack prevButton = createItem(prevSection);
            ItemMeta meta = prevButton.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.translateAlternateColorCodes('&',
                        prevSection.getString("name", "&6Previous Page")));
                prevButton.setItemMeta(meta);
            }

            // Only show if there are previous pages
            if (currentPage > 0) {
                for (int slot : prevSection.getIntegerList("slots")) {
                    inventory.setItem(slot, prevButton);
                }
            }
        }

        // Next page button
        ConfigurationSection nextSection = section.getConfigurationSection("next_button");
        if (nextSection != null) {
            ItemStack nextButton = createItem(nextSection);
            ItemMeta meta = nextButton.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.translateAlternateColorCodes('&',
                        nextSection.getString("name", "&6Next Page")));
                nextButton.setItemMeta(meta);
            }

            // Only show if there are more pages
            int maxItemsPerPage = headSlots.size();
            List<BarrioManager.BarrioData> barrios = getAllBarrios();
            if (showOnlyMemberBarrios) {
                barrios = barrios.stream()
                        .filter(barrioData -> isPlayerMemberOfBarrio(barrioData.getId()))
                        .collect(Collectors.toList());
            }

            if (maxItemsPerPage > 0 && barrios.size() > (currentPage + 1) * maxItemsPerPage) {
                for (int slot : nextSection.getIntegerList("slots")) {
                    inventory.setItem(slot, nextButton);
                }
            }
        }
    }

    /**
     * Sets up the filter button.
     *
     * @param section The configuration section for the GUI
     */
    private void setupFilterButton(ConfigurationSection section) {
        ConfigurationSection filterSection = section.getConfigurationSection("filter_button");
        if (filterSection == null) return;

        ItemStack filterButton = createItem(filterSection);
        ItemMeta meta = filterButton.getItemMeta();
        if (meta == null) return;

        // Get filter text
        String filterText = showOnlyMemberBarrios ?
                filterSection.getString("filter_member", "Only Barrios You Are Member In") :
                filterSection.getString("filter_all", "All Barrios");

        // Set display name
        String name = filterSection.getString("name", "&aFilter: &f%filter%");
        name = name.replace("%filter%", filterText);
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : filterSection.getStringList("lore")) {
            line = line.replace("%filter%", filterText);
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        filterButton.setItemMeta(meta);

        // Add to inventory
        for (int slot : filterSection.getIntegerList("slots")) {
            inventory.setItem(slot, filterButton);
        }
    }

    /**
     * Sets up the home button.
     *
     * @param section The configuration section for the GUI
     */
    private void setupHomeButton(ConfigurationSection section) {
        ConfigurationSection homeSection = section.getConfigurationSection("home_button");
        if (homeSection == null) return;

        ItemStack homeButton = createItem(homeSection);

        // Add to inventory
        for (int slot : homeSection.getIntegerList("slots")) {
            inventory.setItem(slot, homeButton);
        }
    }

    /**
     * Sets up the settings button.
     *
     * @param section The configuration section for the GUI
     */
    private void setupSettingsButton(ConfigurationSection section) {
        ConfigurationSection settingsSection = section.getConfigurationSection("settings_button");
        if (settingsSection == null) return;

        ItemStack settingsButton = createItem(settingsSection);

        // Add to inventory
        for (int slot : settingsSection.getIntegerList("slots")) {
            inventory.setItem(slot, settingsButton);
        }
    }

    /**
     * Sets up the ratings button.
     *
     * @param section The configuration section for the GUI
     */
    private void setupRatingsButton(ConfigurationSection section) {
        ConfigurationSection ratingsSection = section.getConfigurationSection("ratings_button");
        if (ratingsSection == null) return;

        ItemStack ratingsButton = createItem(ratingsSection);

        // Add to inventory
        for (int slot : ratingsSection.getIntegerList("slots")) {
            inventory.setItem(slot, ratingsButton);
        }
    }

    /**
     * Sets up the top-rated button.
     *
     * @param section The configuration section for the GUI
     */
    private void setupTopRatedButton(ConfigurationSection section) {
        ConfigurationSection topRatedSection = section.getConfigurationSection("top_rated_button");
        if (topRatedSection == null) return;

        ItemStack topRatedButton = createItem(topRatedSection);

        // Add to inventory
        for (int slot : topRatedSection.getIntegerList("slots")) {
            inventory.setItem(slot, topRatedButton);
        }
    }

    /**
     * Gets all barrios from the BarrioManager.
     *
     * @return A list of all barrio data
     */
    private List<BarrioManager.BarrioData> getAllBarrios() {
        List<BarrioManager.BarrioData> result = new ArrayList<>();
        BarrioManager barrioManager = plugin.getBarrioManager();

        for (String barrioId : barrioManager.getAllBarrioIds()) {
            BarrioManager.BarrioData barrioData = barrioManager.getBarrioData(barrioId);
            if (barrioData != null) {
                result.add(barrioData);
            }
        }

        return result;
    }

    /**
     * Checks if the player is a member of a barrio (has any rank).
     *
     * @param barrioId The ID of the barrio
     * @return true if the player is a member, false otherwise
     */
    private boolean isPlayerMemberOfBarrio(String barrioId) {
        Rank rank = plugin.getRankManager().getRank(barrioId, player.getUniqueId());
        return rank != Rank.VISITOR;
    }

    /**
     * Handles click events in the GUI.
     *
     * @param event The InventoryClickEvent
     */
    public void handleClick(InventoryClickEvent event) {
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null) return;

        int slot = event.getSlot();
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section == null) return;

        // Check if a barrio head was clicked
        if (barrioSlots.containsKey(slot)) {
            String barrioId = barrioSlots.get(slot);
            visitBarrio(barrioId);
            return;
        }

        // Check if filter button was clicked
        ConfigurationSection filterSection = section.getConfigurationSection("filter_button");
        if (filterSection != null && filterSection.getIntegerList("slots").contains(slot)) {
            showOnlyMemberBarrios = !showOnlyMemberBarrios;
            currentPage = 0; // Reset to first page when changing filter
            setupGuiItems();
            player.openInventory(inventory);
            return;
        }

        // Check if home button was clicked
        ConfigurationSection homeSection = section.getConfigurationSection("home_button");
        if (homeSection != null && homeSection.getIntegerList("slots").contains(slot)) {
            player.closeInventory();
            Bukkit.dispatchCommand(player, "barrio home");
            return;
        }

        // Check if settings button was clicked
        ConfigurationSection settingsSection = section.getConfigurationSection("settings_button");
        if (settingsSection != null && settingsSection.getIntegerList("slots").contains(slot)) {
            player.closeInventory();
            Bukkit.dispatchCommand(player, "barrio settings");
            return;
        }

        // Check if ratings button was clicked
        ConfigurationSection ratingsSection = section.getConfigurationSection("ratings_button");
        if (ratingsSection != null && ratingsSection.getIntegerList("slots").contains(slot)) {
            // If player is in a barrio, show ratings for that barrio
            String worldName = player.getWorld().getName();
            if (worldName.startsWith("Barrios/")) {
                String barrioId = worldName.split("/")[1];
                player.closeInventory();
                Bukkit.dispatchCommand(player, "barrio ratings " + barrioId);
            } else {
                // Otherwise, just open the ratings command
                player.closeInventory();
                Bukkit.dispatchCommand(player, "barrio ratings");
            }
            return;
        }

        // Check if top-rated button was clicked
        ConfigurationSection topRatedSection = section.getConfigurationSection("top_rated_button");
        if (topRatedSection != null && topRatedSection.getIntegerList("slots").contains(slot)) {
            player.closeInventory();
            Bukkit.dispatchCommand(player, "barrio visit top-rated");
            return;
        }

        // Check if previous page button was clicked
        ConfigurationSection prevSection = section.getConfigurationSection("prev_button");
        if (prevSection != null && prevSection.getIntegerList("slots").contains(slot)) {
            if (currentPage > 0) {
                currentPage--;
                setupGuiItems();
                player.openInventory(inventory);
            }
            return;
        }

        // Check if next page button was clicked
        ConfigurationSection nextSection = section.getConfigurationSection("next_button");
        if (nextSection != null && nextSection.getIntegerList("slots").contains(slot)) {
            int maxItemsPerPage = headSlots.size();
            List<BarrioManager.BarrioData> barrios = getAllBarrios();
            if (showOnlyMemberBarrios) {
                barrios = barrios.stream()
                        .filter(barrioData -> isPlayerMemberOfBarrio(barrioData.getId()))
                        .collect(Collectors.toList());
            }

            if (maxItemsPerPage > 0 && barrios.size() > (currentPage + 1) * maxItemsPerPage) {
                currentPage++;
                setupGuiItems();
                player.openInventory(inventory);
            }
            return;
        }
    }

    /**
     * Visits a barrio by teleporting the player.
     *
     * @param barrioId The ID of the barrio to visit
     */
    private void visitBarrio(String barrioId) {
        player.closeInventory();

        // Check if player is banned from the barrio
        if (plugin.getPlayerDataManager().isBanned(barrioId, player.getUniqueId())) {
            player.sendMessage(plugin.getMessage("messages.player_banned_message"));
            return;
        }

        // Get the barrio owner's UUID
        UUID ownerUuid = plugin.getBarrioManager().getBarrioOwner(barrioId);
        OfflinePlayer owner = ownerUuid != null ? Bukkit.getOfflinePlayer(ownerUuid) : null;
        String ownerName = owner != null && owner.getName() != null ? owner.getName() : "Unknown";

        // Get the player's rank in this barrio
        Rank playerRank = plugin.getRankManager().getRank(barrioId, player.getUniqueId());

        // Check visit permission based on the player's rank
        Map<String, Map<Rank, Boolean>> barrioPerms = plugin.getPermissionManager().getBarrioPermissionsForAllRanks(barrioId);
        boolean canVisit = false;

        if (barrioPerms != null) {
            Map<Rank, Boolean> visitPerms = barrioPerms.get("visit_toggle");
            if (visitPerms != null) {
                // Get permission for the player's specific rank
                Boolean rankPerm = visitPerms.get(playerRank);
                if (rankPerm != null) {
                    canVisit = rankPerm;
                } else {
                    // If no specific permission is set for this rank, check the default
                    canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
                }
            } else {
                // If no permission entry exists, check the default
                canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
            }
        } else {
            // If no permissions are set for this barrio, check the default
            canVisit = plugin.getPermissionManager().getDefaultPermission("visit_toggle", playerRank);
        }

        if (!canVisit) {
            player.sendMessage(plugin.getMessage("messages.no_permission"));
            return;
        }

        // Teleport to the barrio
        teleportToBarrio(player, barrioId);
        player.sendMessage(plugin.getMessage("messages.teleported").replace("%player%", ownerName));
    }

    /**
     * Teleports a player to a barrio.
     *
     * @param player The player to teleport
     * @param barrioId The ID of the barrio
     */
    private void teleportToBarrio(Player player, String barrioId) {
        // Check if this is a template-based barrio
        String templateName = plugin.getBarrioManager().getBarrioTemplateName(barrioId);

        org.bukkit.World world;
        if (templateName != null) {
            // Template-based barrio - use the template name for the world path
            world = Bukkit.getWorld("Barrios/" + barrioId + "/" + templateName);
            plugin.getLogger().info("Attempting to teleport to template-based barrio: Barrios/" + barrioId + "/" + templateName);
        } else {
            // Regular barrio - use the standard world path
            world = Bukkit.getWorld("Barrios/" + barrioId + "/world");
            plugin.getLogger().info("Attempting to teleport to regular barrio: Barrios/" + barrioId + "/world");
        }

        if (world == null) {
            player.sendMessage(plugin.getMessage("messages.world_not_found"));
            plugin.getLogger().warning("World not found for barrio: " + barrioId);
            return;
        }

        org.bukkit.Location spawn = world.getSpawnLocation();
        player.teleport(spawn);
    }
}
