package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;
import java.util.stream.Collectors;

public abstract class BaseGUI {
    protected Inventory inventory;
    protected Player player;
    protected BarrioCore plugin;
    protected String guiName;

    public BaseGUI(BarrioCore plugin, Player player, String guiName) {
        this.plugin = plugin;
        this.player = player;
        this.guiName = guiName;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) {
            throw new IllegalStateException("GUI configuration for '" + guiName + "' not found in gui.yml");
        }

        String title = guiSection.getString("title", "&aDefault GUI");
        int size = guiSection.getInt("size", 27); // Default to 27 if not specified
        if (size % 9 != 0 || size < 9 || size > 54) {
            size = 27; // Enforce valid inventory size (9, 18, 27, 36, 45, 54)
        }

        this.inventory = Bukkit.createInventory(player, size, ChatColor.translateAlternateColorCodes('&', title));
        setupDecoration();
        setupItems();
    }

    public void setupDecoration() {
        ConfigurationSection decorSection = plugin.getGuiConfig().getConfigurationSection(guiName + ".decoration");
        if (decorSection != null) {
            for (String key : decorSection.getKeys(false)) {
                ConfigurationSection itemSection = decorSection.getConfigurationSection(key);
                ItemStack item = createItem(itemSection);
                List<Integer> slots = itemSection.getIntegerList("slots");
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, item);
                    }
                }
            }
        }
    }

    protected abstract void setupItems();

    protected ItemStack createItem(ConfigurationSection section) {
        Material material = Material.valueOf(section.getString("material", "STONE"));
        String name = ChatColor.translateAlternateColorCodes('&', section.getString("name", ""));
        List<String> lore = section.getStringList("lore").stream()
                .map(s -> ChatColor.translateAlternateColorCodes('&', s))
                .collect(Collectors.toList());
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(name);
            meta.setLore(lore);
            if (section.contains("custom_model_data")) {
                meta.setCustomModelData(section.getInt("custom_model_data"));
            }
            item.setItemMeta(meta);
        }
        return item;
    }

    public void open() {
        player.openInventory(inventory);
    }

    public Inventory getInventory() {
        return inventory;
    }

    public String getGuiName() {
        return guiName;
    }

    /**
     * Checks if a button has an execute command and runs it if present
     *
     * @param event The InventoryClickEvent
     * @param buttonSection The configuration section for the button
     * @return true if a command was executed, false otherwise
     */
    protected boolean checkAndExecuteCommand(InventoryClickEvent event, ConfigurationSection buttonSection) {
        if (buttonSection != null && buttonSection.contains("execute")) {
            String command = buttonSection.getString("execute");
            if (command != null && !command.isEmpty()) {
                // Execute the command as the player
                player.closeInventory();
                Bukkit.dispatchCommand(player, command.startsWith("/") ? command.substring(1) : command);
                return true;
            }
        }
        return false;
    }

    /**
     * Checks if a button at the given slot has an execute command and runs it if present
     *
     * @param event The InventoryClickEvent
     * @return true if a command was executed, false otherwise
     */
    public boolean checkAndExecuteCommandForSlot(InventoryClickEvent event) {
        int clickedSlot = event.getSlot();
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return false;

        // Find which button was clicked
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration")) {
                continue; // Skip meta keys
            }

            ConfigurationSection buttonSection = guiSection.getConfigurationSection(key);
            if (buttonSection == null) continue;

            List<Integer> slots = buttonSection.getIntegerList("slots");
            if (slots.contains(clickedSlot)) {
                return checkAndExecuteCommand(event, buttonSection);
            }
        }

        return false;
    }
}