package me.zivush.barriocore.gui;

import com.onarandombox.MultiverseCore.MultiverseCore;
import com.onarandombox.MultiverseCore.api.MVWorldManager;
import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import me.zivush.barriocore.modes.BarrioMode;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

public class BarrioCreateTemplateGUI extends BaseGUI {
    private final Database database;
    private String selectedTemplate = null;

    public BarrioCreateTemplateGUI(BarrioCore plugin, Database database, Player player) {
        super(plugin, player, "barrio_create_template");
        this.database = database;
    }

    @Override
    protected void setupItems() {
        // Set up template buttons
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Set up back button
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            ItemStack backButton = createItem(backButtonSection);
            List<Integer> backSlots = backButtonSection.getIntegerList("slots");
            for (int slot : backSlots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, backButton);
                }
            }
        }

        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("create_button") || key.equals("status_indicator") || key.equals("back_button") || key.equals("decoration")) continue;

            ConfigurationSection buttonSection = guiSection.getConfigurationSection(key);
            if (buttonSection != null) {
                ItemStack button = createItem(buttonSection);
                List<Integer> slots = buttonSection.getIntegerList("slots");
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, button);
                    }
                }
            }
        }

        // Set up status indicator (showing "No template selected")
        updateStatusIndicator();

        // Create button is not visible until a template is selected
        updateCreateButton();
    }

    private void updateStatusIndicator() {
        ConfigurationSection indicatorSection = plugin.getGuiConfig().getConfigurationSection(guiName + ".status_indicator");
        if (indicatorSection != null) {
            ItemStack indicator = createItem(indicatorSection);
            ItemMeta meta = indicator.getItemMeta();

            if (meta != null) {
                // Update the name based on selection status
                if (selectedTemplate == null) {
                    meta.setDisplayName(ChatColor.translateAlternateColorCodes('&',
                            indicatorSection.getString("no_selection_name", "&cNo template selected")));
                } else {
                    String templateDisplayName = plugin.getGuiConfig()
                            .getString(guiName + "." + selectedTemplate + ".name", selectedTemplate);
                    meta.setDisplayName(ChatColor.translateAlternateColorCodes('&',
                            indicatorSection.getString("selection_name", "&aSelected: &f%template%")
                                    .replace("%template%", ChatColor.stripColor(ChatColor.translateAlternateColorCodes('&', templateDisplayName)))));
                }

                indicator.setItemMeta(meta);
            }

            // Place indicators in the top row
            List<Integer> slots = indicatorSection.getIntegerList("slots");
            for (int slot : slots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, indicator);
                }
            }
        }
    }

    private void updateCreateButton() {
        // Only show create button if a template is selected
        ConfigurationSection buttonSection = plugin.getGuiConfig().getConfigurationSection(guiName + ".create_button");
        if (buttonSection != null) {
            List<Integer> slots = buttonSection.getIntegerList("slots");

            if (selectedTemplate != null) {
                // Show the create button
                ItemStack button = createItem(buttonSection);
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, button);
                    }
                }
            } else {
                // Hide the create button (replace with air)
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, new ItemStack(Material.AIR));
                    }
                }
            }
        }
    }

    public void handleClick(InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(event.getSlot())) {
                // Handle back button click - go back to type selection
                player.closeInventory();
                BarrioTypeGUI typeGUI = new BarrioTypeGUI(plugin, database, player);
                plugin.setOpenGui(player.getUniqueId(), typeGUI);
                typeGUI.open();
                return;
            }
        }

        // Check if it's the create button
        ConfigurationSection createButtonSection = guiSection.getConfigurationSection("create_button");
        if (createButtonSection != null && selectedTemplate != null) {
            ItemStack createButton = createItem(createButtonSection);
            if (clickedItem.isSimilar(createButton)) {
                player.closeInventory();
                createBarrioFromTemplate(selectedTemplate);
                return;
            }
        }

        // Check if it's a template button
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("create_button") || key.equals("status_indicator")) continue;

            ConfigurationSection buttonSection = guiSection.getConfigurationSection(key);
            if (buttonSection != null) {
                ItemStack button = createItem(buttonSection);
                if (hasSameBasicProperties(clickedItem, button)) {
                    // This is a template button
                    selectedTemplate = key;
                    updateTemplateButtons();
                    updateStatusIndicator();
                    updateCreateButton();

                    // Notify the player
                    String templateDisplayName = buttonSection.getString("name", key);
                    player.sendMessage(plugin.getMessage("messages.template_selected")
                            .replace("%template%", ChatColor.stripColor(ChatColor.translateAlternateColorCodes('&', templateDisplayName))));

                    break;
                }
            }
        }
    }

    private boolean hasSameBasicProperties(ItemStack item1, ItemStack item2) {
        if (item1.getType() != item2.getType()) return false;

        ItemMeta meta1 = item1.getItemMeta();
        ItemMeta meta2 = item2.getItemMeta();

        if (meta1 == null || meta2 == null) return false;

        return meta1.getDisplayName().equals(meta2.getDisplayName());
    }

    private void updateTemplateButtons() {
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // First update the title if needed
        if (selectedTemplate != null) {
            ConfigurationSection selectedSection = guiSection.getConfigurationSection(selectedTemplate);
            if (selectedSection != null) {
                ConfigurationSection onClickSection = selectedSection.getConfigurationSection("on_click");
                if (onClickSection != null && onClickSection.contains("new_title")) {
                    String newTitle = ChatColor.translateAlternateColorCodes('&',
                            onClickSection.getString("new_title"));

                    // Create new inventory and copy items
                    Inventory newInventory = Bukkit.createInventory(player, inventory.getSize(), newTitle);
                    newInventory.setContents(inventory.getContents());
                    inventory = newInventory;
                }
            }
        }

        // Then update all buttons
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("create_button") || key.equals("status_indicator")) continue;

            ConfigurationSection buttonSection = guiSection.getConfigurationSection(key);
            if (buttonSection != null) {
                ItemStack button;
                if (key.equals(selectedTemplate)) {
                    // Selected button - use onClick custom model data
                    button = createItem(buttonSection);
                    ItemMeta meta = button.getItemMeta();
                    if (meta != null) {
                        ConfigurationSection onClickSection = buttonSection.getConfigurationSection("on_click");
                        if (onClickSection != null) {
                            meta.setCustomModelData(onClickSection.getInt("custom_mode_data"));
                            button.setItemMeta(meta);
                        }
                    }
                } else {
                    // Non-selected button - use default custom model data
                    button = createItem(buttonSection);
                }

                List<Integer> slots = buttonSection.getIntegerList("slots");
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, button);
                    }
                }
            }
        }

        // Open the updated inventory
        player.openInventory(inventory);
    }


    private void createBarrioFromTemplate(String templateName) {
        // Generate unique ID
        String id = generateUniqueID();

        // Create default nickname based on player name and count
        String defaultNickname = plugin.getWorldSettingsManager().createDefaultNickname(player.getUniqueId());

        // Create barrio using BarrioManager
        // This also sets the owner in the barrios table, which is used by PlayerDataManager
        plugin.getBarrioManager().createBarrio(id, player.getUniqueId(), defaultNickname);

        // Create worlds from template
        createWorldsFromTemplate(id, templateName);

        // Teleport player
        teleportToSafeSpawn(id);

        // Set cooldown
        if (plugin.getCommand("barrio").getExecutor() instanceof me.zivush.barriocore.BarrioCommand) {
            ((me.zivush.barriocore.BarrioCommand) plugin.getCommand("barrio").getExecutor()).setCreateCooldown(player);
        }
        ConfigurationSection defaultPermsSection = plugin.getConfig().getConfigurationSection("default_permissions");
        if (defaultPermsSection != null) {
            Map<String, Object> rawPerms = defaultPermsSection.getValues(false);
            Map<String, Boolean> defaultPerms = rawPerms.entrySet().stream()
                    .filter(entry -> entry.getValue() instanceof Boolean)
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> (Boolean) entry.getValue()));

            plugin.getLogger().info("Setting up default permissions for new template barrio " + id);
            for (Map.Entry<String, Boolean> entry : defaultPerms.entrySet()) {
                plugin.getLogger().info("Creating default permission: " + entry.getKey() + " = " + entry.getValue());
                // Insert into database with separate values for each rank
                // Visitors get the config value, residents and trusted get true
                database.executeUpdate(
                        "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                        id, entry.getKey(), entry.getValue(), true, true
                );
                // Update in-memory map for each rank
                plugin.getPermissionManager().setPermission(id, entry.getKey(), me.zivush.barriocore.ranks.Rank.VISITOR, entry.getValue());
                plugin.getPermissionManager().setPermission(id, entry.getKey(), me.zivush.barriocore.ranks.Rank.RESIDENT, true);
                plugin.getPermissionManager().setPermission(id, entry.getKey(), me.zivush.barriocore.ranks.Rank.TRUSTED, true);
            }

            // Add trusted-only permissions
            plugin.getLogger().info("Adding trusted-only permissions for new template barrio " + id);

            // Add moderation_access permission (default: true)
            database.executeUpdate(
                    "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                    id, "moderation_access", false, false, true
            );
            plugin.getPermissionManager().setPermission(id, "moderation_access", me.zivush.barriocore.ranks.Rank.TRUSTED, true);

            // Add settings_access permission (default: true)
            database.executeUpdate(
                    "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                    id, "settings_access", false, false, true
            );
            plugin.getPermissionManager().setPermission(id, "settings_access", me.zivush.barriocore.ranks.Rank.TRUSTED, true);

            // Add custom_crops_access permission (default: false for visitors, true for residents and trusted)
            database.executeUpdate(
                    "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                    id, "custom_crops_access", false, true, true
            );
            plugin.getPermissionManager().setPermission(id, "custom_crops_access", me.zivush.barriocore.ranks.Rank.VISITOR, false);
            plugin.getPermissionManager().setPermission(id, "custom_crops_access", me.zivush.barriocore.ranks.Rank.RESIDENT, true);
            plugin.getPermissionManager().setPermission(id, "custom_crops_access", me.zivush.barriocore.ranks.Rank.TRUSTED, true);

            plugin.getLogger().info("Finished setting up default permissions for template barrio " + id);
        } else {
            plugin.getLogger().warning("Could not find 'default_permissions' section in config.yml for template barrio " + id);
        }


        String templatePath = "templates." + templateName;
        String mode = plugin.getConfig().getString(templatePath + ".mode", "UNLIMITED");

        if (!mode.equals("UNLIMITED")) {
            if (mode.equals("RENTING")) {
                String time = plugin.getConfig().getString(templatePath + ".renting.time");
                double price = plugin.getConfig().getDouble(templatePath + ".renting.price");
                boolean rentOff = plugin.getConfig().getBoolean(templatePath + ".renting.rent_off", false);
                BarrioMode barrioMode = rentOff ? BarrioMode.RENTING_OFF : BarrioMode.RENTING;
                plugin.getModeManager().setBarrioMode(id, barrioMode, time, null, false, price);
            } else if (mode.equals("INACTIVITY")) {
                String time = plugin.getConfig().getString(templatePath + ".inactivity.time");
                String groupCheck = plugin.getConfig().getString(templatePath + ".inactivity.group-check");
                boolean onlineInWorld = plugin.getConfig().getBoolean(templatePath + ".inactivity.online-in-world");
                plugin.getModeManager().setBarrioMode(id, BarrioMode.INACTIVITY, time, groupCheck, onlineInWorld, 0);
            }
        }
    }

    private String generateUniqueID() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    private void createWorldsFromTemplate(String id, String templateName) {
        MultiverseCore mvCore = (MultiverseCore) Bukkit.getPluginManager().getPlugin("Multiverse-Core");
        if (mvCore == null) {
            player.sendMessage(plugin.getMessage("messages.multiverse_not_installed"));
            return;
        }

        MVWorldManager worldManager = mvCore.getMVWorldManager();

        // Source template world path - using just the template name without the plugins/BarrioCore prefix
        String sourceWorld = "Templates/" + templateName;

        // Destination world path
        String destWorld = "Barrios/" + id + "/" + templateName;

        // Log the paths for debugging
        plugin.getLogger().info("Attempting to clone world from '" + sourceWorld + "' to '" + destWorld + "'");

        try {
            // Check if the source world exists in Multiverse
            if (!worldManager.isMVWorld(sourceWorld)) {
                // Try to load the world first
                plugin.getLogger().info("Source world not loaded, attempting to load: " + sourceWorld);
                boolean loaded = worldManager.loadWorld(sourceWorld);

                if (!loaded) {
                    plugin.getLogger().severe("Failed to load template world: " + sourceWorld);
                    return;
                }
            }

            // Make sure the destination directory exists
            File destDir = new File(Bukkit.getWorldContainer(), destWorld);
            if (!destDir.getParentFile().exists()) {
                destDir.getParentFile().mkdirs();
            }

            // Clone the world
            plugin.getLogger().info("Cloning world from '" + sourceWorld + "' to '" + destWorld + "'");
            boolean success = worldManager.cloneWorld(sourceWorld, destWorld);

            if (success) {
                plugin.getLogger().info("Successfully cloned world to: " + destWorld);

                // Set initial border for the world
                int defaultBorder = plugin.getConfig().getInt("border.default", 300);
                setBorder(destWorld, defaultBorder);
            } else {
                plugin.getLogger().severe("Failed to clone template world from " + sourceWorld + " to " + destWorld);

                // Try an alternative approach - manual file copy
                plugin.getLogger().info("Attempting manual file copy as fallback...");
                try {
                    File sourceDir = new File(Bukkit.getWorldContainer(), sourceWorld);
                    if (sourceDir.exists() && sourceDir.isDirectory()) {
                        // Use Java NIO for copying
                        Files.walk(sourceDir.toPath())
                                .forEach(source -> {
                                    try {
                                        Path destination = destDir.toPath().resolve(sourceDir.toPath().relativize(source));
                                        if (Files.isDirectory(source)) {
                                            if (!Files.exists(destination)) {
                                                Files.createDirectory(destination);
                                            }
                                        } else {
                                            Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);
                                        }
                                    } catch (IOException e) {
                                        plugin.getLogger().severe("Error copying file: " + e.getMessage());
                                    }
                                });

                        // Now try to load the world with Multiverse
                        if (worldManager.loadWorld(destWorld)) {
                            int defaultBorder = plugin.getConfig().getInt("border.default", 300);
                            plugin.getLogger().info("Successfully loaded manually copied world: " + destWorld);
                            setBorder(destWorld, defaultBorder);
                        } else {
                            plugin.getLogger().severe("Failed to load manually copied world: " + destWorld);
                        }
                    }
                } catch (IOException e) {
                    plugin.getLogger().severe("Manual file copy failed: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Error creating world from template: " + e.getMessage());
            e.printStackTrace();
        }
    }


    private void setBorder(String worldName, int size) {
        World world = Bukkit.getWorld(worldName);
        if (world != null) {
            WorldBorder border = world.getWorldBorder();
            border.setCenter(0, 0);
            border.setSize(size);
        }
    }

    private void teleportToSafeSpawn(String id) {
        // Use the template name for the world path
        World world = Bukkit.getWorld("Barrios/" + id + "/" + selectedTemplate);
        if (world == null) {
            player.sendMessage(plugin.getMessage("messages.world_not_found"));
            return;
        }

        Location spawn = findSafeSpawn(world);
        player.teleport(spawn);
        player.sendMessage(plugin.getMessage("messages.barrio_created"));
    }

    private Location findSafeSpawn(World world) {
        Location spawn = new Location(world, 0.5, 64, 0.5); // Start at 0,0
        int highestY = world.getHighestBlockYAt(0, 0);
        spawn.setY(highestY + 1);

        Block feetBlock = spawn.getBlock();
        Block headBlock = feetBlock.getRelative(BlockFace.UP);

        if (isSafeLocation(feetBlock, headBlock)) {
            return spawn;
        }

        // Search for a safe spot
        for (int x = -5; x <= 5; x++) {
            for (int z = -5; z <= 5; z++) {
                Location candidate = new Location(world, x + 0.5, 64, z + 0.5);
                candidate.setY(world.getHighestBlockYAt(x, z) + 1);
                feetBlock = candidate.getBlock();
                headBlock = feetBlock.getRelative(BlockFace.UP);
                if (isSafeLocation(feetBlock, headBlock)) {
                    return candidate;
                }
            }
        }

        // If no safe location found, create a platform at 0,0
        plugin.getLogger().warning("No safe spawn location found in barrio. Creating a platform at 0,0.");
        spawn = new Location(world, 0.5, 64, 0.5);

        // Find the highest block, even if it's water
        highestY = world.getHighestBlockYAt(0, 0);

        // Place a platform at or slightly above the highest block
        spawn.setY(highestY + 1);

        // Create a small platform (3x3)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                Block block = world.getBlockAt(x, highestY, z);
                block.setType(Material.STONE);
            }
        }

        // Clear space for the player (2 blocks high)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                for (int y = 1; y <= 2; y++) {
                    Block block = world.getBlockAt(x, highestY + y, z);
                    block.setType(Material.AIR);
                }
            }
        }

        return spawn;
    }

    private boolean isSafeLocation(Block feetBlock, Block headBlock) {
        Material feetType = feetBlock.getType();
        Material headType = headBlock.getType();
        Material belowType = feetBlock.getRelative(BlockFace.DOWN).getType();

        // Check if feet and head are air, and below is a solid block
        return (feetType == Material.AIR && headType == Material.AIR) &&
                (belowType.isSolid() && belowType != Material.LAVA && belowType != Material.WATER &&
                        !belowType.name().contains("LEAVES") && !belowType.name().contains("LOG"));
    }
}
