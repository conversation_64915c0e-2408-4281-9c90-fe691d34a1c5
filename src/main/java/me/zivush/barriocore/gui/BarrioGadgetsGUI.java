package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.gadgets.BarrioGadgetSettings;
import me.zivush.barriocore.gadgets.BarrioGadgetsManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class BarrioGadgetsGUI extends BaseGUI {

    private final String barrioId;
    private final BarrioGadgetsManager gadgetsManager;

    public BarrioGadgetsGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_gadgets_gui"); // Use the key from gui.yml
        this.barrioId = barrioId;
        this.gadgetsManager = plugin.getGadgetsManager(); // Assuming you add a getter in BarrioCore
        // Re-setup items after constructor finishes to ensure gadgetsManager is available
        setupItems();
    }

    @Override
    protected void setupItems() {
        if (gadgetsManager == null) return; // Gadgets manager might not be ready if called from super constructor early

        BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
        if (settings == null) {
            player.closeInventory();
            return;
        }

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Set up back button
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            ItemStack backButton = createItem(backButtonSection);
            List<Integer> backSlots = backButtonSection.getIntegerList("slots");
            for (int slot : backSlots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, backButton);
                }
            }
        }

        // Iterate through configured items in gui.yml
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) continue; // Skip meta keys

            ConfigurationSection itemSection = guiSection.getConfigurationSection(key);
            if (itemSection == null) continue;

            ItemStack item = createItem(itemSection); // Create base item

            // Customize based on gadget type and state
            ItemMeta meta = item.getItemMeta();
            if (meta == null) continue;

            List<String> lore = meta.getLore() != null ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
            String statusText = "";

            switch (key) {
                case "creeper_toggle":
                    statusText = settings.isCreeperDamageEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "creeper_block_toggle":
                    statusText = settings.isCreeperBlockDamageEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "ghast_toggle":
                    statusText = settings.isGhastDamageEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "ghast_block_toggle":
                    statusText = settings.isGhastBlockDamageEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "fire_toggle":
                    statusText = settings.isFireSpreadEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "enderman_toggle":
                    statusText = settings.isEndermanGriefEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "ravager_toggle":
                    statusText = settings.isRavagerGriefEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "title_message":
                    statusText = settings.isShowEntryTitleEnabled() ? "&aEnabled" : "&cDisabled";
                    break;
                case "chat_message":
                    statusText = settings.isShowEntryChatEnabled() ? "&aEnabled" : "&cDisabled";
                    break;

                case "expand_border":
                    // No status needed, just the button action
                    break;
                default:
                    // Skip unknown keys or handle differently
                    continue;
            }

            // Replace placeholder in lore
            final String finalStatusText = statusText;
            List<String> finalLore = lore.stream()
                    .map(line -> ChatColor.translateAlternateColorCodes('&', line.replace("%status%", finalStatusText)))
                    .collect(Collectors.toList());

            meta.setLore(finalLore);
            item.setItemMeta(meta);

            // Place item in specified slots
            List<Integer> slots = itemSection.getIntegerList("slots");
            for (int slot : slots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, item);
                }
            }
        }
    }


    public void handleClick(InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(event.getSlot())) {
                // Handle back button click
                player.closeInventory();
                org.bukkit.Bukkit.dispatchCommand(player, "barrio settings");
                return;
            }
        }

        BarrioGadgetSettings settings = gadgetsManager.getSettings(barrioId);
        if (settings == null) return; // Should not happen if GUI opened

        String clickedKey = null;
        // Find which item configuration matches the clicked item
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration")) continue;
            ConfigurationSection itemSection = guiSection.getConfigurationSection(key);
            if (itemSection != null) {
                // Basic check - more robust would involve checking NBT or custom model data if needed
                Material mat = Material.matchMaterial(itemSection.getString("material", ""));
                if (mat != null && clickedItem.getType() == mat) {
                    // Check custom model data if present in config AND on item
                    if (itemSection.contains("custom_model_data")) {
                        if (clickedItem.hasItemMeta() && clickedItem.getItemMeta().hasCustomModelData() &&
                                clickedItem.getItemMeta().getCustomModelData() == itemSection.getInt("custom_model_data")) {
                            clickedKey = key;
                            break;
                        }
                    } else {
                        // If config doesn't specify CMD, assume it matches if material is correct
                        clickedKey = key;
                        break;
                    }
                }
            }
        }


        if (clickedKey == null) return; // Clicked on something not defined as a button

        boolean needsRefresh = false;
        String feedbackMsg = null;

        switch (clickedKey) {
            case "creeper_toggle":
                settings.setCreeperDamage(!settings.isCreeperDamageEnabled());
                feedbackMsg = plugin.getMessage("messages.gadgets_creeper_toggle").replace("%status%", settings.isCreeperDamageEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "creeper_block_toggle":
                settings.setCreeperBlockDamage(!settings.isCreeperBlockDamageEnabled());
                feedbackMsg = "Creeper block damage " + (settings.isCreeperBlockDamageEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "ghast_toggle":
                settings.setGhastDamage(!settings.isGhastDamageEnabled());
                feedbackMsg = "Ghast player damage " + (settings.isGhastDamageEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "ghast_block_toggle":
                settings.setGhastBlockDamage(!settings.isGhastBlockDamageEnabled());
                feedbackMsg = "Ghast block damage " + (settings.isGhastBlockDamageEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "fire_toggle":
                settings.setFireSpread(!settings.isFireSpreadEnabled());
                feedbackMsg = plugin.getMessage("messages.gadgets_fire_toggle").replace("%status%", settings.isFireSpreadEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "enderman_toggle":
                settings.setEndermanGrief(!settings.isEndermanGriefEnabled());
                feedbackMsg = plugin.getMessage("messages.gadgets_enderman_toggle").replace("%status%", settings.isEndermanGriefEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "ravager_toggle":
                settings.setRavagerGrief(!settings.isRavagerGriefEnabled());
                feedbackMsg = plugin.getMessage("messages.gadgets_ravager_toggle").replace("%status%", settings.isRavagerGriefEnabled() ? "enabled" : "disabled");
                needsRefresh = true;
                break;
            case "title_message":
                settings.setShowEntryTitle(!settings.isShowEntryTitleEnabled());
                feedbackMsg = plugin.getMessage("messages.gadgets_title_toggle").replace("%status%", settings.isShowEntryTitleEnabled() ? plugin.getMessage("messages.gadgets_status_enabled") : plugin.getMessage("messages.gadgets_status_disabled")); // Add message key
                needsRefresh = true;
                break;
            case "chat_message":
                settings.setShowEntryChat(!settings.isShowEntryChatEnabled());
                feedbackMsg = plugin.getMessage("messages.gadgets_chat_toggle").replace("%status%", settings.isShowEntryChatEnabled() ? plugin.getMessage("messages.gadgets_status_enabled") : plugin.getMessage("messages.gadgets_status_disabled"));   // Add message key
                needsRefresh = true;
                break;

            case "expand_border":
                player.closeInventory();
                // Execute the expand command for the player
                Bukkit.dispatchCommand(player, "barrio expand");
                return; // Don't save or refresh
            default:
                return; // Unknown button clicked
        }

        if (needsRefresh) {
            gadgetsManager.saveSettings(settings); // Save changes to DB
            setupItems(); // Refresh the GUI display
            if (feedbackMsg != null) {
                player.sendMessage(feedbackMsg);
            }
        }
    }
}