package me.zivush.barriocore.gui;

import com.onarandombox.MultiverseCore.MultiverseCore;
import com.onarandombox.MultiverseCore.api.MVWorldManager;
import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

public class BarrioCreateRegularGUI extends BaseGUI {
    private final Database database;

    public BarrioCreateRegularGUI(BarrioCore plugin, Database database, Player player) {
        super(plugin, player, "barrio_create_regular");
        this.database = database;
    }

    @Override
    protected void setupItems() {
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Set up back button
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            ItemStack backButton = createItem(backButtonSection);
            List<Integer> backSlots = backButtonSection.getIntegerList("slots");
            for (int slot : backSlots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, backButton);
                }
            }
        }

        // Set up create button
        ConfigurationSection buttonSection = plugin.getGuiConfig().getConfigurationSection(guiName + ".create_button");
        if (buttonSection != null) {
            ItemStack button = createItem(buttonSection);
            List<Integer> slots = buttonSection.getIntegerList("slots");
            for (int slot : slots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, button);
                }
            }
        }
    }

    public void handleClick(InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(event.getSlot())) {
                // Handle back button click - go back to type selection
                player.closeInventory();
                BarrioTypeGUI typeGUI = new BarrioTypeGUI(plugin, database, player);
                plugin.setOpenGui(player.getUniqueId(), typeGUI);
                typeGUI.open();
                return;
            }
        }

        ConfigurationSection buttonSection = plugin.getGuiConfig().getConfigurationSection(guiName + ".create_button");
        if (buttonSection == null) return;

        ItemStack button = createItem(buttonSection);
        if (clickedItem.isSimilar(button)) {
            player.closeInventory();
            createBarrio();
        }
    }

    private void createBarrio() {
        // Generate unique ID
        String id = generateUniqueID();

        // Create default nickname based on player name and count
        String defaultNickname = plugin.getWorldSettingsManager().createDefaultNickname(player.getUniqueId());

        // Create barrio using BarrioManager
        // This also sets the owner in the barrios table, which is used by PlayerDataManager
        plugin.getBarrioManager().createBarrio(id, player.getUniqueId(), defaultNickname);
        ConfigurationSection defaultPermsSection = plugin.getConfig().getConfigurationSection("default_permissions");
        if (defaultPermsSection != null) {
            Map<String, Object> rawPerms = defaultPermsSection.getValues(false);
            Map<String, Boolean> defaultPerms = rawPerms.entrySet().stream()
                    .filter(entry -> entry.getValue() instanceof Boolean)
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> (Boolean) entry.getValue()));

            plugin.getLogger().info("Setting up default permissions for new barrio " + id);
            for (Map.Entry<String, Boolean> entry : defaultPerms.entrySet()) {
                plugin.getLogger().info("Creating default permission: " + entry.getKey() + " = " + entry.getValue());
                // Insert into database with separate values for each rank
                // Visitors get the config value, residents and trusted get true
                database.executeUpdate(
                        "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                        id, entry.getKey(), entry.getValue(), true, true
                );
                // Update in-memory map for each rank
                plugin.getPermissionManager().setPermission(id, entry.getKey(), me.zivush.barriocore.ranks.Rank.VISITOR, entry.getValue());
                plugin.getPermissionManager().setPermission(id, entry.getKey(), me.zivush.barriocore.ranks.Rank.RESIDENT, true);
                plugin.getPermissionManager().setPermission(id, entry.getKey(), me.zivush.barriocore.ranks.Rank.TRUSTED, true);
            }

            // Add trusted-only permissions
            plugin.getLogger().info("Adding trusted-only permissions for new barrio " + id);

            // Add moderation_access permission (default: true)
            database.executeUpdate(
                    "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                    id, "moderation_access", false, false, true
            );
            plugin.getPermissionManager().setPermission(id, "moderation_access", me.zivush.barriocore.ranks.Rank.TRUSTED, true);

            // Add settings_access permission (default: true)
            database.executeUpdate(
                    "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) VALUES (?, ?, ?, ?, ?)",
                    id, "settings_access", false, false, true
            );
            plugin.getPermissionManager().setPermission(id, "settings_access", me.zivush.barriocore.ranks.Rank.TRUSTED, true);

            plugin.getLogger().info("Finished setting up default permissions for barrio " + id);
        } else {
            plugin.getLogger().warning("Could not find 'default_permissions' section in config.yml for barrio " + id);
        }

        // Create worlds
        createWorlds(id);

        // Teleport player
        teleportToSafeSpawn(id);

        // Set cooldown
        if (plugin.getCommand("barrio").getExecutor() instanceof me.zivush.barriocore.BarrioCommand) {
            ((me.zivush.barriocore.BarrioCommand) plugin.getCommand("barrio").getExecutor()).setCreateCooldown(player);
        }
    }

    private String generateUniqueID() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    private void createWorlds(String id) {
        MultiverseCore mvCore = (MultiverseCore) Bukkit.getPluginManager().getPlugin("Multiverse-Core");
        if (mvCore == null) {
            player.sendMessage(plugin.getMessage("messages.multiverse_not_installed"));
            return;
        }

        String basePath = "Barrios/" + id + "/";

        // Use Multiverse commands instead of direct API calls to avoid freezing the server
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mv create " + basePath + "world normal");
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mv create " + basePath + "world_nether nether");
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "mv create " + basePath + "world_the_end end");

        // Set initial border for each world
        int defaultBorder = plugin.getConfig().getInt("border.default", 300);
        setBorder(basePath + "world", defaultBorder);
        setBorder(basePath + "world_nether", defaultBorder);
        setBorder(basePath + "world_the_end", defaultBorder);
    }

    private void setBorder(String worldName, int size) {
        World world = Bukkit.getWorld(worldName);
        if (world != null) {
            WorldBorder border = world.getWorldBorder();
            border.setCenter(0, 0);
            border.setSize(size);
        }
    }

    private void teleportToSafeSpawn(String id) {
        World world = Bukkit.getWorld("Barrios/" + id + "/world");
        if (world == null) {
            player.sendMessage(plugin.getMessage("messages.world_not_found"));
            return;
        }

        Location spawn = findSafeSpawn(world);
        player.teleport(spawn);
        player.sendMessage(plugin.getMessage("messages.barrio_created"));
    }

    private Location findSafeSpawn(World world) {
        Location spawn = new Location(world, 0.5, 64, 0.5); // Start at 0,0
        int highestY = world.getHighestBlockYAt(0, 0);
        spawn.setY(highestY + 1);

        Block feetBlock = spawn.getBlock();
        Block headBlock = feetBlock.getRelative(BlockFace.UP);

        if (isSafeLocation(feetBlock, headBlock)) {
            return spawn;
        }

        // Search for a safe spot
        for (int x = -5; x <= 5; x++) {
            for (int z = -5; z <= 5; z++) {
                Location candidate = new Location(world, x + 0.5, 64, z + 0.5);
                candidate.setY(world.getHighestBlockYAt(x, z) + 1);
                feetBlock = candidate.getBlock();
                headBlock = feetBlock.getRelative(BlockFace.UP);
                if (isSafeLocation(feetBlock, headBlock)) {
                    return candidate;
                }
            }
        }

        // If no safe location found, create a platform at 0,0
        plugin.getLogger().warning("No safe spawn location found in barrio. Creating a platform at 0,0.");
        spawn = new Location(world, 0.5, 64, 0.5);

        // Find the highest block, even if it's water
        highestY = world.getHighestBlockYAt(0, 0);

        // Place a platform at or slightly above the highest block
        spawn.setY(highestY + 1);

        // Create a small platform (3x3)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                Block block = world.getBlockAt(x, highestY, z);
                block.setType(Material.STONE);
            }
        }

        // Clear space for the player (2 blocks high)
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                for (int y = 1; y <= 2; y++) {
                    Block block = world.getBlockAt(x, highestY + y, z);
                    block.setType(Material.AIR);
                }
            }
        }

        return spawn;
    }

    private boolean isSafeLocation(Block feetBlock, Block headBlock) {
        Material feetType = feetBlock.getType();
        Material headType = headBlock.getType();
        Material belowType = feetBlock.getRelative(BlockFace.DOWN).getType();

        // Check if feet and head are air, and below is a solid block
        return (feetType == Material.AIR && headType == Material.AIR) &&
                (belowType.isSolid() && belowType != Material.LAVA && belowType != Material.WATER &&
                        !belowType.name().contains("LEAVES") && !belowType.name().contains("LOG"));
    }
}