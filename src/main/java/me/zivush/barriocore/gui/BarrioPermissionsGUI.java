package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// In: me.zivush.barriocore.gui.BarrioPermissionsGUI

public class BarrioPermissionsGUI extends BaseGUI {
    private final String barrioId;
    private Map<String, Boolean> permissions;

    public BarrioPermissionsGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_permissions_gui");
        // Debug logging using proper debug methods
        plugin.debug("BarrioPermissionsGUI Constructor received barrioId: '" + barrioId + "'");
        if (barrioId == null) {
            plugin.getLogger().severe("FATAL: barrioId received in BarrioPermissionsGUI constructor is NULL!");
            // Optionally throw an exception or handle gracefully
            // throw new IllegalArgumentException("barrioId cannot be null for BarrioPermissionsGUI");
        }
        this.barrioId = barrioId;
        setupPermissionsItems();
    }

    private Map<String, Boolean> loadPermissions() {
        Map<String, Boolean> perms = plugin.getPermissionManager().getBarrioPermissions().get(this.barrioId);
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);

        if (guiSection == null) {
            plugin.getLogger().warning("GUI section is null for: " + guiName);
            return perms;
        }

        // Debug logging using proper debug methods
        plugin.debug("loadPermissions called for barrioId: '" + this.barrioId + "'");
        if (this.barrioId == null) {
            plugin.getLogger().severe("FATAL: this.barrioId is NULL inside loadPermissions!");
        }


        for (String key : guiSection.getKeys(false)) {
            if (key == null) {
                plugin.getLogger().warning("Null key found in GUI config");
                continue;
            }

            ConfigurationSection section = guiSection.getConfigurationSection(key);
            if (section != null && section.contains("default")) {
                // Debug logging using proper debug methods
                plugin.debug("Calling hasPermission with barrioId: '" + this.barrioId + "', player: " + player.getName() + ", key: '" + key + "'");
                boolean value = perms != null ? perms.getOrDefault(key, getDefaultPermissionValue(key)) : getDefaultPermissionValue(key);
                perms.put(key, value);
            }
        }

        return perms;
    }
    @Override
    protected void setupItems() {
    }
    protected void setupPermissionsItems() {
        // Debug logging using proper debug methods
        plugin.debug("setupItems called for barrioId: '" + this.barrioId + "'");
        if (this.barrioId == null) {
            plugin.getLogger().severe("FATAL: this.barrioId is NULL inside setupItems!");
        }
        this.permissions = loadPermissions();
        setupDecoration(); // Make sure setupDecoration exists or remove if not needed yet

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) { // Added null check for safety
            plugin.getLogger().warning("GUI section is null during item setup for: " + guiName);
            return;
        }
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }
            ConfigurationSection section = guiSection.getConfigurationSection(key);
            if (section != null && section.contains("slots")) {
                ItemStack item = createItem(section);
                // Check permissions map before accessing
                boolean currentPermValue = permissions.getOrDefault(key, getDefaultPermissionValue(key)); // Use helper or default directly
                plugin.debug("Setting up item for key: '" + key + "', current value: " + currentPermValue);
                updateItemStatus(item, currentPermValue);


                List<Integer> slots = section.getIntegerList("slots");
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, item);
                    }
                }
            }
        }
    }
    private void updateItemStatus(ItemStack item, boolean enabled) {
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            List<String> lore = meta.getLore();
            if (lore != null) {
                List<String> newLore = new ArrayList<>();
                for (String line : lore) {
                    newLore.add(line.replace("%status%",
                            enabled ? "§aEnabled" : "§cDisabled"));
                }
                meta.setLore(newLore);
                item.setItemMeta(meta);
            }
        }
    }

    public void handleClick(InventoryClickEvent event) {
        int clickedSlot = event.getSlot();
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(clickedSlot)) {
                // Handle back button click
                player.closeInventory();
                org.bukkit.Bukkit.dispatchCommand(player, "barrio settings");
                return;
            }
        }

        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }
            ConfigurationSection section = guiSection.getConfigurationSection(key);
            if (section != null && section.contains("slots")) {
                List<Integer> slots = section.getIntegerList("slots");
                if (slots.contains(clickedSlot)) {
                    togglePermission(key);
                    break;
                }
            }
        }
    }

    private void togglePermission(String permission) {
        boolean newValue = !permissions.getOrDefault(permission, false);
        permissions.put(permission, newValue);

        // Update memory only - for VISITOR rank only as per requirements
        plugin.getPermissionManager().setPermission(barrioId, permission, me.zivush.barriocore.ranks.Rank.VISITOR, newValue);

        // Refresh GUI
        setupPermissionsItems();
    }
    private boolean getDefaultPermissionValue(String permissionKey) {
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection != null) {
            ConfigurationSection itemSection = guiSection.getConfigurationSection(permissionKey);
            if (itemSection != null) {
                return itemSection.getBoolean("default", false); // Match the default used in PermissionManager
            }
        }
        return false; // Fallback default
    }
}
