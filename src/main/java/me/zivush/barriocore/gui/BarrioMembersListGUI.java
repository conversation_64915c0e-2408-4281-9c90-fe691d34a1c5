package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerdata.BarrioPlayerData;
import me.zivush.barriocore.ranks.Rank;
import me.zivush.barriocore.util.HeadUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class BarrioMembersListGUI extends BaseGUI {
    private final String barrioId;
    private int currentPage = 0;
    private FilterType currentFilter = FilterType.ALL;
    private final Map<Integer, UUID> playerSlots = new HashMap<>();
    private final List<Integer> headSlots = new ArrayList<>();
    private final List<UUID> filteredPlayers = new ArrayList<>();

    public enum FilterType {
        ALL("all"),
        BANNED("banned"),
        TRUSTED("trusted"),
        RESIDENTS("residents"),
        VISITORS("visitors");

        private final String configKey;

        FilterType(String configKey) {
            this.configKey = configKey;
        }

        public String getConfigKey() {
            return configKey;
        }
    }

    public BarrioMembersListGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_members_list");
        this.barrioId = barrioId;

        // Load head slots from config
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section != null) {
            ConfigurationSection headSection = section.getConfigurationSection("player_head");
            if (headSection != null) {
                headSlots.addAll(headSection.getIntegerList("slots"));
            }
        }

        // Load and filter players
        loadPlayers();

        // Setup the GUI
        setupGuiItems();
    }

    @Override
    protected void setupItems() {
        // This is handled by setupGuiItems
    }

    private void loadPlayers() {
        // Clear previous data
        filteredPlayers.clear();

        // Ensure player data is loaded for this barrio
        plugin.getPlayerDataManager().loadPlayerDataForBarrio(barrioId);

        // Get all player UUIDs with data in this barrio
        Map<UUID, BarrioPlayerData> playerDataMap = plugin.getPlayerDataManager().getPlayerDataMap(barrioId);
        if (playerDataMap == null) return;

        // Apply filter
        for (Map.Entry<UUID, BarrioPlayerData> entry : playerDataMap.entrySet()) {
            UUID uuid = entry.getKey();
            BarrioPlayerData playerData = entry.getValue();

            switch (currentFilter) {
                case ALL:
                    filteredPlayers.add(uuid);
                    break;
                case BANNED:
                    if (playerData.isBanned()) {
                        filteredPlayers.add(uuid);
                    }
                    break;
                case TRUSTED:
                    if (playerData.getRank() == Rank.TRUSTED || playerData.getRank() == Rank.OWNER) {
                        filteredPlayers.add(uuid);
                    }
                    break;
                case RESIDENTS:
                    if (playerData.getRank() == Rank.RESIDENT) {
                        filteredPlayers.add(uuid);
                    }
                    break;
                case VISITORS:
                    if (playerData.getRank() == Rank.VISITOR) {
                        filteredPlayers.add(uuid);
                    }
                    break;
            }
        }
    }

    private void setupGuiItems() {
        // Clear inventory and player slots mapping
        inventory.clear();
        playerSlots.clear();

        // Load navigation and filter buttons from config
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section != null) {
            // Load decoration items
            setupDecoration();

            // Set up back button
            ConfigurationSection backButtonSection = section.getConfigurationSection("back_button");
            if (backButtonSection != null) {
                ItemStack backButton = createItem(backButtonSection);
                List<Integer> backSlots = backButtonSection.getIntegerList("slots");
                for (int slot : backSlots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, backButton);
                    }
                }
            }

            // Load navigation buttons
            setupNavigationButtons(section);

            // Load filter button
            setupFilterButton(section);

            // Load player heads
            setupPlayerHeads(section);
        }
    }

    public void setupDecoration() {
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section == null) return;

        ConfigurationSection decorationSection = section.getConfigurationSection("decoration");
        if (decorationSection == null) return;

        for (String key : decorationSection.getKeys(false)) {
            ConfigurationSection itemSection = decorationSection.getConfigurationSection(key);
            if (itemSection != null) {
                ItemStack item = createItem(itemSection);
                for (int slot : itemSection.getIntegerList("slots")) {
                    inventory.setItem(slot, item);
                }
            }
        }
    }

    private void setupNavigationButtons(ConfigurationSection section) {
        // Previous page button
        ConfigurationSection prevSection = section.getConfigurationSection("prev_button");
        if (prevSection != null) {
            ItemStack prevButton = createItem(prevSection);
            ItemMeta meta = prevButton.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.translateAlternateColorCodes('&',
                        prevSection.getString("name", "&6Previous Page")));
                prevButton.setItemMeta(meta);
            }

            // Only show if there are previous pages
            if (currentPage > 0) {
                for (int slot : prevSection.getIntegerList("slots")) {
                    inventory.setItem(slot, prevButton);
                }
            }
        }

        // Next page button
        ConfigurationSection nextSection = section.getConfigurationSection("next_button");
        if (nextSection != null) {
            ItemStack nextButton = createItem(nextSection);
            ItemMeta meta = nextButton.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.translateAlternateColorCodes('&',
                        nextSection.getString("name", "&6Next Page")));
                nextButton.setItemMeta(meta);
            }

            // Only show if there are more pages
            int maxItemsPerPage = headSlots.size();
            if (maxItemsPerPage > 0 && filteredPlayers.size() > (currentPage + 1) * maxItemsPerPage) {
                for (int slot : nextSection.getIntegerList("slots")) {
                    inventory.setItem(slot, nextButton);
                }
            }
        }
    }

    private void setupFilterButton(ConfigurationSection section) {
        ConfigurationSection filterSection = section.getConfigurationSection("filter_button");
        if (filterSection != null) {
            ItemStack filterButton = createItem(filterSection);
            ItemMeta meta = filterButton.getItemMeta();
            if (meta != null) {
                // Get filter display name from config or use default
                String filterDisplayName = filterSection.getString("filter_" + currentFilter.getConfigKey(),
                        getDefaultFilterDisplayName(currentFilter));

                // Replace filter placeholder in name
                String name = filterSection.getString("name", "&aFilter: &f%filter%");
                name = name.replace("%filter%", filterDisplayName);
                meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));

                List<String> lore = new ArrayList<>();
                for (String line : filterSection.getStringList("lore")) {
                    // Replace filter placeholder in lore
                    String processedLine = line.replace("%filter%", filterDisplayName);
                    lore.add(ChatColor.translateAlternateColorCodes('&', processedLine));
                }
                meta.setLore(lore);

                filterButton.setItemMeta(meta);
            }

            for (int slot : filterSection.getIntegerList("slots")) {
                inventory.setItem(slot, filterButton);
            }
        }
    }

    private void setupPlayerHeads(ConfigurationSection section) {
        ConfigurationSection headSection = section.getConfigurationSection("player_head");
        if (headSection == null) return;

        int maxItemsPerPage = headSlots.size();
        if (maxItemsPerPage == 0) return;

        int startIndex = currentPage * maxItemsPerPage;
        int endIndex = Math.min(startIndex + maxItemsPerPage, filteredPlayers.size());

        // Update title with page info
        String title = section.getString("title", "&6Barrio Members &7(Page %page%)");
        title = title.replace("%page%", String.valueOf(currentPage + 1));
        title = ChatColor.translateAlternateColorCodes('&', title);

        // Create a new inventory with the updated title
        inventory = Bukkit.createInventory(player, inventory.getSize(), title);

        // Re-setup decoration and buttons
        setupDecoration();

        // Set up back button
        ConfigurationSection backButtonSection = section.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            ItemStack backButton = createItem(backButtonSection);
            List<Integer> backSlots = backButtonSection.getIntegerList("slots");
            for (int slot : backSlots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, backButton);
                }
            }
        }

        setupNavigationButtons(section);
        setupFilterButton(section);

        // Add player heads
        for (int i = startIndex; i < endIndex; i++) {
            UUID playerUuid = filteredPlayers.get(i);
            int slotIndex = i - startIndex;

            if (slotIndex < headSlots.size()) {
                int slot = headSlots.get(slotIndex);
                ItemStack head = createPlayerHead(playerUuid, headSection);
                inventory.setItem(slot, head);
                playerSlots.put(slot, playerUuid);
            }
        }
    }

    private String getDefaultFilterDisplayName(FilterType filterType) {
        switch (filterType) {
            case ALL:
                return "All Players";
            case BANNED:
                return "Banned Players";
            case TRUSTED:
                return "Trusted Players";
            case RESIDENTS:
                return "Resident Players";
            case VISITORS:
                return "Visitors";
            default:
                return filterType.name();
        }
    }

    public ItemStack createItem(ConfigurationSection section) {
        Material material = Material.valueOf(section.getString("material", "STONE"));
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            String name = section.getString("name", " ");
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));

            List<String> lore = new ArrayList<>();
            for (String line : section.getStringList("lore")) {
                lore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(lore);

            item.setItemMeta(meta);
        }
        return item;
    }

    private ItemStack createPlayerHead(UUID playerUuid, ConfigurationSection section) {
        // Create player head item
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return head;

        // Get the offline player
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerUuid);

        // Get player data for placeholders
        String playerName = targetPlayer.getName() != null ? targetPlayer.getName() : "Unknown";
        Rank playerRank = plugin.getRankManager().getRank(barrioId, playerUuid);
        boolean isOwner = playerRank == Rank.OWNER;

        // Get ban information
        BarrioPlayerData playerData = plugin.getPlayerDataManager().getPlayerData(barrioId, playerUuid);
        boolean isBanned = playerData != null && playerData.isBanned();
        long banDate = playerData != null ? playerData.getBanDate() : 0;

        // Format ban date or use not_banned_text from config
        String notBannedText = section.getString("not_banned_text", "N/A");
        String banDateText = notBannedText;
        if (isBanned && banDate > 0) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            banDateText = dateFormat.format(new Date(banDate));
        }

        // Set name with placeholder
        String name = section.getString("name", "&e%player_name%");
        name = ChatColor.translateAlternateColorCodes('&',
                name.replace("%player_name%", playerName));
        meta.setDisplayName(name);

        // Set lore with placeholders
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            // Replace all placeholders
            line = line.replace("%player_name%", playerName)
                    .replace("%player_rank%", playerRank.name())
                    .replace("%is_owner%", isOwner ? "Yes" : "No")
                    .replace("%player_banned_date%", banDateText);

            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        head.setItemMeta(meta);

        // Use our utility class to properly set the head texture for offline players
        head = HeadUtils.setSkullOwner(head, targetPlayer);

        return head;
    }

    public void handleClick(InventoryClickEvent event) {
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null) return;

        int slot = event.getSlot();

        // Check for back button click
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection != null) {
            ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
            if (backButtonSection != null) {
                List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
                if (backButtonSlots.contains(slot)) {
                    // Handle back button click
                    player.closeInventory();
                    org.bukkit.Bukkit.dispatchCommand(player, "barrio settings");
                    return;
                }
            }
        }

        // Check if a player head was clicked
        if (playerSlots.containsKey(slot)) {
            UUID playerUuid = playerSlots.get(slot);
            OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerUuid);

            // Open the member manage GUI for this player
            player.closeInventory();
            BarrioMemberManageGUI gui = new BarrioMemberManageGUI(plugin, player, targetPlayer, barrioId);
            plugin.setOpenGui(player.getUniqueId(), gui);
            gui.open();
            return;
        }

        // Check if navigation buttons were clicked
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section != null) {
            // Previous page button
            ConfigurationSection prevSection = section.getConfigurationSection("prev_button");
            if (prevSection != null && prevSection.getIntegerList("slots").contains(slot)) {
                if (currentPage > 0) {
                    currentPage--;
                    setupGuiItems();
                    player.openInventory(inventory);
                }
                return;
            }

            // Next page button
            ConfigurationSection nextSection = section.getConfigurationSection("next_button");
            if (nextSection != null && nextSection.getIntegerList("slots").contains(slot)) {
                int maxItemsPerPage = headSlots.size();
                if (maxItemsPerPage > 0 && filteredPlayers.size() > (currentPage + 1) * maxItemsPerPage) {
                    currentPage++;
                    setupGuiItems();
                    player.openInventory(inventory);
                }
                return;
            }

            // Filter button
            ConfigurationSection filterSection = section.getConfigurationSection("filter_button");
            if (filterSection != null && filterSection.getIntegerList("slots").contains(slot)) {
                // Cycle through filter types
                switch (currentFilter) {
                    case ALL:
                        currentFilter = FilterType.BANNED;
                        break;
                    case BANNED:
                        currentFilter = FilterType.TRUSTED;
                        break;
                    case TRUSTED:
                        currentFilter = FilterType.RESIDENTS;
                        break;
                    case RESIDENTS:
                        currentFilter = FilterType.VISITORS;
                        break;
                    case VISITORS:
                        currentFilter = FilterType.ALL;
                        break;
                }

                // Reset to first page and reload
                currentPage = 0;
                loadPlayers();

                setupGuiItems();
                player.openInventory(inventory);
                return;
            }
        }
    }
}
