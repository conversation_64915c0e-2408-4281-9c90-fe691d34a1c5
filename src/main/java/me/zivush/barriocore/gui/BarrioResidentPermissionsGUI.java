package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.ChatColor;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


// This GUI reuses the 'barrio_permissions_gui' layout from gui.yml
// but defaults all toggles to ON visually when opened.
// As per requirements, resident permissions are always true and cannot be changed.
// This GUI is now just for display purposes.
public class BarrioResidentPermissionsGUI extends BaseGUI {
    private final String barrioId;
    // This map holds the *visual state* of the GUI, initialized to all true
    private Map<String, Boolean> guiDisplayPermissions;

    public BarrioResidentPermissionsGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_resident_permissions_gui");
        this.barrioId = barrioId;
        if (barrioId == null) {
            plugin.getLogger().severe("FATAL: barrioId received in ResidentPermissionsGUI constructor is NULL!");
            player.closeInventory(); // Prevent opening broken GUI
            // Optionally throw new IllegalArgumentException("barrioId cannot be null");
            return; // Stop initialization
        }
        plugin.debug("ResidentPermissionsGUI Constructor for barrioId: '" + barrioId + "'");

        // Initialize and set up items with default ON state
        initializeDisplayPermissions();
        setupPermissionsItems();
    }

    // Initialize the visual state map - set all potential permissions to true
    private void initializeDisplayPermissions() {
        this.guiDisplayPermissions = new HashMap<>();
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName); // guiName is "barrio_permissions_gui"
        if (guiSection == null) {
            plugin.getLogger().warning("GUI section is null for: " + guiName + " in ResidentPermissionsGUI");
            return;
        }

        plugin.debug("Initializing Resident GUI display state for " + barrioId);
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }
            ConfigurationSection section = guiSection.getConfigurationSection(key);
            // Check if it looks like a permission toggle (has slots and default is defined usually)
            if (section != null && section.contains("slots") && section.contains("lore")) {
                plugin.debugFine("Setting display default ON for: " + key);
                this.guiDisplayPermissions.put(key, true); // Default visual state is ON
            }
        }
        plugin.debug("Resident GUI display state initialized with keys: " + this.guiDisplayPermissions.keySet());
    }

    // Override setupItems to do nothing, as setup is handled by setupPermissionsItems
    @Override
    protected void setupItems() { }


    // Sets up the visual items based on the guiDisplayPermissions map
    protected void setupPermissionsItems() {
        // Clear previous items before redraw (optional, but safer)
        // inventory.clear(); // Be careful if you have static decorations you want to keep
        setupDecoration(); // Redraw decoration if needed

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) {
            plugin.getLogger().warning("GUI section is null during item setup for: " + guiName + " in ResidentPermissionsGUI");
            return;
        }
        plugin.debug("Setting up ResidentPermissionsGUI items for barrioId: '" + this.barrioId + "' using display map.");


        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }
            ConfigurationSection section = guiSection.getConfigurationSection(key);
            // Ensure it's a permission item we initialized
            if (section != null && section.contains("slots") && this.guiDisplayPermissions.containsKey(key)) {
                ItemStack item = createItem(section); // Create the base item from config

                // Get the *current visual state* from our map
                boolean currentDisplayValue = this.guiDisplayPermissions.getOrDefault(key, true); // Default to true if somehow missing
                plugin.debugFine("Setting up item for key: '" + key + "', display value: " + currentDisplayValue);


                updateItemStatus(item, currentDisplayValue); // Update lore based on visual state

                List<Integer> slots = section.getIntegerList("slots");
                for (int slot : slots) {
                    if (slot >= 0 && slot < inventory.getSize()) {
                        inventory.setItem(slot, item);
                    } else {
                        plugin.getLogger().warning("Invalid slot " + slot + " for item " + key + " in GUI " + guiName);
                    }
                }
            }
        }
    }

    // Updates the %status% placeholder in item lore
    private void updateItemStatus(ItemStack item, boolean enabled) {
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            List<String> lore = meta.getLore();
            if (lore != null) {
                List<String> newLore = new ArrayList<>();
                String statusText = enabled ? plugin.getMessage("messages.gui_status_enabled") : plugin.getMessage("messages.gui_status_disabled");
                // Example messages.yml entries:
                // gui.status_enabled: "&aEnabled"
                // gui.status_disabled: "&cDisabled"
                if (statusText.startsWith("&cMessage not found")) { // Fallback if message missing
                    statusText = enabled ? "§aEnabled" : "§cDisabled";
                }

                for (String line : lore) {
                    newLore.add(line.replace("%status%", statusText));
                }
                meta.setLore(newLore);
            }
            // Optionally update item name too if it uses %status%
            // String name = meta.getDisplayName();
            // if (name != null) {
            //     meta.setDisplayName(name.replace("%status%", statusText));
            // }
            item.setItemMeta(meta);
        } else {
            plugin.getLogger().warning("ItemMeta is null for item: " + item.getType() + " in ResidentPermissionsGUI");
        }
    }

    // Handles clicks within the Resident GUI
    public void handleClick(InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType().isAir()) {
            return;
        }

        int clickedSlot = event.getSlot();
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        plugin.debug("ResidentPermissionsGUI handleClick - Slot: " + clickedSlot);

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(clickedSlot)) {
                // Handle back button click
                player.closeInventory();
                org.bukkit.Bukkit.dispatchCommand(player, "barrio settings");
                return;
            }
        }

        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }
            ConfigurationSection section = guiSection.getConfigurationSection(key);
            // Check if it's a permission item we manage
            if (section != null && section.contains("slots") && this.guiDisplayPermissions.containsKey(key)) {
                List<Integer> slots = section.getIntegerList("slots");
                if (slots.contains(clickedSlot)) {
                    plugin.debugFine("Matched permission key: " + key);
                    togglePermission(key);
                    break; // Found the clicked item, stop searching
                }
            }
        }
    }

    // Toggles the permission's visual state only - no longer saves changes as resident permissions are always true
    private void togglePermission(String permissionKey) {
        // Get the *current visual state* and flip it
        boolean currentDisplayValue = this.guiDisplayPermissions.getOrDefault(permissionKey, true);
        boolean newDisplayValue = !currentDisplayValue;

        plugin.debug("Toggling visual permission '" + permissionKey + "' for barrio '" + barrioId + "' from " + currentDisplayValue + " to " + newDisplayValue);

        // Update the visual state map for the GUI
        this.guiDisplayPermissions.put(permissionKey, newDisplayValue);

        // No longer save to permission manager as resident permissions are always true
        // and cannot be changed as per requirements

        // Refresh the GUI to show the updated status on the item
        setupPermissionsItems();
    }
}