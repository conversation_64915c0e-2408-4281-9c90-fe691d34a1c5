package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.BarrioManager;
import me.zivush.barriocore.ratings.BarrioRatingManager;
import me.zivush.barriocore.util.HeadUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * GUI for rating a barrio.
 */
public class BarrioRatingGUI extends BaseGUI {
    private final String barrioId;
    private BarrioRatingManager ratingManager;
    private int selectedStars = 0;
    private String message = "";
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    /**
     * Constructor for the BarrioRatingGUI.
     *
     * @param plugin The BarrioCore plugin instance
     * @param player The player viewing the GUI
     * @param barrioId The ID of the barrio to rate
     */
    public BarrioRatingGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_rating_gui");
        this.barrioId = barrioId;

        try {
            this.ratingManager = plugin.getRatingManager();

            if (this.ratingManager == null) {
                plugin.getLogger().severe("Rating manager is null in BarrioRatingGUI constructor");
                player.sendMessage(plugin.getMessage("messages.rating.error"));
                player.closeInventory();
                return;
            }

            // Check if the barrio exists before setting up items
            if (plugin.getBarrioManager().getBarrioData(barrioId) == null) {
                plugin.getLogger().warning("Barrio not found in BarrioRatingGUI constructor: " + barrioId);
                player.sendMessage(plugin.getMessage("messages.rating.barrio_not_found"));
                player.closeInventory();
                return;
            }


            setupRatingItems();
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingGUI constructor", e);
            e.printStackTrace();
            player.sendMessage(plugin.getMessage("messages.rating.error"));
            player.closeInventory();
        }
    }

    @Override
    protected void setupItems() {
        // This method is called by BaseGUI constructor, but we need ratingManager to be initialized first
        // So we do nothing here and call setupRatingItems() explicitly after ratingManager is initialized
    }

    /**
     * Sets up the items in the GUI after ratingManager is initialized.
     */
    protected void setupRatingItems() {
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section == null) return;

        // Get barrio info
        BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
        if (barrioData == null) return;

        String nickname = barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;
        UUID ownerUuid = barrioData.getOwnerUuid();
        OfflinePlayer owner = ownerUuid != null ? Bukkit.getOfflinePlayer(ownerUuid) : null;
        String ownerName = owner != null && owner.getName() != null ? owner.getName() : "Unknown";

        // Update title with barrio name
        String title = section.getString("title", "&6Rate &e%barrio_name%");
        title = title.replace("%barrio_name%", nickname)
                .replace("%owner_name%", ownerName);
        title = ChatColor.translateAlternateColorCodes('&', title);

        // Create a new inventory with the updated title
        inventory = Bukkit.createInventory(player, inventory.getSize(), title);

        // Re-setup decoration after creating new inventory
        setupDecoration();

        // Set up back button
        ConfigurationSection backButtonSection = section.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            ItemStack backButton = createItem(backButtonSection);
            List<Integer> backSlots = backButtonSection.getIntegerList("slots");
            for (int slot : backSlots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, backButton);
                }
            }
        }

        // Set barrio head
        ConfigurationSection headSection = section.getConfigurationSection("barrio_head");
        if (headSection != null) {
            ItemStack head = createBarrioHead(barrioData, headSection);
            for (int slot : headSection.getIntegerList("slots")) {
                inventory.setItem(slot, head);
            }
        }

        // Set star items
        ConfigurationSection starSection = section.getConfigurationSection("star");
        if (starSection != null) {
            List<Integer> slots = starSection.getIntegerList("slots");
            plugin.getLogger().info("Setting up " + slots.size() + " star items, selectedStars: " + selectedStars);

            for (int i = 0; i < slots.size(); i++) {
                int starNumber = i + 1;
                boolean selected = starNumber <= selectedStars;
                ItemStack star = createStarItem(starNumber, selected, starSection);
                int slot = slots.get(i);
                inventory.setItem(slot, star);
                plugin.getLogger().info("Set star " + starNumber + " in slot " + slot + ", selected: " + selected);
            }
        }

        // Set message item
        ConfigurationSection messageSection = section.getConfigurationSection("message");
        if (messageSection != null) {
            ItemStack messageItem = createMessageItem(messageSection);
            for (int slot : messageSection.getIntegerList("slots")) {
                inventory.setItem(slot, messageItem);
            }
        }

        // Set submit button
        ConfigurationSection submitSection = section.getConfigurationSection("submit");
        if (submitSection != null) {
            ItemStack submitButton = createSubmitButton(submitSection);
            for (int slot : submitSection.getIntegerList("slots")) {
                inventory.setItem(slot, submitButton);
            }
        }
    }

    /**
     * Creates a player head item for a barrio.
     *
     * @param barrioData The barrio data
     * @param section The configuration section for the head item
     * @return The created ItemStack
     */
    private ItemStack createBarrioHead(BarrioManager.BarrioData barrioData, ConfigurationSection section) {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return head;

        // Get barrio info for placeholders
        UUID ownerUuid = barrioData.getOwnerUuid();
        OfflinePlayer owner = ownerUuid != null ? Bukkit.getOfflinePlayer(ownerUuid) : null;
        String nickname = barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;
        String ownerName = owner != null && owner.getName() != null ? owner.getName() : "Unknown";

        // Get rating info
        double average = ratingManager.getAverageRating(barrioId);
        int count = ratingManager.getRatingCount(barrioId);
        int highest = ratingManager.getHighestRating(barrioId);
        int lowest = ratingManager.getLowestRating(barrioId);

        // Set display name
        String name = section.getString("name", "&6%barrio_name%'s Rating Info");
        name = name.replace("%barrio_name%", nickname)
                .replace("%owner_name%", ownerName);
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            line = line.replace("%barrio_name%", nickname)
                    .replace("%owner_name%", ownerName)
                    .replace("%average%", String.format("%.1f", average))
                    .replace("%count%", String.valueOf(count))
                    .replace("%highest%", String.valueOf(highest))
                    .replace("%lowest%", String.valueOf(lowest));
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        head.setItemMeta(meta);

        // Use our utility class to properly set the head texture for offline players
        if (owner != null) {
            head = HeadUtils.setSkullOwner(head, owner);
        }

        return head;
    }

    /**
     * Creates a star item for rating.
     *
     * @param starNumber The star number (1-5)
     * @param selected Whether the star is selected
     * @param section The configuration section for the star item
     * @return The created ItemStack
     */
    private ItemStack createStarItem(int starNumber, boolean selected, ConfigurationSection section) {
        String materialKey = selected ? "selected_material" : "unselected_material";
        String defaultMaterial = selected ? "GOLD_BLOCK" : "IRON_BLOCK";
        Material material = Material.valueOf(section.getString(materialKey, defaultMaterial));

        plugin.getLogger().info("Creating star item: " + starNumber + ", selected: " + selected + ", material: " + material);

        ItemStack star = new ItemStack(material);
        ItemMeta meta = star.getItemMeta();
        if (meta == null) return star;

        // Set display name
        String name = section.getString("name", "&e%number% Stars");
        name = name.replace("%number%", String.valueOf(starNumber));
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            line = line.replace("%number%", String.valueOf(starNumber));
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        // Set custom model data if specified
        if (section.contains("selected_model_data") && selected) {
            meta.setCustomModelData(section.getInt("selected_model_data"));
        } else if (section.contains("unselected_model_data") && !selected) {
            meta.setCustomModelData(section.getInt("unselected_model_data"));
        }

        star.setItemMeta(meta);
        return star;
    }

    /**
     * Creates a message item for the rating.
     *
     * @param section The configuration section for the message item
     * @return The created ItemStack
     */
    private ItemStack createMessageItem(ConfigurationSection section) {
        Material material = Material.valueOf(section.getString("material", "PAPER"));
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return item;

        // Set display name
        String name = section.getString("name", "&eEdit Message");
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            line = line.replace("%message%", message.isEmpty() ? "No message yet" : message);
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Creates a submit button for the rating.
     *
     * @param section The configuration section for the submit button
     * @return The created ItemStack
     */
    private ItemStack createSubmitButton(ConfigurationSection section) {
        Material material = Material.valueOf(section.getString("material", "EMERALD"));
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return item;

        // Get barrio info
        BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
        String nickname = barrioData != null && barrioData.getNickname() != null ? barrioData.getNickname() : barrioId;

        // Set display name
        String name = section.getString("name", "&aSubmit Rating");
        name = name.replace("%barrio_name%", nickname);
        name = ChatColor.translateAlternateColorCodes('&', name);
        meta.setDisplayName(name);

        // Set lore
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            line = line.replace("%barrio_name%", nickname);
            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        item.setItemMeta(meta);
        return item;
    }

    /**
     * Handles click events in the GUI.
     *
     * @param event The InventoryClickEvent
     */
    public void handleClick(InventoryClickEvent event) {
        try {

            int slot = event.getSlot();
            ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
            if (section == null) {
                plugin.getLogger().warning("GUI config section is null in BarrioRatingGUI.handleClick");
                return;
            }

            // Check for back button click
            ConfigurationSection backButtonSection = section.getConfigurationSection("back_button");
            if (backButtonSection != null) {
                List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
                if (backButtonSlots.contains(slot)) {
                    // Handle back button click - go back to rating info
                    player.closeInventory();
                    BarrioRatingInfoGUI infoGUI = new BarrioRatingInfoGUI(plugin, player, barrioId);
                    plugin.setOpenGui(player.getUniqueId(), infoGUI);
                    infoGUI.open();
                    return;
                }
            }

            // Check if a star was clicked
            ConfigurationSection starSection = section.getConfigurationSection("star");
            if (starSection != null) {
                List<Integer> starSlots = starSection.getIntegerList("slots");
                int starIndex = starSlots.indexOf(slot);
                if (starIndex != -1) {
                    selectedStars = starIndex + 1;
                    plugin.getLogger().info("Star clicked: " + selectedStars + " stars selected");

                    // Update just the star items in the existing inventory
                    updateStarItems();

                    // Force client to update inventory display
                    player.updateInventory();

                    return;
                }
            }

            // Check if message button was clicked
            ConfigurationSection messageSection = section.getConfigurationSection("message");
            if (messageSection != null && messageSection.getIntegerList("slots").contains(slot)) {

                player.closeInventory();
                plugin.openSignEditor(player, lines -> {
                    message = String.join(" ", lines).trim();
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        setupRatingItems();
                        // Re-register this GUI in the plugin's open GUIs map before reopening
                        plugin.setOpenGui(player.getUniqueId(), this);
                        open();
                    });
                });
                return;
            }

            // Check if submit button was clicked
            ConfigurationSection submitSection = section.getConfigurationSection("submit");
            if (submitSection != null && submitSection.getIntegerList("slots").contains(slot)) {

                if (selectedStars == 0) {
                    player.sendMessage(plugin.getMessage("messages.rating.no_stars_selected"));
                    return;
                }

                submitRating();
                return;
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingGUI.handleClick", e);
            e.printStackTrace();
        }
    }

    /**
     * Submits the rating.
     */
    private void submitRating() {
        try {
            // Check if on cooldown
            if (ratingManager.isOnCooldown(barrioId, player.getUniqueId())) {
                long remainingTime = ratingManager.getRemainingCooldown(barrioId, player.getUniqueId());
                String timeStr = formatTimeRemaining(remainingTime);

                player.sendMessage(plugin.getMessage("messages.rating.cooldown")
                        .replace("%time%", timeStr));
                return;
            }

            // Check if player is rating their own barrio
            BarrioManager.BarrioData barrioData = plugin.getBarrioManager().getBarrioData(barrioId);
            if (barrioData != null && barrioData.getOwnerUuid() != null &&
                    barrioData.getOwnerUuid().equals(player.getUniqueId())) {

                player.sendMessage(plugin.getMessage("messages.rating.cannot_rate_own"));
                return;
            }

            // Submit the rating
            boolean success = ratingManager.saveRating(barrioId, player.getUniqueId(), selectedStars, message);
            if (success) {
                String nickname = barrioData != null && barrioData.getNickname() != null ?
                        barrioData.getNickname() : barrioId;

                player.sendMessage(plugin.getMessage("messages.rating.success")
                        .replace("%barrio_name%", nickname)
                        .replace("%stars%", String.valueOf(selectedStars)));
                player.closeInventory();
            } else {
                plugin.getLogger().warning("Failed to save rating in BarrioRatingGUI.submitRating");
                player.sendMessage(plugin.getMessage("messages.rating.error"));
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error in BarrioRatingGUI.submitRating", e);
            e.printStackTrace();
            player.sendMessage(plugin.getMessage("messages.rating.error"));
        }
    }

    /**
     * Formats a time in milliseconds to a human-readable string.
     *
     * @param timeMs The time in milliseconds
     * @return A formatted string (e.g., "2h 30m")
     */
    private String formatTimeRemaining(long timeMs) {
        long seconds = timeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return days + "d " + (hours % 24) + "h";
        } else if (hours > 0) {
            return hours + "h " + (minutes % 60) + "m";
        } else if (minutes > 0) {
            return minutes + "m";
        } else {
            return seconds + "s";
        }
    }

    /**
     * Gets the selected number of stars.
     *
     * @return The selected stars (1-5)
     */
    public int getSelectedStars() {
        return selectedStars;
    }

    /**
     * Gets the rating message.
     *
     * @return The message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Sets the rating message.
     *
     * @param message The message to set
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * Updates only the star items in the inventory without recreating the entire inventory.
     * This is more efficient and provides a smoother user experience.
     */
    private void updateStarItems() {
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section == null) return;

        ConfigurationSection starSection = section.getConfigurationSection("star");
        if (starSection != null) {
            List<Integer> slots = starSection.getIntegerList("slots");
            plugin.getLogger().info("Updating " + slots.size() + " star items, selectedStars: " + selectedStars);

            for (int i = 0; i < slots.size(); i++) {
                int starNumber = i + 1;
                boolean selected = starNumber <= selectedStars;
                ItemStack star = createStarItem(starNumber, selected, starSection);
                int slot = slots.get(i);
                inventory.setItem(slot, star);
                plugin.getLogger().info("Updated star " + starNumber + " in slot " + slot + ", selected: " + selected);
            }
        }
    }
}
