package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.playerdata.BarrioPlayerData;
import me.zivush.barriocore.ranks.Rank;
import me.zivush.barriocore.util.HeadUtils;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BarrioMemberManageGUI extends BaseGUI {
    private final OfflinePlayer targetPlayer;
    private final String barrioId;

    public BarrioMemberManageGUI(BarrioCore plugin, Player player, OfflinePlayer targetPlayer, String barrioId) {
        super(plugin, player, "barrio_member_manage");
        this.targetPlayer = targetPlayer;
        this.barrioId = barrioId;

        // Update the title with the player's name
        updateTitle();

        setupGuiItems();
    }

    /**
     * Updates the inventory title with the player's name
     */
    private void updateTitle() {
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section != null) {
            String title = section.getString("title", "&6Managing: %player%");
            String playerName = targetPlayer.getName() != null ? targetPlayer.getName() : "Unknown";
            title = title.replace("%player%", playerName);
            title = ChatColor.translateAlternateColorCodes('&', title);

            // Create a new inventory with the updated title
            Inventory newInventory = Bukkit.createInventory(player, inventory.getSize(), title);

            // Copy the contents from the old inventory to the new one
            for (int i = 0; i < inventory.getSize(); i++) {
                ItemStack item = inventory.getItem(i);
                if (item != null) {
                    newInventory.setItem(i, item);
                }
            }

            // Replace the old inventory with the new one
            inventory = newInventory;
        }
    }

    @Override
    protected void setupItems() {
    }

    private void setupGuiItems() {
        // Load items from config
        ConfigurationSection section = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (section != null) {
            // Clear the inventory first to avoid duplicates
            inventory.clear();

            // Re-setup decoration after clearing
            setupDecoration();

            for (String key : section.getKeys(false)) {
                if (!key.equals("title") && !key.equals("size")) {
                    ConfigurationSection buttonSection = section.getConfigurationSection(key);
                    if (buttonSection != null) {
                        // Special handling for player head
                        if (key.equals("player_head")) {
                            setupPlayerHead(buttonSection);
                        } else {
                            // Regular items
                            ItemStack item = createItem(buttonSection);
                            for (int slot : buttonSection.getIntegerList("slots")) {
                                inventory.setItem(slot, item);
                            }
                        }
                    }
                }
            }
        }
    }

    private void setupPlayerHead(ConfigurationSection section) {
        // Create player head item
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        if (meta == null) return;

        // Get player data for placeholders
        String playerName = targetPlayer.getName() != null ? targetPlayer.getName() : "Unknown";
        Rank playerRank = plugin.getRankManager().getRank(barrioId, targetPlayer.getUniqueId());
        boolean isOwner = playerRank == Rank.OWNER;

        // Get ban information
        BarrioPlayerData playerData = plugin.getPlayerDataManager().getPlayerData(barrioId, targetPlayer.getUniqueId());
        boolean isBanned = playerData != null && playerData.isBanned();
        long banDate = playerData != null ? playerData.getBanDate() : 0;

        // Format ban date or use not_banned_text from config
        String notBannedText = section.getString("not_banned_text", "N/A");
        String banDateText = notBannedText;
        if (isBanned && banDate > 0) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            banDateText = dateFormat.format(new Date(banDate));
        }

        // Set name with placeholder
        String name = section.getString("name", "&e%player_name%");
        name = ChatColor.translateAlternateColorCodes('&',
                name.replace("%player_name%", playerName));
        meta.setDisplayName(name);

        // Set lore with placeholders
        List<String> lore = new ArrayList<>();
        for (String line : section.getStringList("lore")) {
            // Replace all placeholders
            line = line.replace("%player_name%", playerName)
                    .replace("%player_rank%", playerRank.name())
                    .replace("%is_owner%", isOwner ? "Yes" : "No")
                    .replace("%player_banned_date%", banDateText);

            lore.add(ChatColor.translateAlternateColorCodes('&', line));
        }
        meta.setLore(lore);

        head.setItemMeta(meta);

        // Use our utility class to properly set the head texture for offline players
        head = HeadUtils.setSkullOwner(head, targetPlayer);

        // Place in all configured slots
        for (int slot : section.getIntegerList("slots")) {
            inventory.setItem(slot, head);
        }
    }

    @Override
    public void open() {
        // Make sure the title is updated before opening
        updateTitle();
        super.open();
    }

    public void handleClick(InventoryClickEvent event) {
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null) return;

        int slot = event.getSlot();

        // Check for back button click
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection != null) {
            ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
            if (backButtonSection != null) {
                List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
                if (backButtonSlots.contains(slot)) {
                    // Handle back button click - go back to members list
                    player.closeInventory();
                    BarrioMembersListGUI gui = new BarrioMembersListGUI(plugin, player, barrioId);
                    plugin.setOpenGui(player.getUniqueId(), gui);
                    gui.open();
                    return;
                }
            }
        }

        // Player heads should not be allowed to be taken out of the GUI
        // We'll just process them like other items

        String command = "";
        String targetName = targetPlayer.getName() != null ? targetPlayer.getName() : "Unknown";

        if (targetName.equals("Unknown")) {
            player.sendMessage(plugin.getMessage("messages.player_not_found"));
            player.closeInventory();
            return;
        }

        if (clicked.getType() == Material.GRAY_WOOL) {
            command = "barrio members set " + targetName + " visitor";
        } else if (clicked.getType() == Material.LIME_WOOL) {
            command = "barrio members set " + targetName + " resident";
        } else if (clicked.getType() == Material.LIGHT_BLUE_WOOL) {
            command = "barrio members set " + targetName + " trusted";
        } else if (clicked.getType() == Material.RED_WOOL) {
            // Check if player has moderation_access permission before allowing ban
            if (plugin.getPermissionManager().hasPermission(barrioId, player, "moderation_access")) {
                command = "barrio ban " + targetName;
            } else {
                player.sendMessage(plugin.getMessage("messages.no_moderation_access"));
                player.closeInventory();
                return;
            }
        }

        if (!command.isEmpty()) {
            player.closeInventory();
            player.performCommand(command);
        }
    }
}