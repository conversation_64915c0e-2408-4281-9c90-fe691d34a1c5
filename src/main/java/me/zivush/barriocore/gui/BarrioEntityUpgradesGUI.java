package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.upgrades.BarrioUpgradeSettings;
import me.zivush.barriocore.upgrades.BarrioUpgradeType;
import org.bukkit.ChatColor;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.configuration.ConfigurationSection;

import java.util.ArrayList;
import java.util.List;

/**
 * GUI for managing entity upgrades in a barrio.
 */
public class BarrioEntityUpgradesGUI extends BaseGUI {

    private final String barrioId;

    public BarrioEntityUpgradesGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_entity_upgrades");
        this.barrioId = barrioId;
        setupUpgradeItems();
    }

    @Override
    protected void setupItems() {
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Set up back button
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            ItemStack backButton = createItem(backButtonSection);
            List<Integer> backSlots = backButtonSection.getIntegerList("slots");
            for (int slot : backSlots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, backButton);
                }
            }
        }
    }

    /**
     * Set up the upgrade items in the GUI based on configuration.
     */
    private void setupUpgradeItems() {
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        BarrioUpgradeSettings settings = plugin.getUpgradesManager().getSettings(barrioId);

        // Iterate through configured upgrade items
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue; // Skip meta keys
            }

            ConfigurationSection itemSection = guiSection.getConfigurationSection(key);
            if (itemSection == null) continue;

            // Check if this is an upgrade item (has entity_type and levels configured)
            if (!itemSection.contains("entity_type") || !itemSection.contains("levels")) {
                continue;
            }

            try {
                EntityType entityType = EntityType.valueOf(itemSection.getString("entity_type"));
                ItemStack item = createUpgradeItem(itemSection, entityType, settings);

                List<Integer> slots = itemSection.getIntegerList("slots");
                for (int slot : slots) {
                    inventory.setItem(slot, item);
                }
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid entity type in entity upgrades GUI config: " + itemSection.getString("entity_type"));
            }
        }
    }

    /**
     * Create an upgrade item with current level and cost information.
     */
    private ItemStack createUpgradeItem(ConfigurationSection itemSection, EntityType entityType, BarrioUpgradeSettings settings) {
        ItemStack item = createItem(itemSection);
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return item;

        int currentLevel = settings.getEntityUpgradeLevel(entityType);
        ConfigurationSection levelsSection = itemSection.getConfigurationSection("levels");

        List<String> lore = meta.getLore() != null ? new ArrayList<>(meta.getLore()) : new ArrayList<>();

        // Add current level info
        lore.add("");
        lore.add(ChatColor.YELLOW + "Current Level: " + ChatColor.WHITE + currentLevel);

        if (levelsSection != null) {
            // Find next level info
            String nextLevelKey = String.valueOf(currentLevel + 1);
            if (levelsSection.contains(nextLevelKey)) {
                ConfigurationSection nextLevel = levelsSection.getConfigurationSection(nextLevelKey);
                if (nextLevel != null) {
                    int limit = nextLevel.getInt("limit", 0);
                    double cost = nextLevel.getDouble("cost", 0.0);

                    lore.add(ChatColor.GREEN + "Next Level: " + ChatColor.WHITE + (currentLevel + 1));
                    lore.add(ChatColor.GREEN + "Limit: " + ChatColor.WHITE + limit + " entities");
                    lore.add(ChatColor.GREEN + "Cost: " + ChatColor.WHITE + "$" + cost);
                    lore.add("");
                    lore.add(ChatColor.YELLOW + "Click to upgrade!");
                } else {
                    lore.add(ChatColor.RED + "Max level reached!");
                }
            } else {
                lore.add(ChatColor.RED + "Max level reached!");
            }
        }

        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    public void handleClick(InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null) return;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        int clickedSlot = event.getSlot();

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(clickedSlot)) {
                player.closeInventory();
                // Go back to main upgrades GUI
                BarrioUpgradesGUI upgradesGUI = new BarrioUpgradesGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), upgradesGUI);
                upgradesGUI.open();
                return;
            }
        }

        // Check for upgrade item clicks
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration") || key.equals("back_button")) {
                continue;
            }

            ConfigurationSection itemSection = guiSection.getConfigurationSection(key);
            if (itemSection == null || !itemSection.contains("entity_type") || !itemSection.contains("levels")) {
                continue;
            }

            List<Integer> slots = itemSection.getIntegerList("slots");
            if (slots.contains(clickedSlot)) {
                handleUpgradeClick(itemSection);
                return;
            }
        }
    }

    /**
     * Handle clicking on an upgrade item.
     */
    private void handleUpgradeClick(ConfigurationSection itemSection) {
        try {
            EntityType entityType = EntityType.valueOf(itemSection.getString("entity_type"));
            BarrioUpgradeSettings settings = plugin.getUpgradesManager().getSettings(barrioId);
            int currentLevel = settings.getEntityUpgradeLevel(entityType);

            ConfigurationSection levelsSection = itemSection.getConfigurationSection("levels");
            if (levelsSection == null) return;

            String nextLevelKey = String.valueOf(currentLevel + 1);
            ConfigurationSection nextLevel = levelsSection.getConfigurationSection(nextLevelKey);
            if (nextLevel == null) {
                player.sendMessage(plugin.getMessage("messages.upgrade_max_level"));
                return;
            }

            double cost = nextLevel.getDouble("cost", 0.0);

            // Check if economy is available
            if (!plugin.getUpgradesManager().isEconomyAvailable()) {
                player.sendMessage(plugin.getMessage("messages.economy_not_available"));
                return;
            }

            // Attempt purchase
            boolean success = plugin.getUpgradesManager().purchaseUpgrade(
                player, barrioId, BarrioUpgradeType.ENTITY, entityType.name(), cost
            );

            if (success) {
                player.sendMessage(plugin.getMessage("messages.upgrade_purchased")
                    .replace("%type%", entityType.name().toLowerCase().replace("_", " "))
                    .replace("%level%", String.valueOf(currentLevel + 1))
                    .replace("%cost%", String.valueOf(cost)));

                // Refresh the GUI
                setupUpgradeItems();
            } else {
                player.sendMessage(plugin.getMessage("messages.insufficient_funds")
                    .replace("%cost%", String.valueOf(cost)));
            }

        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid entity type in upgrade click: " + itemSection.getString("entity_type"));
        }
    }

    public String getBarrioId() {
        return barrioId;
    }
}
