package me.zivush.barriocore.modes;

import com.onarandombox.MultiverseCore.MultiverseCore;
import me.zivush.barriocore.BarrioCommand;
import me.zivush.barriocore.BarrioCore;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.OfflinePlayer;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.plugin.RegisteredServiceProvider;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BarrioModeManager {
    private final BarrioCore plugin;
    private Economy economy;

    public BarrioModeManager(BarrioCore plugin) {
        this.plugin = plugin;
        setupEconomy();
        startModeChecks();
    }

    private void setupEconomy() {
        if (Bukkit.getPluginManager().getPlugin("Vault") == null) {
            return;
        }
        RegisteredServiceProvider<Economy> rsp = plugin.getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp != null) {
            economy = rsp.getProvider();
        }
    }



    public void setBarrioMode(String barrioId, BarrioMode mode, String checkTime,
                              String groupCheck, boolean onlineInWorld, double rentPrice) {
        long currentTime = System.currentTimeMillis();
        long nextCheck = currentTime;

        if (checkTime != null) {
            nextCheck = currentTime + parseTime(checkTime);
        }

        plugin.getDatabase().executeUpdate(
                "INSERT INTO barrio_modes (barrio_id, mode, group_check, check_time, online_in_world, rent_price, last_check, next_check) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE " +
                        "mode = ?, group_check = ?, check_time = ?, online_in_world = ?, rent_price = ?, last_check = ?, next_check = ?",
                barrioId, mode.name(), groupCheck, checkTime, onlineInWorld, rentPrice, currentTime, nextCheck,
                mode.name(), groupCheck, checkTime, onlineInWorld, rentPrice, currentTime, nextCheck
        );
    }
    public BarrioMode getBarrioMode(String barrioId) {
        // Check if there's a mode set in the barrio_modes table
        return plugin.getDatabase().executeQuery(
                "SELECT mode FROM barrio_modes WHERE barrio_id = ?",
                rs -> {
                    if (rs.next()) {
                        String modeStr = rs.getString("mode");
                        try {
                            return BarrioMode.valueOf(modeStr);
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("Invalid barrio mode in database: " + modeStr);
                            return BarrioMode.UNLIMITED;
                        }
                    }
                    return BarrioMode.UNLIMITED; // Default mode if not found
                },
                barrioId
        );
    }

    /**
     * Gets the next check time for a barrio.
     *
     * @param barrioId The ID of the barrio
     * @return The next check time in milliseconds, or 0 if not found
     */
    public long getNextCheckTime(String barrioId) {
        return plugin.getDatabase().executeQuery(
                "SELECT next_check FROM barrio_modes WHERE barrio_id = ?",
                rs -> {
                    if (rs.next()) {
                        return rs.getLong("next_check");
                    }
                    return 0L; // Default if not found
                },
                barrioId
        );
    }
    private void startModeChecks() {
        // Run checks every minute
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            checkInactiveBarrios();
            checkRentingBarrios();
        }, 1200L, 1200L); // Every minute (20 ticks * 60 seconds = 1200 ticks)
    }

    private void checkInactiveBarrios() {
        plugin.getDatabase().executeQuery(
                "SELECT bm.*, b.uuid FROM barrio_modes bm " +
                        "JOIN barrios b ON b.id = bm.barrio_id " +
                        "WHERE bm.mode = 'INACTIVITY'",
                rs -> {
                    while (rs.next()) {
                        String barrioId = rs.getString("barrio_id");
                        long nextCheck = rs.getLong("next_check");
                        String checkTime = rs.getString("check_time");

                        if (System.currentTimeMillis() >= nextCheck) {
                            deleteBarrio(barrioId);
                        }
                    }
                    return null;
                }
        );
    }

    private void checkRentingBarrios() {
        plugin.getDatabase().executeQuery(
                "SELECT bm.*, b.uuid FROM barrio_modes bm " +
                        "JOIN barrios b ON b.id = bm.barrio_id " +
                        "WHERE bm.mode = 'RENTING' OR bm.mode = 'RENTING_OFF'",
                rs -> {
                    while (rs.next()) {
                        String barrioId = rs.getString("barrio_id");
                        String ownerUuid = rs.getString("uuid");
                        double rentPrice = rs.getDouble("rent_price");
                        String mode = rs.getString("mode");
                        long nextCheck = rs.getLong("next_check");
                        String checkTime = rs.getString("check_time");

                        if (System.currentTimeMillis() >= nextCheck) {
                            // If mode is RENTING (ON), try to collect rent
                            if (mode.equals("RENTING")) {
                                boolean rentCollected = collectRent(ownerUuid, rentPrice);

                                if (rentCollected) {
                                    // Rent collected successfully, update next check time
                                    long newNextCheck = System.currentTimeMillis() + parseTime(checkTime);
                                    plugin.getDatabase().executeUpdate(
                                            "UPDATE barrio_modes SET last_check = ?, next_check = ? WHERE barrio_id = ?",
                                            System.currentTimeMillis(), newNextCheck, barrioId
                                    );

                                    // Notify player about rent payment
                                    Player owner = Bukkit.getPlayer(UUID.fromString(ownerUuid));
                                    if (owner != null && owner.isOnline()) {
                                        owner.sendMessage(plugin.getMessage("messages.rent_paid").replace("%amount%", String.valueOf(rentPrice)));
                                    }
                                } else {
                                    // Not enough money, delete barrio
                                    // Notify player about barrio deletion
                                    Player owner = Bukkit.getPlayer(UUID.fromString(ownerUuid));
                                    if (owner != null && owner.isOnline()) {
                                        owner.sendMessage(plugin.getMessage("messages.barrio_deleted_no_funds").replace("%amount%", String.valueOf(rentPrice)));
                                    }

                                    deleteBarrio(barrioId);
                                }
                            } else if (mode.equals("RENTING_OFF")) {
                                // If mode is RENTING_OFF (OFF), just delete the barrio when time comes
                                // Notify player about barrio deletion
                                Player owner = Bukkit.getPlayer(UUID.fromString(ownerUuid));
                                if (owner != null && owner.isOnline()) {
                                    owner.sendMessage(plugin.getMessage("messages.barrio_deleted_rent_off"));
                                }

                                deleteBarrio(barrioId);
                            }
                        }
                    }
                    return null;
                }
        );
    }




    public boolean collectRent(String ownerUuid, double amount) {
        if (economy == null) {
            return false;
        }

        OfflinePlayer player = Bukkit.getOfflinePlayer(java.util.UUID.fromString(ownerUuid));
        double balance = economy.getBalance(player);

        if (balance < amount) {
            return false;
        }

        return economy.withdrawPlayer(player, amount).transactionSuccess();
    }

    private void deleteBarrio(String barrioId) {
        // Get server spawn location
        World spawnWorld = Bukkit.getWorld(plugin.getConfig().getString("server_spawn.world", "world"));
        double x = plugin.getConfig().getDouble("server_spawn.x", 0);
        double y = plugin.getConfig().getDouble("server_spawn.y", 100);
        double z = plugin.getConfig().getDouble("server_spawn.z", 0);
        Location serverSpawn = new Location(spawnWorld, x, y, z);

        // Execute world operations on main thread
        Bukkit.getScheduler().runTask(plugin, () -> {
            File barrioDir = new File(Bukkit.getWorldContainer(), "Barrios/" + barrioId);
            MultiverseCore mvc = (MultiverseCore) Bukkit.getPluginManager().getPlugin("Multiverse-Core");

            if (barrioDir.exists()) {
                File[] worlds = barrioDir.listFiles();
                if (worlds != null) {
                    boolean isRegular = Arrays.stream(worlds)
                            .anyMatch(file -> file.getName().equals("world_nether") || file.getName().equals("world_the_end"));

                    if (isRegular) {
                        String[] worldTypes = {"world", "world_nether", "world_the_end"};
                        for (String type : worldTypes) {
                            String worldName = "Barrios/" + barrioId + "/" + type;
                            World world = Bukkit.getWorld(worldName);
                            if (world != null) {
                                world.getPlayers().forEach(player -> player.teleport(serverSpawn));
                                if (mvc != null) {
                                    mvc.getMVWorldManager().deleteWorld(worldName, true, true);
                                }
                            }
                        }
                    } else {
                        for (File worldFile : worlds) {
                            String worldName = "Barrios/" + barrioId + "/" + worldFile.getName();
                            World world = Bukkit.getWorld(worldName);
                            if (world != null) {
                                world.getPlayers().forEach(player -> player.teleport(serverSpawn));
                                if (mvc != null) {
                                    mvc.getMVWorldManager().deleteWorld(worldName, true, true);
                                }
                            }
                        }
                    }
                }

                // Schedule a task to delete the barrio directory after all worlds have been deleted
                // This needs to be done in a separate task to ensure all world deletion operations are complete
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    // Check if the directory still exists
                    if (barrioDir.exists()) {
                        // Check if the directory is empty
                        File[] remainingFiles = barrioDir.listFiles();
                        if (remainingFiles == null || remainingFiles.length == 0) {
                            // Directory is empty, safe to delete
                            if (barrioDir.delete()) {
                                plugin.getLogger().info("Deleted barrio directory: " + barrioDir.getPath());
                            } else {
                                plugin.getLogger().warning("Failed to delete barrio directory: " + barrioDir.getPath());
                            }
                        } else {
                            // Directory still has files, log a warning
                            plugin.getLogger().warning("Barrio directory not empty after world deletion, cannot delete: " + barrioDir.getPath());
                            plugin.getLogger().warning("Remaining files: " + remainingFiles.length);
                            for (File file : remainingFiles) {
                                plugin.getLogger().warning(" - " + file.getName());
                            }
                        }
                    }
                }, 40L); // Wait 2 seconds (40 ticks) to ensure all world deletion operations are complete
            }
        });

        // Clear in-memory data for this barrio
        // This prevents memory leaks and errors when trying to save deleted barrio data
        if (plugin.getRankManager() != null) {
            plugin.getRankManager().forceUnloadBarrioRanks(barrioId, false); // Don't save, we're deleting
        }

        if (plugin.getPlayerDataManager() != null) {
            plugin.getPlayerDataManager().unloadPlayerData(barrioId);
        }

        if (plugin.getGadgetsManager() != null) {
            plugin.getGadgetsManager().unloadSettings(barrioId);
        }

        if (plugin.getPermissionManager() != null) {
            plugin.getPermissionManager().unloadPermissions(barrioId);
        }

        // Delete from all related tables in database
        // Note: Most tables have ON DELETE CASCADE foreign keys, but we'll delete explicitly to be sure
        plugin.getDatabase().executeUpdate("DELETE FROM barrio_modes WHERE barrio_id = ?", barrioId);
        plugin.getDatabase().executeUpdate("DELETE FROM barrio_player_data WHERE barrio_id = ?", barrioId);
        plugin.getDatabase().executeUpdate("DELETE FROM barrio_gadgets WHERE barrio_id = ?", barrioId);
        plugin.getDatabase().executeUpdate("DELETE FROM barrio_permissions WHERE barrio_id = ?", barrioId);

        // Delete from main barrios table last using BarrioManager (this should cascade to other tables as well)
        plugin.getBarrioManager().deleteBarrio(barrioId);
    }

    public long parseTime(String time) {
        Pattern pattern = Pattern.compile("(\\d+)([smhdwMy])");
        Matcher matcher = pattern.matcher(time);

        if (matcher.matches()) {
            long value = Long.parseLong(matcher.group(1));
            String unit = matcher.group(2);
            long result = 0;

            switch (unit) {
                case "s":
                    result = value * 1000; // seconds to milliseconds
                    break;
                case "m":
                    result = value * 60 * 1000; // minutes to milliseconds
                    break;
                case "h":
                    result = value * 60 * 60 * 1000; // hours to milliseconds
                    break;
                case "d":
                    result = value * 24 * 60 * 60 * 1000; // days to milliseconds
                    break;
                case "w":
                    result = value * 7 * 24 * 60 * 60 * 1000; // weeks to milliseconds
                    break;
                case "M":
                    result = value * 30 * 24 * 60 * 60 * 1000; // months (approx) to milliseconds
                    break;
                case "y":
                    result = value * 365 * 24 * 60 * 60 * 1000; // years (approx) to milliseconds
                    break;
                default:
                    result = 0;
            }

            return result;
        }

        return 0;
    }
}
