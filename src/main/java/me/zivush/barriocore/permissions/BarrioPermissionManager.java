package me.zivush.barriocore.permissions;

import me.zivush.barriocore.BarrioCommand;
import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.ranks.BarrioRankManager;
import me.zivush.barriocore.ranks.Rank;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

public class BarrioPermissionManager {
    private final BarrioCore plugin;
    // Structure: barrioId -> permission -> rank -> value
    private final Map<String, Map<String, Map<Rank, Boolean>>> barrioPermissions = new ConcurrentHashMap<>();
    private BukkitTask saveTask;
    private final BarrioRankManager rankManager;


    public BarrioPermissionManager(BarrioCore plugin) {
        this.plugin = plugin;
        loadAllPermissions();
        this.rankManager = plugin.getRankManager();
        if (this.rankManager == null) {
            plugin.getLogger().severe("RankManager is null during BarrioPermissionManager initialization! This will cause permission issues.");
        }

        // Migrate new permissions for existing barrios
        migrateCustomCropsPermission();

        // Check for orphaned Barrio files on server startup
        checkOrphanedBarrioFiles();
    }

    public void loadAllPermissions() {
        plugin.getLogger().info("Starting to load all barrio permissions...");
        plugin.getDatabase().executeQuery(
                "SELECT * FROM barrio_permissions",
                rs -> {
                    int count = 0;
                    while (rs.next()) {
                        String barrioId = rs.getString("barrio_id");
                        String permission = rs.getString("permission");
                        boolean valueVisitor = rs.getBoolean("value_visitor");
                        boolean valueResident = rs.getBoolean("value_resident");
                        boolean valueTrusted = rs.getBoolean("value_trusted");

                        // Get or create the permissions map for this barrio
                        Map<String, Map<Rank, Boolean>> barrioPerms = barrioPermissions.computeIfAbsent(barrioId, k -> new ConcurrentHashMap<>());

                        // Get or create the rank map for this permission
                        Map<Rank, Boolean> permRanks = barrioPerms.computeIfAbsent(permission, k -> new ConcurrentHashMap<>());

                        // Store values for each rank
                        permRanks.put(Rank.VISITOR, valueVisitor);
                        permRanks.put(Rank.RESIDENT, valueResident);
                        permRanks.put(Rank.TRUSTED, valueTrusted);

                        count++;
                    }
                    plugin.getLogger().info("Finished loading " + count + " permissions for " + barrioPermissions.size() + " barrios");
                    return null;
                }
        );
    }

    public boolean hasPermission(String barrioId, Player player, String permission) {
        if (barrioId == null || permission == null) {
            plugin.getLogger().warning("Null check failed - barrioId: " + barrioId + ", permission: " + permission);
            return false;
        }

        // Check for admin bypass permission
        if (player.hasPermission("barrio.admin.bypass")) {
            plugin.debug("Player " + player.getName() + " bypassed permission check for " + permission + " in barrio " + barrioId + " with admin bypass");
            return true;
        }

        // Get the player's rank for this barrio
        Rank playerRank = rankManager.getRank(barrioId, player.getUniqueId());

        // Owner always has all permissions
        if (playerRank == Rank.OWNER) {
            return true;
        }

        // Get the permissions map for this barrio
        Map<String, Map<Rank, Boolean>> barrioPerms = barrioPermissions.get(barrioId);
        if (barrioPerms == null) {
            plugin.getLogger().info("No permissions found for barrio: " + barrioId);
            return getDefaultPermission(permission, playerRank);
        }

        // Get the rank map for this permission
        Map<Rank, Boolean> permRanks = barrioPerms.get(permission);
        if (permRanks == null) {
            plugin.getLogger().info("No rank permissions found for permission: " + permission + " in barrio: " + barrioId);
            return getDefaultPermission(permission, playerRank);
        }

        // Get the permission value for this rank
        Boolean value = permRanks.get(playerRank);
        if (value == null) {
            plugin.getLogger().info("No permission value found for rank: " + playerRank + ", permission: " + permission + " in barrio: " + barrioId);
            return getDefaultPermission(permission, playerRank);
        }

        return value;
    }


    public boolean getDefaultPermission(String permission, Rank rank) {
        // Special permissions that only apply to trusted players
        if (permission.equals("moderation_access") || permission.equals("settings_access")) {
            // These permissions are only for trusted players, null for others
            if (rank == Rank.TRUSTED) {
                return plugin.getGuiConfig().getBoolean("barrio_trusted_permissions_gui." + permission + ".default", true);
            } else {
                // Visitors and residents don't have these permissions
                return false;
            }
        }

        // For RESIDENT and TRUSTED ranks, default is always true for standard permissions
        if (rank == Rank.RESIDENT || rank == Rank.TRUSTED) {
            return true;
        }

        // For VISITOR rank, get the default from config
        return plugin.getGuiConfig().getBoolean("barrio_permissions_gui." + permission + ".default", false);
    }

    // Keep the old method for backward compatibility
    public boolean getDefaultPermission(String permission) {
        return getDefaultPermission(permission, Rank.VISITOR);
    }

    public void setPermission(String barrioId, String permission, Rank rank, boolean value) {
        // Get or create the permissions map for this barrio
        Map<String, Map<Rank, Boolean>> barrioPerms = barrioPermissions.computeIfAbsent(barrioId, k -> new ConcurrentHashMap<>());

        // Get or create the rank map for this permission
        Map<Rank, Boolean> permRanks = barrioPerms.computeIfAbsent(permission, k -> new ConcurrentHashMap<>());

        // Set the value for this rank
        permRanks.put(rank, value);
    }

    // For backward compatibility - sets permission for VISITOR rank only
    public void setPermission(String barrioId, String permission, boolean value) {
        setPermission(barrioId, permission, Rank.VISITOR, value);
    }
    public void startPeriodicSaving() {
        int interval = plugin.getConfig().getInt("permissions.save_interval", 300) * 20; // Convert to ticks
        saveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::saveAllPermissions, interval, interval);
    }

    public void saveAllPermissions() {
        plugin.getLogger().info("Saving all barrio permissions to database...");
        for (Map.Entry<String, Map<String, Map<Rank, Boolean>>> barrioEntry : barrioPermissions.entrySet()) {
            String barrioId = barrioEntry.getKey();

            // Check if barrio exists before saving permissions
            boolean barrioExists = plugin.getDatabase().executeQuery(
                "SELECT COUNT(*) FROM barrios WHERE id = ?",
                rs -> rs.next() && rs.getInt(1) > 0,
                barrioId
            );
            if (!barrioExists) {
                plugin.getLogger().warning("Skipping permissions save for non-existent barrio: " + barrioId);
                continue;
            }

            for (Map.Entry<String, Map<Rank, Boolean>> permEntry : barrioEntry.getValue().entrySet()) {
                String permission = permEntry.getKey();
                Map<Rank, Boolean> rankValues = permEntry.getValue();

                // Get values for each rank, defaulting to appropriate values if not set
                boolean visitorValue = rankValues.getOrDefault(Rank.VISITOR, getDefaultPermission(permission, Rank.VISITOR));
                boolean residentValue = rankValues.getOrDefault(Rank.RESIDENT, true); // Default true for residents
                boolean trustedValue = rankValues.getOrDefault(Rank.TRUSTED, true);  // Default true for trusted

                plugin.getDatabase().executeUpdate(
                        "INSERT INTO barrio_permissions (barrio_id, permission, value_visitor, value_resident, value_trusted) " +
                        "VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE " +
                        "value_visitor = ?, value_resident = ?, value_trusted = ?",
                        barrioId, permission, visitorValue, residentValue, trustedValue,
                        visitorValue, residentValue, trustedValue
                );
            }
        }
        plugin.getLogger().info("Finished saving permissions to database");
    }

    public boolean isPlayerRankTrustedOrHigher(String barrioId, UUID playerUuid) {
        // Basic validation
        if (barrioId == null || playerUuid == null) {
            plugin.getLogger().warning("isPlayerRankTrustedOrHigher called with null barrioId or playerUuid.");
            return false;
        }
        // Ensure rankManager is available
        if (rankManager == null) {
            plugin.getLogger().severe("isPlayerRankTrustedOrHigher: RankManager is null!");
            return false; // Fail safe
        }

        // Get rank from the manager and check its level
        try {
            Rank rank = rankManager.getRank(barrioId, playerUuid);
            // Use the helper method within the Rank enum
            return rank.isAtLeast(Rank.TRUSTED);
        } catch (Exception e) {
            // Log potential errors from rankManager.getRank if any can occur
            plugin.getLogger().log(java.util.logging.Level.WARNING, "Error getting rank in isPlayerRankTrustedOrHigher for " + playerUuid + " in barrio " + barrioId, e);
            return false; // Fail safe on error
        }
    }

    public void stopPeriodicSaving() {
        if (saveTask != null) {
            saveTask.cancel();
        }
        saveAllPermissions(); // Final save on disable
    }

    /**
     * Unloads permissions for a barrio from memory.
     * Used when deleting a barrio.
     *
     * @param barrioId The ID of the barrio
     */
    public void unloadPermissions(String barrioId) {
        if (barrioId == null) return;
        barrioPermissions.remove(barrioId);
    }
    /**
     * Gets a simplified view of permissions for backward compatibility.
     * This returns only the VISITOR permissions for each barrio.
     *
     * @return A map of barrioId -> permission -> value (for VISITOR rank)
     */
    public Map<String, Map<String, Boolean>> getBarrioPermissions() {
        Map<String, Map<String, Boolean>> result = new ConcurrentHashMap<>();

        for (Map.Entry<String, Map<String, Map<Rank, Boolean>>> barrioEntry : barrioPermissions.entrySet()) {
            String barrioId = barrioEntry.getKey();
            Map<String, Boolean> visitorPerms = new ConcurrentHashMap<>();

            for (Map.Entry<String, Map<Rank, Boolean>> permEntry : barrioEntry.getValue().entrySet()) {
                String permission = permEntry.getKey();
                Map<Rank, Boolean> rankValues = permEntry.getValue();

                // Get the visitor value, defaulting if not set
                boolean visitorValue = rankValues.getOrDefault(Rank.VISITOR, getDefaultPermission(permission, Rank.VISITOR));
                visitorPerms.put(permission, visitorValue);
            }

            result.put(barrioId, visitorPerms);
        }

        return result;
    }

    /**
     * Gets the full permissions map including all ranks.
     *
     * @return The complete permissions map
     */
    public Map<String, Map<String, Map<Rank, Boolean>>> getFullBarrioPermissions() {
        return barrioPermissions;
    }

    /**
     * Gets all permissions for a specific barrio, including all ranks.
     *
     * @param barrioId The ID of the barrio
     * @return A map of permission -> rank -> value, or null if the barrio doesn't exist
     */
    public Map<String, Map<Rank, Boolean>> getBarrioPermissionsForAllRanks(String barrioId) {
        if (barrioId == null) return null;
        return barrioPermissions.get(barrioId);
    }

    /**
     * Checks for Barrio directories on the server that don't have corresponding entries in the database.
     * If any are found, they are deleted.
     */
    private void checkOrphanedBarrioFiles() {
        plugin.getLogger().info("Checking for orphaned Barrio files on server...");

        // Get all Barrio IDs from the database
        Set<String> databaseBarrioIds = new HashSet<>();
        try {
            plugin.getDatabase().executeQuery(
                "SELECT id FROM barrios",
                rs -> {
                    while (rs.next()) {
                        databaseBarrioIds.add(rs.getString("id"));
                    }
                    return null;
                }
            );
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error retrieving Barrio IDs from database", e);
            return;
        }

        plugin.getLogger().info("Found " + databaseBarrioIds.size() + " Barrios in database");

        // Get all Barrio directories from the server
        File barriosDir = new File(Bukkit.getWorldContainer(), "Barrios");
        if (!barriosDir.exists() || !barriosDir.isDirectory()) {
            plugin.getLogger().info("No Barrios directory found on server");
            return;
        }

        File[] barrioDirs = barriosDir.listFiles();
        if (barrioDirs == null || barrioDirs.length == 0) {
            plugin.getLogger().info("No Barrio directories found on server");
            return;
        }

        plugin.getLogger().info("Found " + barrioDirs.length + " Barrio directories on server");

        // Find Barrio directories that don't have corresponding database entries
        List<String> orphanedBarrioIds = new ArrayList<>();
        for (File barrioDir : barrioDirs) {
            if (barrioDir.isDirectory()) {
                String barrioId = barrioDir.getName();
                if (!databaseBarrioIds.contains(barrioId)) {
                    orphanedBarrioIds.add(barrioId);
                }
            }
        }

        if (orphanedBarrioIds.isEmpty()) {
            plugin.getLogger().info("No orphaned Barrio files found on server");
            return;
        }

        plugin.getLogger().warning("Found " + orphanedBarrioIds.size() + " orphaned Barrio directories: " + String.join(", ", orphanedBarrioIds));

        // Delete orphaned Barrio directories
        for (String barrioId : orphanedBarrioIds) {
            deleteOrphanedBarrio(barrioId);
        }

        plugin.getLogger().info("Finished checking for orphaned Barrio files");
    }

    /**
     * Deletes an orphaned Barrio directory from the server.
     *
     * @param barrioId The ID of the orphaned Barrio
     */
    private void deleteOrphanedBarrio(String barrioId) {
        plugin.getLogger().warning("Deleting orphaned Barrio directory: " + barrioId);

        // Get server spawn location for teleporting players
        World spawnWorld = Bukkit.getWorld(plugin.getConfig().getString("server_spawn.world", "world"));
        double x = plugin.getConfig().getDouble("server_spawn.x", 0);
        double y = plugin.getConfig().getDouble("server_spawn.y", 100);
        double z = plugin.getConfig().getDouble("server_spawn.z", 0);
        Location serverSpawn = new Location(spawnWorld, x, y, z);

        File barrioDir = new File(Bukkit.getWorldContainer(), "Barrios/" + barrioId);
        Plugin mvPlugin = Bukkit.getPluginManager().getPlugin("Multiverse-Core");

        if (mvPlugin == null) {
            plugin.getLogger().severe("Multiverse-Core plugin not found! Cannot delete orphaned Barrio worlds.");
            return;
        }

        // Use reflection to avoid direct dependency on Multiverse-Core
        try {
            Object mvc = mvPlugin;
            Object worldManager = mvc.getClass().getMethod("getMVWorldManager").invoke(mvc);

            if (barrioDir.exists()) {
                File[] worlds = barrioDir.listFiles();
                if (worlds != null) {
                    // Check if it's a regular barrio (has multiple worlds) or template (single world)
                    boolean isRegular = Arrays.stream(worlds)
                            .anyMatch(file -> file.getName().equals("world_nether") || file.getName().equals("world_the_end"));

                    if (isRegular) {
                        // Handle regular barrio (3 worlds)
                        String[] worldTypes = {"world", "world_nether", "world_the_end"};
                        for (String type : worldTypes) {
                            String worldName = "Barrios/" + barrioId + "/" + type;
                            World world = Bukkit.getWorld(worldName);
                            if (world != null) {
                                // Teleport any players in this world to server spawn
                                world.getPlayers().forEach(player -> player.teleport(serverSpawn));

                                // Delete the world using Multiverse
                                worldManager.getClass().getMethod("deleteWorld", String.class, boolean.class, boolean.class)
                                        .invoke(worldManager, worldName, true, true);
                            }
                        }
                    } else {
                        // Handle template barrio (single world)
                        for (File worldFile : worlds) {
                            if (worldFile.isDirectory()) {
                                String worldName = "Barrios/" + barrioId + "/" + worldFile.getName();
                                World world = Bukkit.getWorld(worldName);
                                if (world != null) {
                                    // Teleport any players in this world to server spawn
                                    world.getPlayers().forEach(player -> player.teleport(serverSpawn));

                                    // Delete the world using Multiverse
                                    worldManager.getClass().getMethod("deleteWorld", String.class, boolean.class, boolean.class)
                                            .invoke(worldManager, worldName, true, true);
                                }
                            }
                        }
                    }
                }

                // Delete the barrio directory after all worlds have been deleted
                // This is needed because Multiverse-Core only deletes the world directories, not the parent barrio directory
                if (barrioDir.exists()) {
                    // Check if the directory is empty
                    File[] remainingFiles = barrioDir.listFiles();
                    if (remainingFiles == null || remainingFiles.length == 0) {
                        // Directory is empty, safe to delete
                        if (barrioDir.delete()) {
                            plugin.getLogger().info("Deleted orphaned barrio directory: " + barrioDir.getPath());
                        } else {
                            plugin.getLogger().warning("Failed to delete orphaned barrio directory: " + barrioDir.getPath());
                        }
                    } else {
                        // Directory still has files, log a warning
                        plugin.getLogger().warning("Orphaned barrio directory not empty after world deletion, cannot delete: " + barrioDir.getPath());
                        plugin.getLogger().warning("Remaining files: " + remainingFiles.length);
                        for (File file : remainingFiles) {
                            plugin.getLogger().warning(" - " + file.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error deleting orphaned Barrio worlds for " + barrioId, e);
        }
    }
}
