<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>me.zivush</groupId>
  <artifactId>BarrioCore</artifactId>
  <name>BarrioCore</name>
  <version>1.0</version>
  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>21</source>
          <target>21</target>
          <compilerArgs>--enable-preview</compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>OnARandomBox</id>
      <url>https://repo.onarandombox.com/content/groups/public/</url>
    </repository>
    <repository>
      <id>jitpack.io</id>
      <url>https://jitpack.io</url>
    </repository>
    <repository>
      <id>placeholderapi</id>
      <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.21-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.onarandombox.multiversecore</groupId>
      <artifactId>Multiverse-Core</artifactId>
      <version>4.3.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.MilkBowl</groupId>
      <artifactId>VaultAPI</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.mojang</groupId>
      <artifactId>authlib</artifactId>
      <version>1.5.21</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.6</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
